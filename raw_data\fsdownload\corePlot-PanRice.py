# -*- coding: utf-8 -*-
"""
Created on Thu Apr  6 20:48:39 2023

@author: yuz0a
"""

import os
import re
from matplotlib import pyplot as plt
import matplotlib.patches as patches
import sys, getopt
try:
   opts,args=getopt.getopt(sys.argv[1:],"hc:o:a:")
except getopt.GetoptError:
   sys.exit(2)
for option,value in opts:
   if option in ('-h'):
       sys.exit()
   elif option in ('-c'):
       chrr=value
   elif option in ('-o'):
       out=value
   elif option in ('-a'):
       accessionlist=value
#chrr='Chr01'
#accn='164'
#out = 'C:/Users/<USER>/Desktop/'+chrr+'50.all.coords'
#os.chdir("C:/Users/<USER>/Desktop/coreR/")


chrd = {}
with open('MH63RS3.cento') as f:
    for i in f:
        if i.startswith('Chr\t') == False:
            ii = re.split('\t', i.strip())
            chrd[ii[0]] = [int(ii[1]),int(ii[2]), int(ii[3]), int(ii[4])]
f.close()

acc = []
xin = 0
oryzan = 0
with open(accessionlist) as f:
    for i in f:
        ii = re.split('\t', i.strip())
        acc.append(ii)
        if ii[1] == 'xi':
            xin += 1
        if ii[1] != 'w':
            oryzan += 1
f.close()
accn = len(acc)
# fig,ax=plt.subplots(12,1,sharex=True, sharey=True,figsize=(82.7, 117))

i=chrr
print(i)
fig,ax=plt.subplots(1,1,sharex=True, sharey=True,figsize=(82.7, 117))
ax.set_ylabel(i,fontsize=60)
#ax[chro].xaxis.set_major_formatter(FormatStrFormatter('%1.2f Mbp'))
#ax[chro].xaxis.set_tick_params(labelsize=40,labelbottom=True)
#ax[chro].yaxis.set_tick_params(labelsize=40)
#Chromosome
ax.add_patch(patches.Rectangle((chrd[i][0]/50000000, 0), (chrd[i][1]-chrd[i][0])/50000000, 1, color = '#D1E8E4'))
#Centromere
ax.add_patch(patches.Rectangle((chrd[i][2]/50000000, 0), (chrd[i][3]-chrd[i][2])/50000000, 1, color = '#FEC868'))
m = 0
for naml in acc:
    nam = naml[0]
    alignd = {}
    print(nam)
    with open('MH63_RP_'+nam+'.filtered.coords.50.filtered.1227') as f:
        for a in f:
            aa = re.split(r'\t', a.strip())
            if aa[0] in alignd:
                alignd[aa[0]].append([int(aa[1]), int(aa[2])])
            else:
                alignd[aa[0]] = [[int(aa[1]), int(aa[2])]]
    f.close()
    if naml[1] == 'xi':
        tc = '#FF6B6B'
    elif naml[1] == 'gj':
        tc = '#4D96FF'
    elif naml[1] == 'ca':
        tc = '#FFD93D'
    elif naml[1] == 'cb':
        tc = '#6BCB77'
    for j in alignd[i]:
        ax.text(chrd[i][1]/50000000, m/int(accn), nam, fontsize=40, color=tc)
        if m == 0:
            ax.add_patch(patches.Rectangle((j[0]/50000000, m/int(accn)), (j[1]-j[0])/50000000, 1/int(accn), color = '#ADE792'))
        else:
            ax.add_patch(patches.Rectangle((j[0]/50000000, m/int(accn)), (j[1]-j[0])/50000000, 1/int(accn), color = '#FF6D60'))
    m += 1


for n in range(1,int(accn)):
    alignd = {}
    with open(i+'.50.R'+str(n)+'_RP_qryRecord.coords') as f:
        for a in f:
            aa = re.split(r'\t', a.strip())
            if aa[0] in alignd:
                alignd[aa[0]].append([int(aa[1]), int(aa[2])])
            else:
                alignd[aa[0]] = [[int(aa[1]), int(aa[2])]]
    f.close()
    if n < int(xin):
        for j in alignd[i]:
            ax.add_patch(patches.Rectangle((j[0]/50000000, n/int(accn)), (j[1]-j[0])/50000000, 1/int(accn), color = '#ADE792'))
    elif n < int(oryzan):
        for j in alignd[i]:
            ax.add_patch(patches.Rectangle((j[0]/50000000, n/int(accn)), (j[1]-j[0])/50000000, 1/int(accn), color = '#1C6758'))
    else:
        for j in alignd[i]:
            ax.add_patch(patches.Rectangle((j[0]/50000000, n/int(accn)), (j[1]-j[0])/50000000, 1/int(accn), color = '#000000'))
    # for j in xalignd[i]:
    #     ax.add_patch(patches.Rectangle((j[0]/50000000, 0.5), (j[1]-j[0])/50000000, 0.4, color = '#62CDFF'))
    
ax.spines['left'].set_visible(False)
ax.spines['right'].set_visible(False)
ax.spines['top'].set_visible(False)
ax.spines['bottom'].set_visible(False)

fig.savefig(out+'.'+i+'.jpg', dpi=300)


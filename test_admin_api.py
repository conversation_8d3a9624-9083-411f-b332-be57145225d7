#!/usr/bin/env python3
"""
测试管理后台数据管理API的脚本
"""

import requests
import json

# API基础URL
BASE_URL = "http://localhost:8000/admin"

def test_data_list():
    """测试数据列表API"""
    print("测试数据列表API...")
    
    url = f"{BASE_URL}/data-management/list/"
    params = {
        'page': 1,
        'page_size': 5
    }
    
    try:
        response = requests.get(url, params=params)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            if data.get('success'):
                print(f"✅ 成功获取数据，共 {data.get('total', 0)} 条记录")
                
                # 检查第一条记录的文件状态
                if data.get('data') and len(data['data']) > 0:
                    first_record = data['data'][0]
                    print(f"第一条记录: {first_record['accession']}")
                    
                    file_status = []
                    for key, value in first_record.items():
                        if key.startswith('has') and value:
                            file_status.append(key.replace('has', ''))
                    
                    print(f"文件状态: {', '.join(file_status) if file_status else '无文件'}")
                else:
                    print("⚠️ 没有返回数据记录")
            else:
                print(f"❌ API返回失败: {data.get('message', '未知错误')}")
        else:
            print(f"❌ HTTP请求失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")

def test_specific_accession():
    """测试特定accession的文件状态"""
    print("\n测试特定accession (IR64) 的文件状态...")
    
    url = f"{BASE_URL}/data-management/list/"
    params = {
        'page': 1,
        'page_size': 100,
        'search': 'IR64'
    }
    
    try:
        response = requests.get(url, params=params)
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('success') and data.get('data'):
                for record in data['data']:
                    if record['accession'] == 'IR64':
                        print(f"找到 IR64 记录:")
                        print(f"  SubPopulation: {record.get('subPopulation', 'N/A')}")
                        print(f"  SeqData: {record.get('seqData', 'N/A')}")
                        print(f"  经纬度: ({record.get('longitude', 'N/A')}, {record.get('latitude', 'N/A')})")
                        
                        file_checks = {
                            'hasGenome': 'Genome',
                            'hasAnnotation': 'Annotation', 
                            'hasTranscriptome': 'Transcriptome',
                            'hasCodon': 'Codon',
                            'hasCentromere': 'Centromere',
                            'hasTEs': 'TEs',
                            'hasCoreBlocks': 'CoreBlocks',
                            'hasmiRNA': 'miRNA',
                            'hastRNA': 'tRNA',
                            'hasrRNA': 'rRNA'
                        }
                        
                        print("  文件状态:")
                        for key, label in file_checks.items():
                            status = "✅" if record.get(key, False) else "❌"
                            print(f"    {label}: {status}")
                        
                        return
                
                print("❌ 未找到 IR64 记录")
            else:
                print(f"❌ 搜索失败: {data.get('message', '未知错误')}")
        else:
            print(f"❌ HTTP请求失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")

if __name__ == "__main__":
    print("=== 管理后台数据管理API测试 ===")
    test_data_list()
    test_specific_accession()
    print("\n=== 测试完成 ===")

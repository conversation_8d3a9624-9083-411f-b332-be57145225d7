{"ast": null, "code": "import { createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, resolveComponent as _resolveComponent, createBlock as _createBlock, withCtx as _withCtx, createVNode as _createVNode, createTextVNode as _createTextVNode, resolveDirective as _resolveDirective, withDirectives as _withDirectives } from \"vue\";\nconst _hoisted_1 = {\n  class: \"admin-data-manager\"\n};\nconst _hoisted_2 = {\n  class: \"page-header\"\n};\nconst _hoisted_3 = {\n  class: \"header-stats\"\n};\nconst _hoisted_4 = {\n  key: 0\n};\nconst _hoisted_5 = {\n  key: 1,\n  class: \"selected-info\"\n};\nconst _hoisted_6 = {\n  class: \"filter-section\"\n};\nconst _hoisted_7 = {\n  class: \"filter-row\"\n};\nconst _hoisted_8 = {\n  class: \"filter-item\"\n};\nconst _hoisted_9 = {\n  class: \"filter-item\"\n};\nconst _hoisted_10 = {\n  class: \"filter-item\"\n};\nconst _hoisted_11 = [\"href\"];\nconst _hoisted_12 = {\n  key: 1,\n  class: \"data-empty\"\n};\nconst _hoisted_13 = [\"onClick\", \"title\"];\nconst _hoisted_14 = {\n  key: 1,\n  class: \"file-empty\"\n};\nconst _hoisted_15 = [\"onClick\", \"title\"];\nconst _hoisted_16 = {\n  key: 1,\n  class: \"file-empty\"\n};\nconst _hoisted_17 = [\"onClick\", \"title\"];\nconst _hoisted_18 = {\n  key: 1,\n  class: \"file-empty\"\n};\nconst _hoisted_19 = [\"onClick\", \"title\"];\nconst _hoisted_20 = {\n  key: 1,\n  class: \"file-empty\"\n};\nconst _hoisted_21 = [\"onClick\", \"title\"];\nconst _hoisted_22 = {\n  key: 1,\n  class: \"file-empty\"\n};\nconst _hoisted_23 = [\"onClick\", \"title\"];\nconst _hoisted_24 = {\n  key: 1,\n  class: \"file-empty\"\n};\nconst _hoisted_25 = [\"onClick\", \"title\"];\nconst _hoisted_26 = {\n  key: 1,\n  class: \"file-empty\"\n};\nconst _hoisted_27 = [\"onClick\", \"title\"];\nconst _hoisted_28 = {\n  key: 1,\n  class: \"file-empty\"\n};\nconst _hoisted_29 = [\"onClick\", \"title\"];\nconst _hoisted_30 = {\n  key: 1,\n  class: \"file-empty\"\n};\nconst _hoisted_31 = [\"onClick\", \"title\"];\nconst _hoisted_32 = {\n  key: 1,\n  class: \"file-empty\"\n};\nconst _hoisted_33 = [\"onClick\", \"title\"];\nconst _hoisted_34 = {\n  key: 1,\n  class: \"file-empty\"\n};\nconst _hoisted_35 = [\"onClick\", \"title\"];\nconst _hoisted_36 = {\n  key: 1,\n  class: \"file-empty\"\n};\nconst _hoisted_37 = [\"onClick\", \"title\"];\nconst _hoisted_38 = {\n  key: 1,\n  class: \"file-empty\"\n};\nconst _hoisted_39 = [\"onClick\", \"title\"];\nconst _hoisted_40 = {\n  key: 1,\n  class: \"file-empty\"\n};\nconst _hoisted_41 = [\"onClick\", \"title\"];\nconst _hoisted_42 = {\n  key: 1,\n  class: \"file-empty\"\n};\nconst _hoisted_43 = {\n  class: \"pagination-container\"\n};\nconst _hoisted_44 = {\n  class: \"file-management-grid\"\n};\nconst _hoisted_45 = {\n  class: \"file-item\"\n};\nconst _hoisted_46 = {\n  class: \"file-controls\"\n};\nconst _hoisted_47 = {\n  key: 0,\n  class: \"current-file\"\n};\nconst _hoisted_48 = {\n  key: 1,\n  class: \"no-file\"\n};\nconst _hoisted_49 = {\n  class: \"file-item\"\n};\nconst _hoisted_50 = {\n  class: \"file-controls\"\n};\nconst _hoisted_51 = {\n  key: 0,\n  class: \"current-file\"\n};\nconst _hoisted_52 = {\n  key: 1,\n  class: \"no-file\"\n};\nconst _hoisted_53 = {\n  class: \"file-item\"\n};\nconst _hoisted_54 = {\n  class: \"file-controls\"\n};\nconst _hoisted_55 = {\n  key: 0,\n  class: \"current-file\"\n};\nconst _hoisted_56 = {\n  key: 1,\n  class: \"no-file\"\n};\nconst _hoisted_57 = {\n  class: \"file-item\"\n};\nconst _hoisted_58 = {\n  class: \"file-controls\"\n};\nconst _hoisted_59 = {\n  key: 0,\n  class: \"current-file\"\n};\nconst _hoisted_60 = {\n  key: 1,\n  class: \"no-file\"\n};\nconst _hoisted_61 = {\n  class: \"file-item\"\n};\nconst _hoisted_62 = {\n  class: \"file-controls\"\n};\nconst _hoisted_63 = {\n  key: 0,\n  class: \"current-file\"\n};\nconst _hoisted_64 = {\n  key: 1,\n  class: \"no-file\"\n};\nconst _hoisted_65 = {\n  class: \"file-item\"\n};\nconst _hoisted_66 = {\n  class: \"file-controls\"\n};\nconst _hoisted_67 = {\n  key: 0,\n  class: \"current-file\"\n};\nconst _hoisted_68 = {\n  key: 1,\n  class: \"no-file\"\n};\nconst _hoisted_69 = {\n  class: \"file-item\"\n};\nconst _hoisted_70 = {\n  class: \"file-controls\"\n};\nconst _hoisted_71 = {\n  key: 0,\n  class: \"current-file\"\n};\nconst _hoisted_72 = {\n  key: 1,\n  class: \"no-file\"\n};\nconst _hoisted_73 = {\n  class: \"file-item\"\n};\nconst _hoisted_74 = {\n  class: \"file-controls\"\n};\nconst _hoisted_75 = {\n  key: 0,\n  class: \"current-file\"\n};\nconst _hoisted_76 = {\n  key: 1,\n  class: \"no-file\"\n};\nconst _hoisted_77 = {\n  class: \"file-item\"\n};\nconst _hoisted_78 = {\n  class: \"file-controls\"\n};\nconst _hoisted_79 = {\n  key: 0,\n  class: \"current-file\"\n};\nconst _hoisted_80 = {\n  key: 1,\n  class: \"no-file\"\n};\nconst _hoisted_81 = {\n  class: \"file-item\"\n};\nconst _hoisted_82 = {\n  class: \"file-controls\"\n};\nconst _hoisted_83 = {\n  key: 0,\n  class: \"current-file\"\n};\nconst _hoisted_84 = {\n  key: 1,\n  class: \"no-file\"\n};\nconst _hoisted_85 = {\n  class: \"file-item\"\n};\nconst _hoisted_86 = {\n  class: \"file-controls\"\n};\nconst _hoisted_87 = {\n  key: 0,\n  class: \"current-file\"\n};\nconst _hoisted_88 = {\n  key: 1,\n  class: \"no-file\"\n};\nconst _hoisted_89 = {\n  class: \"file-item\"\n};\nconst _hoisted_90 = {\n  class: \"file-controls\"\n};\nconst _hoisted_91 = {\n  key: 0,\n  class: \"current-file\"\n};\nconst _hoisted_92 = {\n  key: 1,\n  class: \"no-file\"\n};\nconst _hoisted_93 = {\n  class: \"file-item\"\n};\nconst _hoisted_94 = {\n  class: \"file-controls\"\n};\nconst _hoisted_95 = {\n  key: 0,\n  class: \"current-file\"\n};\nconst _hoisted_96 = {\n  key: 1,\n  class: \"no-file\"\n};\nconst _hoisted_97 = {\n  class: \"file-item\"\n};\nconst _hoisted_98 = {\n  class: \"file-controls\"\n};\nconst _hoisted_99 = {\n  key: 0,\n  class: \"current-file\"\n};\nconst _hoisted_100 = {\n  key: 1,\n  class: \"no-file\"\n};\nconst _hoisted_101 = {\n  class: \"file-item\"\n};\nconst _hoisted_102 = {\n  class: \"file-controls\"\n};\nconst _hoisted_103 = {\n  key: 0,\n  class: \"current-file\"\n};\nconst _hoisted_104 = {\n  key: 1,\n  class: \"no-file\"\n};\nconst _hoisted_105 = {\n  class: \"dialog-footer\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_option = _resolveComponent(\"el-option\");\n  const _component_el_select = _resolveComponent(\"el-select\");\n  const _component_Refresh = _resolveComponent(\"Refresh\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_table_column = _resolveComponent(\"el-table-column\");\n  const _component_el_table = _resolveComponent(\"el-table\");\n  const _component_el_pagination = _resolveComponent(\"el-pagination\");\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_input_number = _resolveComponent(\"el-input-number\");\n  const _component_el_divider = _resolveComponent(\"el-divider\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  const _component_el_dialog = _resolveComponent(\"el-dialog\");\n  const _directive_loading = _resolveDirective(\"loading\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_cache[41] || (_cache[41] = _createElementVNode(\"h2\", null, \"数据表格管理\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"span\", null, \"总计: \" + _toDisplayString($setup.totalCount) + \" 条记录\", 1 /* TEXT */), $setup.tableData.length > 0 ? (_openBlock(), _createElementBlock(\"span\", _hoisted_4, \" (当前页: \" + _toDisplayString($setup.tableData.length) + \" 条) \", 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), $setup.selectedRows.length > 0 ? (_openBlock(), _createElementBlock(\"span\", _hoisted_5, \" 已选择: \" + _toDisplayString($setup.selectedRows.length) + \" 条 \", 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" 搜索和筛选区域 \"), _createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, [_createCommentVNode(\" Accession搜索框 \"), _createElementVNode(\"div\", _hoisted_8, [_cache[42] || (_cache[42] = _createElementVNode(\"label\", null, \"Accession搜索:\", -1 /* CACHED */)), _createVNode(_component_el_select, {\n    modelValue: $setup.searchAccession,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.searchAccession = $event),\n    placeholder: \"请选择Accession\",\n    filterable: \"\",\n    clearable: \"\",\n    style: {\n      \"width\": \"250px\"\n    },\n    onChange: $setup.handleAccessionChange,\n    onClear: $setup.handleAccessionClear\n  }, {\n    default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.allAccessionOptions, accession => {\n      return _openBlock(), _createBlock(_component_el_option, {\n        key: accession,\n        label: accession,\n        value: accession\n      }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n    }), 128 /* KEYED_FRAGMENT */))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onChange\", \"onClear\"])]), _createCommentVNode(\" SubPopulation筛选 \"), _createElementVNode(\"div\", _hoisted_9, [_cache[43] || (_cache[43] = _createElementVNode(\"label\", null, \"SubPopulation筛选:\", -1 /* CACHED */)), _createVNode(_component_el_select, {\n    modelValue: $setup.selectedSubPopulations,\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.selectedSubPopulations = $event),\n    placeholder: \"请选择亚群\",\n    multiple: \"\",\n    \"collapse-tags\": \"\",\n    \"collapse-tags-tooltip\": \"\",\n    clearable: \"\",\n    style: {\n      \"width\": \"300px\"\n    },\n    onChange: $setup.handleSubPopulationFilterChange\n  }, {\n    default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.subPopulationOptions, option => {\n      return _openBlock(), _createBlock(_component_el_option, {\n        key: option.value,\n        label: option.label,\n        value: option.value\n      }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n    }), 128 /* KEYED_FRAGMENT */))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onChange\"])]), _createCommentVNode(\" 重置按钮 \"), _createElementVNode(\"div\", _hoisted_10, [_createVNode(_component_el_button, {\n    onClick: $setup.resetFilters\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Refresh)]),\n      _: 1 /* STABLE */\n    }), _cache[44] || (_cache[44] = _createTextVNode(\" 重置筛选 \"))]),\n    _: 1 /* STABLE */,\n    __: [44]\n  }, 8 /* PROPS */, [\"onClick\"])])])]), _createCommentVNode(\" 数据表格 \"), _withDirectives((_openBlock(), _createBlock(_component_el_table, {\n    data: $setup.tableData,\n    border: \"\",\n    stripe: \"\",\n    style: {\n      \"width\": \"100%\"\n    },\n    \"header-cell-style\": {\n      background: '#f0f5ff',\n      color: '#1a56db',\n      fontWeight: 'bold'\n    },\n    onSelectionChange: $setup.handleSelectionChange\n  }, {\n    default: _withCtx(() => [_createCommentVNode(\" 多选框列 \"), _createVNode(_component_el_table_column, {\n      type: \"selection\",\n      width: \"55\",\n      fixed: \"left\"\n    }), _createVNode(_component_el_table_column, {\n      prop: \"accession\",\n      label: \"Accession\",\n      width: \"150\",\n      fixed: \"left\"\n    }), _createVNode(_component_el_table_column, {\n      prop: \"subPopulation\",\n      label: \"SubPopulation\",\n      width: \"120\"\n    }), _createVNode(_component_el_table_column, {\n      prop: \"seqData\",\n      label: \"SeqData\",\n      width: \"200\"\n    }, {\n      default: _withCtx(scope => [scope.row.seqData && scope.row.seqData !== '-' ? (_openBlock(), _createElementBlock(\"a\", {\n        key: 0,\n        href: scope.row.seqData,\n        target: \"_blank\",\n        class: \"data-link\"\n      }, _toDisplayString(scope.row.seqData), 9 /* TEXT, PROPS */, _hoisted_11)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_12, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      prop: \"longitude\",\n      label: \"Longitude\",\n      width: \"100\"\n    }), _createVNode(_component_el_table_column, {\n      prop: \"latitude\",\n      label: \"Latitude\",\n      width: \"100\"\n    }), _createCommentVNode(\" 文件显示列 \"), _createVNode(_component_el_table_column, {\n      label: \"Genome\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [scope.row.genomeFile ? (_openBlock(), _createElementBlock(\"span\", {\n        key: 0,\n        class: \"file-link\",\n        onClick: $event => $setup.downloadFile(scope.row.accession, 'genome'),\n        title: scope.row.genomeFile\n      }, _toDisplayString(scope.row.genomeFile), 9 /* TEXT, PROPS */, _hoisted_13)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_14, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"Annotation\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [scope.row.annotationFile ? (_openBlock(), _createElementBlock(\"span\", {\n        key: 0,\n        class: \"file-link\",\n        onClick: $event => $setup.downloadFile(scope.row.accession, 'annotation'),\n        title: scope.row.annotationFile\n      }, _toDisplayString(scope.row.annotationFile), 9 /* TEXT, PROPS */, _hoisted_15)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_16, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"Transcriptome.all\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [scope.row.transcriptomeAllFile ? (_openBlock(), _createElementBlock(\"span\", {\n        key: 0,\n        class: \"file-link\",\n        onClick: $event => $setup.downloadFile(scope.row.accession, 'transcriptome.all'),\n        title: scope.row.transcriptomeAllFile\n      }, _toDisplayString(scope.row.transcriptomeAllFile), 9 /* TEXT, PROPS */, _hoisted_17)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_18, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"Transcriptome.leaf\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [scope.row.transcriptomeLeafFile ? (_openBlock(), _createElementBlock(\"span\", {\n        key: 0,\n        class: \"file-link\",\n        onClick: $event => $setup.downloadFile(scope.row.accession, 'transcriptome.leaf'),\n        title: scope.row.transcriptomeLeafFile\n      }, _toDisplayString(scope.row.transcriptomeLeafFile), 9 /* TEXT, PROPS */, _hoisted_19)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_20, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"Transcriptome.panicles\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [scope.row.transcriptomePaniclesFile ? (_openBlock(), _createElementBlock(\"span\", {\n        key: 0,\n        class: \"file-link\",\n        onClick: $event => $setup.downloadFile(scope.row.accession, 'transcriptome.panicles'),\n        title: scope.row.transcriptomePaniclesFile\n      }, _toDisplayString(scope.row.transcriptomePaniclesFile), 9 /* TEXT, PROPS */, _hoisted_21)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_22, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"Transcriptome.shoot\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [scope.row.transcriptomeShootFile ? (_openBlock(), _createElementBlock(\"span\", {\n        key: 0,\n        class: \"file-link\",\n        onClick: $event => $setup.downloadFile(scope.row.accession, 'transcriptome.shoot'),\n        title: scope.row.transcriptomeShootFile\n      }, _toDisplayString(scope.row.transcriptomeShootFile), 9 /* TEXT, PROPS */, _hoisted_23)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_24, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"Transcriptome.stem\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [scope.row.transcriptomeStemFile ? (_openBlock(), _createElementBlock(\"span\", {\n        key: 0,\n        class: \"file-link\",\n        onClick: $event => $setup.downloadFile(scope.row.accession, 'transcriptome.stem'),\n        title: scope.row.transcriptomeStemFile\n      }, _toDisplayString(scope.row.transcriptomeStemFile), 9 /* TEXT, PROPS */, _hoisted_25)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_26, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"Transcriptome.root\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [scope.row.transcriptomeRootFile ? (_openBlock(), _createElementBlock(\"span\", {\n        key: 0,\n        class: \"file-link\",\n        onClick: $event => $setup.downloadFile(scope.row.accession, 'transcriptome.root'),\n        title: scope.row.transcriptomeRootFile\n      }, _toDisplayString(scope.row.transcriptomeRootFile), 9 /* TEXT, PROPS */, _hoisted_27)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_28, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"Codon\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [scope.row.codonFile ? (_openBlock(), _createElementBlock(\"span\", {\n        key: 0,\n        class: \"file-link\",\n        onClick: $event => $setup.downloadFile(scope.row.accession, 'codon'),\n        title: scope.row.codonFile\n      }, _toDisplayString(scope.row.codonFile), 9 /* TEXT, PROPS */, _hoisted_29)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_30, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"Centromere\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [scope.row.centromereFile ? (_openBlock(), _createElementBlock(\"span\", {\n        key: 0,\n        class: \"file-link\",\n        onClick: $event => $setup.downloadFile(scope.row.accession, 'centromere'),\n        title: scope.row.centromereFile\n      }, _toDisplayString(scope.row.centromereFile), 9 /* TEXT, PROPS */, _hoisted_31)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_32, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"TEs\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [scope.row.tesFile ? (_openBlock(), _createElementBlock(\"span\", {\n        key: 0,\n        class: \"file-link\",\n        onClick: $event => $setup.downloadFile(scope.row.accession, 'TEs'),\n        title: scope.row.tesFile\n      }, _toDisplayString(scope.row.tesFile), 9 /* TEXT, PROPS */, _hoisted_33)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_34, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"CoreBlocks\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [scope.row.coreBlocksFile ? (_openBlock(), _createElementBlock(\"span\", {\n        key: 0,\n        class: \"file-link\",\n        onClick: $event => $setup.downloadFile(scope.row.accession, 'coreBlocks'),\n        title: scope.row.coreBlocksFile\n      }, _toDisplayString(scope.row.coreBlocksFile), 9 /* TEXT, PROPS */, _hoisted_35)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_36, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"miRNA\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [scope.row.miRNAFile ? (_openBlock(), _createElementBlock(\"span\", {\n        key: 0,\n        class: \"file-link\",\n        onClick: $event => $setup.downloadFile(scope.row.accession, 'miRNA'),\n        title: scope.row.miRNAFile\n      }, _toDisplayString(scope.row.miRNAFile), 9 /* TEXT, PROPS */, _hoisted_37)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_38, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"tRNA\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [scope.row.tRNAFile ? (_openBlock(), _createElementBlock(\"span\", {\n        key: 0,\n        class: \"file-link\",\n        onClick: $event => $setup.downloadFile(scope.row.accession, 'tRNA'),\n        title: scope.row.tRNAFile\n      }, _toDisplayString(scope.row.tRNAFile), 9 /* TEXT, PROPS */, _hoisted_39)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_40, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"rRNA\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [scope.row.rRNAFile ? (_openBlock(), _createElementBlock(\"span\", {\n        key: 0,\n        class: \"file-link\",\n        onClick: $event => $setup.downloadFile(scope.row.accession, 'rRNA'),\n        title: scope.row.rRNAFile\n      }, _toDisplayString(scope.row.rRNAFile), 9 /* TEXT, PROPS */, _hoisted_41)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_42, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"操作\",\n      width: \"150\",\n      fixed: \"right\"\n    }, {\n      default: _withCtx(scope => [_createVNode(_component_el_button, {\n        type: \"primary\",\n        size: \"small\",\n        onClick: $event => $setup.editRow(scope.row)\n      }, {\n        default: _withCtx(() => _cache[45] || (_cache[45] = [_createTextVNode(\"编辑\")])),\n        _: 2 /* DYNAMIC */,\n        __: [45]\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"]), _createVNode(_component_el_button, {\n        type: \"danger\",\n        size: \"small\",\n        onClick: $event => $setup.deleteRow(scope.row)\n      }, {\n        default: _withCtx(() => _cache[46] || (_cache[46] = [_createTextVNode(\"删除\")])),\n        _: 2 /* DYNAMIC */,\n        __: [46]\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"data\", \"onSelectionChange\"])), [[_directive_loading, $setup.loading]]), _createCommentVNode(\" 分页 \"), _createElementVNode(\"div\", _hoisted_43, [_createVNode(_component_el_pagination, {\n    \"current-page\": $setup.currentPage,\n    \"onUpdate:currentPage\": _cache[2] || (_cache[2] = $event => $setup.currentPage = $event),\n    \"page-size\": $setup.pageSize,\n    \"onUpdate:pageSize\": _cache[3] || (_cache[3] = $event => $setup.pageSize = $event),\n    \"page-sizes\": [20, 50, 100],\n    total: $setup.totalCount,\n    layout: \"total, sizes, prev, pager, next, jumper\",\n    onSizeChange: $setup.handleSizeChange,\n    onCurrentChange: $setup.handleCurrentChange\n  }, null, 8 /* PROPS */, [\"current-page\", \"page-size\", \"total\", \"onSizeChange\", \"onCurrentChange\"])]), _createCommentVNode(\" 新增/编辑对话框 \"), _createVNode(_component_el_dialog, {\n    modelValue: $setup.showAddDialog,\n    \"onUpdate:modelValue\": _cache[40] || (_cache[40] = $event => $setup.showAddDialog = $event),\n    title: $setup.editingRow ? '编辑 Accession' : '新增 Accession',\n    width: \"600px\",\n    onClose: $setup.resetForm\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"span\", _hoisted_105, [_createVNode(_component_el_button, {\n      onClick: _cache[39] || (_cache[39] = $event => $setup.showAddDialog = false)\n    }, {\n      default: _withCtx(() => _cache[93] || (_cache[93] = [_createTextVNode(\"取消\")])),\n      _: 1 /* STABLE */,\n      __: [93]\n    }), _createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $setup.saveData,\n      loading: $setup.saving\n    }, {\n      default: _withCtx(() => _cache[94] || (_cache[94] = [_createTextVNode(\"保存\")])),\n      _: 1 /* STABLE */,\n      __: [94]\n    }, 8 /* PROPS */, [\"onClick\", \"loading\"])])]),\n    default: _withCtx(() => [_createVNode(_component_el_form, {\n      model: $setup.formData,\n      rules: $setup.formRules,\n      ref: \"formRef\",\n      \"label-width\": \"120px\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_form_item, {\n        label: \"Accession\",\n        prop: \"accession\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.formData.accession,\n          \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $setup.formData.accession = $event),\n          disabled: $setup.editingRow\n        }, null, 8 /* PROPS */, [\"modelValue\", \"disabled\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"SubPopulation\",\n        prop: \"subPopulation\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_select, {\n          modelValue: $setup.formData.subPopulation,\n          \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $setup.formData.subPopulation = $event),\n          placeholder: \"请选择或输入亚群\",\n          filterable: \"\",\n          \"allow-create\": \"\",\n          \"default-first-option\": \"\",\n          \"reserve-keyword\": false,\n          onChange: _ctx.handleSubPopulationChange\n        }, {\n          default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.subPopulationOptions, option => {\n            return _openBlock(), _createBlock(_component_el_option, {\n              key: option.value,\n              label: option.label,\n              value: option.value\n            }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n          }), 128 /* KEYED_FRAGMENT */))]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"modelValue\", \"onChange\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"SeqData URL\",\n        prop: \"seqData\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.formData.seqData,\n          \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $setup.formData.seqData = $event),\n          placeholder: \"请输入SeqData链接，留空则为 -\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"经度\",\n        prop: \"longitude\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input_number, {\n          modelValue: $setup.formData.longitude,\n          \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $setup.formData.longitude = $event),\n          precision: 2,\n          placeholder: \"经度\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"纬度\",\n        prop: \"latitude\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input_number, {\n          modelValue: $setup.formData.latitude,\n          \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $setup.formData.latitude = $event),\n          precision: 2,\n          placeholder: \"纬度\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createCommentVNode(\" 文件管理部分 \"), _createVNode(_component_el_divider, {\n        \"content-position\": \"left\"\n      }, {\n        default: _withCtx(() => _cache[47] || (_cache[47] = [_createTextVNode(\"文件管理\")])),\n        _: 1 /* STABLE */,\n        __: [47]\n      }), _createElementVNode(\"div\", _hoisted_44, [_createCommentVNode(\" Genome \"), _createElementVNode(\"div\", _hoisted_45, [_cache[50] || (_cache[50] = _createElementVNode(\"label\", null, \"Genome:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_46, [$setup.formData.files.genome ? (_openBlock(), _createElementBlock(\"span\", _hoisted_47, _toDisplayString($setup.formData.files.genome), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_48, \"无文件\")), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[9] || (_cache[9] = $event => $setup.selectFile('genome'))\n      }, {\n        default: _withCtx(() => _cache[48] || (_cache[48] = [_createTextVNode(\"选择文件\")])),\n        _: 1 /* STABLE */,\n        __: [48]\n      }), $setup.formData.files.genome ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 2,\n        size: \"small\",\n        type: \"danger\",\n        onClick: _cache[10] || (_cache[10] = $event => $setup.removeFile('genome'))\n      }, {\n        default: _withCtx(() => _cache[49] || (_cache[49] = [_createTextVNode(\"删除\")])),\n        _: 1 /* STABLE */,\n        __: [49]\n      })) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" Annotation \"), _createElementVNode(\"div\", _hoisted_49, [_cache[53] || (_cache[53] = _createElementVNode(\"label\", null, \"Annotation:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_50, [$setup.formData.files.annotation ? (_openBlock(), _createElementBlock(\"span\", _hoisted_51, _toDisplayString($setup.formData.files.annotation), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_52, \"无文件\")), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[11] || (_cache[11] = $event => $setup.selectFile('annotation'))\n      }, {\n        default: _withCtx(() => _cache[51] || (_cache[51] = [_createTextVNode(\"选择文件\")])),\n        _: 1 /* STABLE */,\n        __: [51]\n      }), $setup.formData.files.annotation ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 2,\n        size: \"small\",\n        type: \"danger\",\n        onClick: _cache[12] || (_cache[12] = $event => $setup.removeFile('annotation'))\n      }, {\n        default: _withCtx(() => _cache[52] || (_cache[52] = [_createTextVNode(\"删除\")])),\n        _: 1 /* STABLE */,\n        __: [52]\n      })) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" Transcriptome.all \"), _createElementVNode(\"div\", _hoisted_53, [_cache[56] || (_cache[56] = _createElementVNode(\"label\", null, \"Transcriptome.all:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_54, [$setup.formData.files.transcriptomeAll ? (_openBlock(), _createElementBlock(\"span\", _hoisted_55, _toDisplayString($setup.formData.files.transcriptomeAll), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_56, \"无文件\")), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[13] || (_cache[13] = $event => $setup.selectFile('transcriptomeAll'))\n      }, {\n        default: _withCtx(() => _cache[54] || (_cache[54] = [_createTextVNode(\"选择文件\")])),\n        _: 1 /* STABLE */,\n        __: [54]\n      }), $setup.formData.files.transcriptomeAll ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 2,\n        size: \"small\",\n        type: \"danger\",\n        onClick: _cache[14] || (_cache[14] = $event => $setup.removeFile('transcriptomeAll'))\n      }, {\n        default: _withCtx(() => _cache[55] || (_cache[55] = [_createTextVNode(\"删除\")])),\n        _: 1 /* STABLE */,\n        __: [55]\n      })) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" Transcriptome.leaf \"), _createElementVNode(\"div\", _hoisted_57, [_cache[59] || (_cache[59] = _createElementVNode(\"label\", null, \"Transcriptome.leaf:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_58, [$setup.formData.files.transcriptomeLeaf ? (_openBlock(), _createElementBlock(\"span\", _hoisted_59, _toDisplayString($setup.formData.files.transcriptomeLeaf), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_60, \"无文件\")), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[15] || (_cache[15] = $event => $setup.selectFile('transcriptomeLeaf'))\n      }, {\n        default: _withCtx(() => _cache[57] || (_cache[57] = [_createTextVNode(\"选择文件\")])),\n        _: 1 /* STABLE */,\n        __: [57]\n      }), $setup.formData.files.transcriptomeLeaf ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 2,\n        size: \"small\",\n        type: \"danger\",\n        onClick: _cache[16] || (_cache[16] = $event => $setup.removeFile('transcriptomeLeaf'))\n      }, {\n        default: _withCtx(() => _cache[58] || (_cache[58] = [_createTextVNode(\"删除\")])),\n        _: 1 /* STABLE */,\n        __: [58]\n      })) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" Transcriptome.panicles \"), _createElementVNode(\"div\", _hoisted_61, [_cache[62] || (_cache[62] = _createElementVNode(\"label\", null, \"Transcriptome.panicles:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_62, [$setup.formData.files.transcriptomePanicles ? (_openBlock(), _createElementBlock(\"span\", _hoisted_63, _toDisplayString($setup.formData.files.transcriptomePanicles), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_64, \"无文件\")), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[17] || (_cache[17] = $event => $setup.selectFile('transcriptomePanicles'))\n      }, {\n        default: _withCtx(() => _cache[60] || (_cache[60] = [_createTextVNode(\"选择文件\")])),\n        _: 1 /* STABLE */,\n        __: [60]\n      }), $setup.formData.files.transcriptomePanicles ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 2,\n        size: \"small\",\n        type: \"danger\",\n        onClick: _cache[18] || (_cache[18] = $event => $setup.removeFile('transcriptomePanicles'))\n      }, {\n        default: _withCtx(() => _cache[61] || (_cache[61] = [_createTextVNode(\"删除\")])),\n        _: 1 /* STABLE */,\n        __: [61]\n      })) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" Transcriptome.shoot \"), _createElementVNode(\"div\", _hoisted_65, [_cache[65] || (_cache[65] = _createElementVNode(\"label\", null, \"Transcriptome.shoot:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_66, [$setup.formData.files.transcriptomeShoot ? (_openBlock(), _createElementBlock(\"span\", _hoisted_67, _toDisplayString($setup.formData.files.transcriptomeShoot), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_68, \"无文件\")), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[19] || (_cache[19] = $event => $setup.selectFile('transcriptomeShoot'))\n      }, {\n        default: _withCtx(() => _cache[63] || (_cache[63] = [_createTextVNode(\"选择文件\")])),\n        _: 1 /* STABLE */,\n        __: [63]\n      }), $setup.formData.files.transcriptomeShoot ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 2,\n        size: \"small\",\n        type: \"danger\",\n        onClick: _cache[20] || (_cache[20] = $event => $setup.removeFile('transcriptomeShoot'))\n      }, {\n        default: _withCtx(() => _cache[64] || (_cache[64] = [_createTextVNode(\"删除\")])),\n        _: 1 /* STABLE */,\n        __: [64]\n      })) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" Transcriptome.stem \"), _createElementVNode(\"div\", _hoisted_69, [_cache[68] || (_cache[68] = _createElementVNode(\"label\", null, \"Transcriptome.stem:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_70, [$setup.formData.files.transcriptomeStem ? (_openBlock(), _createElementBlock(\"span\", _hoisted_71, _toDisplayString($setup.formData.files.transcriptomeStem), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_72, \"无文件\")), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[21] || (_cache[21] = $event => $setup.selectFile('transcriptomeStem'))\n      }, {\n        default: _withCtx(() => _cache[66] || (_cache[66] = [_createTextVNode(\"选择文件\")])),\n        _: 1 /* STABLE */,\n        __: [66]\n      }), $setup.formData.files.transcriptomeStem ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 2,\n        size: \"small\",\n        type: \"danger\",\n        onClick: _cache[22] || (_cache[22] = $event => $setup.removeFile('transcriptomeStem'))\n      }, {\n        default: _withCtx(() => _cache[67] || (_cache[67] = [_createTextVNode(\"删除\")])),\n        _: 1 /* STABLE */,\n        __: [67]\n      })) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" Transcriptome.root \"), _createElementVNode(\"div\", _hoisted_73, [_cache[71] || (_cache[71] = _createElementVNode(\"label\", null, \"Transcriptome.root:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_74, [$setup.formData.files.transcriptomeRoot ? (_openBlock(), _createElementBlock(\"span\", _hoisted_75, _toDisplayString($setup.formData.files.transcriptomeRoot), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_76, \"无文件\")), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[23] || (_cache[23] = $event => $setup.selectFile('transcriptomeRoot'))\n      }, {\n        default: _withCtx(() => _cache[69] || (_cache[69] = [_createTextVNode(\"选择文件\")])),\n        _: 1 /* STABLE */,\n        __: [69]\n      }), $setup.formData.files.transcriptomeRoot ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 2,\n        size: \"small\",\n        type: \"danger\",\n        onClick: _cache[24] || (_cache[24] = $event => $setup.removeFile('transcriptomeRoot'))\n      }, {\n        default: _withCtx(() => _cache[70] || (_cache[70] = [_createTextVNode(\"删除\")])),\n        _: 1 /* STABLE */,\n        __: [70]\n      })) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" Codon \"), _createElementVNode(\"div\", _hoisted_77, [_cache[74] || (_cache[74] = _createElementVNode(\"label\", null, \"Codon:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_78, [$setup.formData.files.codon ? (_openBlock(), _createElementBlock(\"span\", _hoisted_79, _toDisplayString($setup.formData.files.codon), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_80, \"无文件\")), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[25] || (_cache[25] = $event => $setup.selectFile('codon'))\n      }, {\n        default: _withCtx(() => _cache[72] || (_cache[72] = [_createTextVNode(\"选择文件\")])),\n        _: 1 /* STABLE */,\n        __: [72]\n      }), $setup.formData.files.codon ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 2,\n        size: \"small\",\n        type: \"danger\",\n        onClick: _cache[26] || (_cache[26] = $event => $setup.removeFile('codon'))\n      }, {\n        default: _withCtx(() => _cache[73] || (_cache[73] = [_createTextVNode(\"删除\")])),\n        _: 1 /* STABLE */,\n        __: [73]\n      })) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" Centromere \"), _createElementVNode(\"div\", _hoisted_81, [_cache[77] || (_cache[77] = _createElementVNode(\"label\", null, \"Centromere:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_82, [$setup.formData.files.centromere ? (_openBlock(), _createElementBlock(\"span\", _hoisted_83, _toDisplayString($setup.formData.files.centromere), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_84, \"无文件\")), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[27] || (_cache[27] = $event => $setup.selectFile('centromere'))\n      }, {\n        default: _withCtx(() => _cache[75] || (_cache[75] = [_createTextVNode(\"选择文件\")])),\n        _: 1 /* STABLE */,\n        __: [75]\n      }), $setup.formData.files.centromere ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 2,\n        size: \"small\",\n        type: \"danger\",\n        onClick: _cache[28] || (_cache[28] = $event => $setup.removeFile('centromere'))\n      }, {\n        default: _withCtx(() => _cache[76] || (_cache[76] = [_createTextVNode(\"删除\")])),\n        _: 1 /* STABLE */,\n        __: [76]\n      })) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" TEs \"), _createElementVNode(\"div\", _hoisted_85, [_cache[80] || (_cache[80] = _createElementVNode(\"label\", null, \"TEs:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_86, [$setup.formData.files.TEs ? (_openBlock(), _createElementBlock(\"span\", _hoisted_87, _toDisplayString($setup.formData.files.TEs), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_88, \"无文件\")), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[29] || (_cache[29] = $event => $setup.selectFile('TEs'))\n      }, {\n        default: _withCtx(() => _cache[78] || (_cache[78] = [_createTextVNode(\"选择文件\")])),\n        _: 1 /* STABLE */,\n        __: [78]\n      }), $setup.formData.files.TEs ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 2,\n        size: \"small\",\n        type: \"danger\",\n        onClick: _cache[30] || (_cache[30] = $event => $setup.removeFile('TEs'))\n      }, {\n        default: _withCtx(() => _cache[79] || (_cache[79] = [_createTextVNode(\"删除\")])),\n        _: 1 /* STABLE */,\n        __: [79]\n      })) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" CoreBlocks \"), _createElementVNode(\"div\", _hoisted_89, [_cache[83] || (_cache[83] = _createElementVNode(\"label\", null, \"CoreBlocks:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_90, [$setup.formData.files.coreBlocks ? (_openBlock(), _createElementBlock(\"span\", _hoisted_91, _toDisplayString($setup.formData.files.coreBlocks), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_92, \"无文件\")), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[31] || (_cache[31] = $event => $setup.selectFile('coreBlocks'))\n      }, {\n        default: _withCtx(() => _cache[81] || (_cache[81] = [_createTextVNode(\"选择文件\")])),\n        _: 1 /* STABLE */,\n        __: [81]\n      }), $setup.formData.files.coreBlocks ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 2,\n        size: \"small\",\n        type: \"danger\",\n        onClick: _cache[32] || (_cache[32] = $event => $setup.removeFile('coreBlocks'))\n      }, {\n        default: _withCtx(() => _cache[82] || (_cache[82] = [_createTextVNode(\"删除\")])),\n        _: 1 /* STABLE */,\n        __: [82]\n      })) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" miRNA \"), _createElementVNode(\"div\", _hoisted_93, [_cache[86] || (_cache[86] = _createElementVNode(\"label\", null, \"miRNA:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_94, [$setup.formData.files.miRNA ? (_openBlock(), _createElementBlock(\"span\", _hoisted_95, _toDisplayString($setup.formData.files.miRNA), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_96, \"无文件\")), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[33] || (_cache[33] = $event => $setup.selectFile('miRNA'))\n      }, {\n        default: _withCtx(() => _cache[84] || (_cache[84] = [_createTextVNode(\"选择文件\")])),\n        _: 1 /* STABLE */,\n        __: [84]\n      }), $setup.formData.files.miRNA ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 2,\n        size: \"small\",\n        type: \"danger\",\n        onClick: _cache[34] || (_cache[34] = $event => $setup.removeFile('miRNA'))\n      }, {\n        default: _withCtx(() => _cache[85] || (_cache[85] = [_createTextVNode(\"删除\")])),\n        _: 1 /* STABLE */,\n        __: [85]\n      })) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" tRNA \"), _createElementVNode(\"div\", _hoisted_97, [_cache[89] || (_cache[89] = _createElementVNode(\"label\", null, \"tRNA:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_98, [$setup.formData.files.tRNA ? (_openBlock(), _createElementBlock(\"span\", _hoisted_99, _toDisplayString($setup.formData.files.tRNA), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_100, \"无文件\")), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[35] || (_cache[35] = $event => $setup.selectFile('tRNA'))\n      }, {\n        default: _withCtx(() => _cache[87] || (_cache[87] = [_createTextVNode(\"选择文件\")])),\n        _: 1 /* STABLE */,\n        __: [87]\n      }), $setup.formData.files.tRNA ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 2,\n        size: \"small\",\n        type: \"danger\",\n        onClick: _cache[36] || (_cache[36] = $event => $setup.removeFile('tRNA'))\n      }, {\n        default: _withCtx(() => _cache[88] || (_cache[88] = [_createTextVNode(\"删除\")])),\n        _: 1 /* STABLE */,\n        __: [88]\n      })) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" rRNA \"), _createElementVNode(\"div\", _hoisted_101, [_cache[92] || (_cache[92] = _createElementVNode(\"label\", null, \"rRNA:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_102, [$setup.formData.files.rRNA ? (_openBlock(), _createElementBlock(\"span\", _hoisted_103, _toDisplayString($setup.formData.files.rRNA), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_104, \"无文件\")), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[37] || (_cache[37] = $event => $setup.selectFile('rRNA'))\n      }, {\n        default: _withCtx(() => _cache[90] || (_cache[90] = [_createTextVNode(\"选择文件\")])),\n        _: 1 /* STABLE */,\n        __: [90]\n      }), $setup.formData.files.rRNA ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 2,\n        size: \"small\",\n        type: \"danger\",\n        onClick: _cache[38] || (_cache[38] = $event => $setup.removeFile('rRNA'))\n      }, {\n        default: _withCtx(() => _cache[91] || (_cache[91] = [_createTextVNode(\"删除\")])),\n        _: 1 /* STABLE */,\n        __: [91]\n      })) : _createCommentVNode(\"v-if\", true)])])])]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"model\", \"rules\"])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"title\", \"onClose\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_toDisplayString", "$setup", "totalCount", "tableData", "length", "_hoisted_4", "selectedRows", "_hoisted_5", "_createCommentVNode", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_createVNode", "_component_el_select", "searchAccession", "$event", "placeholder", "filterable", "clearable", "style", "onChange", "handleAccessionChange", "onClear", "handleAccessionClear", "_Fragment", "_renderList", "allAccessionOptions", "accession", "_createBlock", "_component_el_option", "key", "label", "value", "_hoisted_9", "selectedSubPopulations", "multiple", "handleSubPopulationFilterChange", "subPopulationOptions", "option", "_hoisted_10", "_component_el_button", "onClick", "resetFilters", "_component_el_icon", "_component_Refresh", "_component_el_table", "data", "border", "stripe", "background", "color", "fontWeight", "onSelectionChange", "handleSelectionChange", "_component_el_table_column", "type", "width", "fixed", "prop", "default", "_withCtx", "scope", "row", "seqData", "href", "target", "_hoisted_11", "_hoisted_12", "genomeFile", "downloadFile", "title", "_hoisted_13", "_hoisted_14", "annotationFile", "_hoisted_15", "_hoisted_16", "transcriptomeAllFile", "_hoisted_17", "_hoisted_18", "transcriptomeLeafFile", "_hoisted_19", "_hoisted_20", "transcriptomePaniclesFile", "_hoisted_21", "_hoisted_22", "transcriptomeShootFile", "_hoisted_23", "_hoisted_24", "transcriptomeStemFile", "_hoisted_25", "_hoisted_26", "transcriptomeRootFile", "_hoisted_27", "_hoisted_28", "codonFile", "_hoisted_29", "_hoisted_30", "centromereFile", "_hoisted_31", "_hoisted_32", "tesFile", "_hoisted_33", "_hoisted_34", "coreBlocksFile", "_hoisted_35", "_hoisted_36", "miRNAFile", "_hoisted_37", "_hoisted_38", "tRNAFile", "_hoisted_39", "_hoisted_40", "rRNAFile", "_hoisted_41", "_hoisted_42", "size", "editRow", "_cache", "deleteRow", "loading", "_hoisted_43", "_component_el_pagination", "currentPage", "pageSize", "total", "layout", "onSizeChange", "handleSizeChange", "onCurrentChange", "handleCurrentChange", "_component_el_dialog", "showAddDialog", "editingRow", "onClose", "resetForm", "footer", "_hoisted_105", "saveData", "saving", "_component_el_form", "model", "formData", "rules", "formRules", "ref", "_component_el_form_item", "_component_el_input", "disabled", "subPopulation", "_ctx", "handleSubPopulationChange", "_component_el_input_number", "longitude", "precision", "latitude", "_component_el_divider", "_hoisted_44", "_hoisted_45", "_hoisted_46", "files", "genome", "_hoisted_47", "_hoisted_48", "selectFile", "removeFile", "_hoisted_49", "_hoisted_50", "annotation", "_hoisted_51", "_hoisted_52", "_hoisted_53", "_hoisted_54", "transcriptomeAll", "_hoisted_55", "_hoisted_56", "_hoisted_57", "_hoisted_58", "transcriptomeLeaf", "_hoisted_59", "_hoisted_60", "_hoisted_61", "_hoisted_62", "transcriptomePanicles", "_hoisted_63", "_hoisted_64", "_hoisted_65", "_hoisted_66", "transcriptomeShoot", "_hoisted_67", "_hoisted_68", "_hoisted_69", "_hoisted_70", "transcriptomeStem", "_hoisted_71", "_hoisted_72", "_hoisted_73", "_hoisted_74", "transcriptomeRoot", "_hoisted_75", "_hoisted_76", "_hoisted_77", "_hoisted_78", "codon", "_hoisted_79", "_hoisted_80", "_hoisted_81", "_hoisted_82", "centromere", "_hoisted_83", "_hoisted_84", "_hoisted_85", "_hoisted_86", "TEs", "_hoisted_87", "_hoisted_88", "_hoisted_89", "_hoisted_90", "coreBlocks", "_hoisted_91", "_hoisted_92", "_hoisted_93", "_hoisted_94", "miRNA", "_hoisted_95", "_hoisted_96", "_hoisted_97", "_hoisted_98", "tRNA", "_hoisted_99", "_hoisted_100", "_hoisted_101", "_hoisted_102", "rRNA", "_hoisted_103", "_hoisted_104"], "sources": ["D:\\gene_manage_system\\vue_project\\src\\views\\AdminDataManager.vue"], "sourcesContent": ["<template>\n  <div class=\"admin-data-manager\">\n    <div class=\"page-header\">\n      <h2>数据表格管理</h2>\n      <div class=\"header-stats\">\n        <span>总计: {{ totalCount }} 条记录</span>\n        <span v-if=\"tableData.length > 0\">\n          (当前页: {{ tableData.length }} 条)\n        </span>\n        <span v-if=\"selectedRows.length > 0\" class=\"selected-info\">\n          已选择: {{ selectedRows.length }} 条\n        </span>\n      </div>\n    </div>\n\n    <!-- 搜索和筛选区域 -->\n    <div class=\"filter-section\">\n      <div class=\"filter-row\">\n        <!-- Accession搜索框 -->\n        <div class=\"filter-item\">\n          <label>Accession搜索:</label>\n          <el-select\n            v-model=\"searchAccession\"\n            placeholder=\"请选择Accession\"\n            filterable\n            clearable\n            style=\"width: 250px;\"\n            @change=\"handleAccessionChange\"\n            @clear=\"handleAccessionClear\">\n            <el-option\n              v-for=\"accession in allAccessionOptions\"\n              :key=\"accession\"\n              :label=\"accession\"\n              :value=\"accession\" />\n          </el-select>\n        </div>\n\n        <!-- SubPopulation筛选 -->\n        <div class=\"filter-item\">\n          <label>SubPopulation筛选:</label>\n          <el-select\n            v-model=\"selectedSubPopulations\"\n            placeholder=\"请选择亚群\"\n            multiple\n            collapse-tags\n            collapse-tags-tooltip\n            clearable\n            style=\"width: 300px;\"\n            @change=\"handleSubPopulationFilterChange\">\n            <el-option\n              v-for=\"option in subPopulationOptions\"\n              :key=\"option.value\"\n              :label=\"option.label\"\n              :value=\"option.value\" />\n          </el-select>\n        </div>\n\n        <!-- 重置按钮 -->\n        <div class=\"filter-item\">\n          <el-button @click=\"resetFilters\">\n            <el-icon><Refresh /></el-icon>\n            重置筛选\n          </el-button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 数据表格 -->\n    <el-table\n      :data=\"tableData\"\n      v-loading=\"loading\"\n      border\n      stripe\n      style=\"width: 100%\"\n      :header-cell-style=\"{ background: '#f0f5ff', color: '#1a56db', fontWeight: 'bold' }\"\n      @selection-change=\"handleSelectionChange\">\n\n      <!-- 多选框列 -->\n      <el-table-column type=\"selection\" width=\"55\" fixed=\"left\" />\n\n      <el-table-column prop=\"accession\" label=\"Accession\" width=\"150\" fixed=\"left\" />\n      <el-table-column prop=\"subPopulation\" label=\"SubPopulation\" width=\"120\" />\n      <el-table-column prop=\"seqData\" label=\"SeqData\" width=\"200\">\n        <template #default=\"scope\">\n          <a v-if=\"scope.row.seqData && scope.row.seqData !== '-'\" \n             :href=\"scope.row.seqData\" \n             target=\"_blank\" \n             class=\"data-link\">\n            {{ scope.row.seqData }}\n          </a>\n          <span v-else class=\"data-empty\">-</span>\n        </template>\n      </el-table-column>\n      <el-table-column prop=\"longitude\" label=\"Longitude\" width=\"100\" />\n      <el-table-column prop=\"latitude\" label=\"Latitude\" width=\"100\" />\n\n      <!-- 文件显示列 -->\n      <el-table-column label=\"Genome\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.genomeFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'genome')\"\n                :title=\"scope.row.genomeFile\">\n            {{ scope.row.genomeFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"Annotation\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.annotationFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'annotation')\"\n                :title=\"scope.row.annotationFile\">\n            {{ scope.row.annotationFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"Transcriptome.all\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.transcriptomeAllFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'transcriptome.all')\"\n                :title=\"scope.row.transcriptomeAllFile\">\n            {{ scope.row.transcriptomeAllFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"Transcriptome.leaf\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.transcriptomeLeafFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'transcriptome.leaf')\"\n                :title=\"scope.row.transcriptomeLeafFile\">\n            {{ scope.row.transcriptomeLeafFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"Transcriptome.panicles\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.transcriptomePaniclesFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'transcriptome.panicles')\"\n                :title=\"scope.row.transcriptomePaniclesFile\">\n            {{ scope.row.transcriptomePaniclesFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"Transcriptome.shoot\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.transcriptomeShootFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'transcriptome.shoot')\"\n                :title=\"scope.row.transcriptomeShootFile\">\n            {{ scope.row.transcriptomeShootFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"Transcriptome.stem\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.transcriptomeStemFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'transcriptome.stem')\"\n                :title=\"scope.row.transcriptomeStemFile\">\n            {{ scope.row.transcriptomeStemFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"Transcriptome.root\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.transcriptomeRootFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'transcriptome.root')\"\n                :title=\"scope.row.transcriptomeRootFile\">\n            {{ scope.row.transcriptomeRootFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"Codon\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.codonFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'codon')\"\n                :title=\"scope.row.codonFile\">\n            {{ scope.row.codonFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"Centromere\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.centromereFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'centromere')\"\n                :title=\"scope.row.centromereFile\">\n            {{ scope.row.centromereFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"TEs\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.tesFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'TEs')\"\n                :title=\"scope.row.tesFile\">\n            {{ scope.row.tesFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"CoreBlocks\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.coreBlocksFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'coreBlocks')\"\n                :title=\"scope.row.coreBlocksFile\">\n            {{ scope.row.coreBlocksFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"miRNA\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.miRNAFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'miRNA')\"\n                :title=\"scope.row.miRNAFile\">\n            {{ scope.row.miRNAFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"tRNA\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.tRNAFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'tRNA')\"\n                :title=\"scope.row.tRNAFile\">\n            {{ scope.row.tRNAFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"rRNA\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.rRNAFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'rRNA')\"\n                :title=\"scope.row.rRNAFile\">\n            {{ scope.row.rRNAFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"操作\" width=\"150\" fixed=\"right\">\n        <template #default=\"scope\">\n          <el-button type=\"primary\" size=\"small\" @click=\"editRow(scope.row)\">编辑</el-button>\n          <el-button type=\"danger\" size=\"small\" @click=\"deleteRow(scope.row)\">删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- 分页 -->\n    <div class=\"pagination-container\">\n      <el-pagination\n        v-model:current-page=\"currentPage\"\n        v-model:page-size=\"pageSize\"\n        :page-sizes=\"[20, 50, 100]\"\n        :total=\"totalCount\"\n        layout=\"total, sizes, prev, pager, next, jumper\"\n        @size-change=\"handleSizeChange\"\n        @current-change=\"handleCurrentChange\"\n      />\n    </div>\n\n    <!-- 新增/编辑对话框 -->\n    <el-dialog\n      v-model=\"showAddDialog\"\n      :title=\"editingRow ? '编辑 Accession' : '新增 Accession'\"\n      width=\"600px\"\n      @close=\"resetForm\">\n      \n      <el-form :model=\"formData\" :rules=\"formRules\" ref=\"formRef\" label-width=\"120px\">\n        <el-form-item label=\"Accession\" prop=\"accession\">\n          <el-input v-model=\"formData.accession\" :disabled=\"editingRow\" />\n        </el-form-item>\n        \n        <el-form-item label=\"SubPopulation\" prop=\"subPopulation\">\n          <el-select\n            v-model=\"formData.subPopulation\"\n            placeholder=\"请选择或输入亚群\"\n            filterable\n            allow-create\n            default-first-option\n            :reserve-keyword=\"false\"\n            @change=\"handleSubPopulationChange\">\n            <el-option\n              v-for=\"option in subPopulationOptions\"\n              :key=\"option.value\"\n              :label=\"option.label\"\n              :value=\"option.value\" />\n          </el-select>\n        </el-form-item>\n        \n        <el-form-item label=\"SeqData URL\" prop=\"seqData\">\n          <el-input v-model=\"formData.seqData\" placeholder=\"请输入SeqData链接，留空则为 -\" />\n        </el-form-item>\n        \n        <el-form-item label=\"经度\" prop=\"longitude\">\n          <el-input-number v-model=\"formData.longitude\" :precision=\"2\" placeholder=\"经度\" />\n        </el-form-item>\n        \n        <el-form-item label=\"纬度\" prop=\"latitude\">\n          <el-input-number v-model=\"formData.latitude\" :precision=\"2\" placeholder=\"纬度\" />\n        </el-form-item>\n\n        <!-- 文件管理部分 -->\n        <el-divider content-position=\"left\">文件管理</el-divider>\n\n        <div class=\"file-management-grid\">\n          <!-- Genome -->\n          <div class=\"file-item\">\n            <label>Genome:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.genome\" class=\"current-file\">{{ formData.files.genome }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('genome')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.genome\" size=\"small\" type=\"danger\" @click=\"removeFile('genome')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- Annotation -->\n          <div class=\"file-item\">\n            <label>Annotation:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.annotation\" class=\"current-file\">{{ formData.files.annotation }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('annotation')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.annotation\" size=\"small\" type=\"danger\" @click=\"removeFile('annotation')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- Transcriptome.all -->\n          <div class=\"file-item\">\n            <label>Transcriptome.all:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.transcriptomeAll\" class=\"current-file\">{{ formData.files.transcriptomeAll }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('transcriptomeAll')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.transcriptomeAll\" size=\"small\" type=\"danger\" @click=\"removeFile('transcriptomeAll')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- Transcriptome.leaf -->\n          <div class=\"file-item\">\n            <label>Transcriptome.leaf:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.transcriptomeLeaf\" class=\"current-file\">{{ formData.files.transcriptomeLeaf }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('transcriptomeLeaf')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.transcriptomeLeaf\" size=\"small\" type=\"danger\" @click=\"removeFile('transcriptomeLeaf')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- Transcriptome.panicles -->\n          <div class=\"file-item\">\n            <label>Transcriptome.panicles:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.transcriptomePanicles\" class=\"current-file\">{{ formData.files.transcriptomePanicles }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('transcriptomePanicles')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.transcriptomePanicles\" size=\"small\" type=\"danger\" @click=\"removeFile('transcriptomePanicles')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- Transcriptome.shoot -->\n          <div class=\"file-item\">\n            <label>Transcriptome.shoot:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.transcriptomeShoot\" class=\"current-file\">{{ formData.files.transcriptomeShoot }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('transcriptomeShoot')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.transcriptomeShoot\" size=\"small\" type=\"danger\" @click=\"removeFile('transcriptomeShoot')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- Transcriptome.stem -->\n          <div class=\"file-item\">\n            <label>Transcriptome.stem:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.transcriptomeStem\" class=\"current-file\">{{ formData.files.transcriptomeStem }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('transcriptomeStem')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.transcriptomeStem\" size=\"small\" type=\"danger\" @click=\"removeFile('transcriptomeStem')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- Transcriptome.root -->\n          <div class=\"file-item\">\n            <label>Transcriptome.root:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.transcriptomeRoot\" class=\"current-file\">{{ formData.files.transcriptomeRoot }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('transcriptomeRoot')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.transcriptomeRoot\" size=\"small\" type=\"danger\" @click=\"removeFile('transcriptomeRoot')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- Codon -->\n          <div class=\"file-item\">\n            <label>Codon:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.codon\" class=\"current-file\">{{ formData.files.codon }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('codon')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.codon\" size=\"small\" type=\"danger\" @click=\"removeFile('codon')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- Centromere -->\n          <div class=\"file-item\">\n            <label>Centromere:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.centromere\" class=\"current-file\">{{ formData.files.centromere }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('centromere')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.centromere\" size=\"small\" type=\"danger\" @click=\"removeFile('centromere')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- TEs -->\n          <div class=\"file-item\">\n            <label>TEs:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.TEs\" class=\"current-file\">{{ formData.files.TEs }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('TEs')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.TEs\" size=\"small\" type=\"danger\" @click=\"removeFile('TEs')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- CoreBlocks -->\n          <div class=\"file-item\">\n            <label>CoreBlocks:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.coreBlocks\" class=\"current-file\">{{ formData.files.coreBlocks }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('coreBlocks')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.coreBlocks\" size=\"small\" type=\"danger\" @click=\"removeFile('coreBlocks')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- miRNA -->\n          <div class=\"file-item\">\n            <label>miRNA:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.miRNA\" class=\"current-file\">{{ formData.files.miRNA }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('miRNA')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.miRNA\" size=\"small\" type=\"danger\" @click=\"removeFile('miRNA')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- tRNA -->\n          <div class=\"file-item\">\n            <label>tRNA:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.tRNA\" class=\"current-file\">{{ formData.files.tRNA }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('tRNA')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.tRNA\" size=\"small\" type=\"danger\" @click=\"removeFile('tRNA')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- rRNA -->\n          <div class=\"file-item\">\n            <label>rRNA:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.rRNA\" class=\"current-file\">{{ formData.files.rRNA }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('rRNA')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.rRNA\" size=\"small\" type=\"danger\" @click=\"removeFile('rRNA')\">删除</el-button>\n            </div>\n          </div>\n        </div>\n      </el-form>\n      \n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"showAddDialog = false\">取消</el-button>\n          <el-button type=\"primary\" @click=\"saveData\" :loading=\"saving\">保存</el-button>\n        </span>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, onMounted } from 'vue'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport { Plus, Refresh, Download, Upload, Delete, Document } from '@element-plus/icons-vue'\nimport axios from 'axios'\n\nexport default {\n  name: 'AdminDataManager',\n  components: {\n    Plus,\n    Refresh,\n    Download,\n    Upload,\n    Delete,\n    Document\n  },\n  setup() {\n    const loading = ref(false)\n    const saving = ref(false)\n    const tableData = ref([])\n    const currentPage = ref(1)\n    const pageSize = ref(20)\n    const totalCount = ref(0)\n\n    const showAddDialog = ref(false)\n    const editingRow = ref(null)\n    const formRef = ref(null)\n\n    // 搜索和筛选相关\n    const searchAccession = ref('')\n    const selectedSubPopulations = ref([])\n    const allAccessionOptions = ref([])\n    const loadingAccessions = ref(false)\n\n    // 多选相关\n    const selectedRows = ref([])\n\n    const subPopulationOptions = ref([\n      { label: 'cA', value: 'cA' },\n      { label: 'cB', value: 'cB' },\n      { label: 'GJ', value: 'GJ' },\n      { label: 'XI', value: 'XI' },\n      { label: 'WILD', value: 'WILD' },\n      { label: 'O.glaberrima', value: 'O.glaberrima' },\n      { label: '未知', value: '-' }\n    ])\n    \n    const formData = reactive({\n      accession: '',\n      subPopulation: '',\n      seqData: '',\n      longitude: null,\n      latitude: null,\n      files: {\n        genome: '',\n        annotation: '',\n        transcriptomeAll: '',\n        transcriptomeLeaf: '',\n        transcriptomePanicles: '',\n        transcriptomeShoot: '',\n        transcriptomeStem: '',\n        transcriptomeRoot: '',\n        codon: '',\n        centromere: '',\n        TEs: '',\n        coreBlocks: '',\n        miRNA: '',\n        tRNA: '',\n        rRNA: ''\n      },\n      filesToUpload: {} // 存储待上传的文件\n    })\n    \n    const formRules = {\n      accession: [\n        { required: true, message: '请输入Accession', trigger: 'blur' }\n      ]\n    }\n\n    // 加载亚群选项\n    const loadSubPopulationOptions = async () => {\n      try {\n        const response = await axios.get('/admin/data-management/list/', {\n          params: { page: 1, page_size: 1000 } // 获取所有数据来提取亚群\n        })\n\n        if (response.data.success && response.data.data) {\n          // 提取所有唯一的亚群\n          const existingSubPopulations = new Set()\n          response.data.data.forEach(item => {\n            if (item.subPopulation && item.subPopulation !== '-') {\n              existingSubPopulations.add(item.subPopulation)\n            }\n          })\n\n          // 合并默认选项和已存在的亚群\n          const defaultOptions = [\n            { label: 'cA', value: 'cA' },\n            { label: 'cB', value: 'cB' },\n            { label: 'GJ', value: 'GJ' },\n            { label: 'XI', value: 'XI' },\n            { label: 'WILD', value: 'WILD' },\n            { label: 'O.glaberrima', value: 'O.glaberrima' },\n            { label: '未知', value: '-' }\n          ]\n\n          const defaultValues = new Set(defaultOptions.map(opt => opt.value))\n          const additionalOptions = Array.from(existingSubPopulations)\n            .filter(value => !defaultValues.has(value))\n            .map(value => ({ label: value, value: value }))\n\n          subPopulationOptions.value = [...defaultOptions, ...additionalOptions]\n        }\n      } catch (error) {\n        console.error('加载亚群选项失败:', error)\n      }\n    }\n\n    // 加载数据\n    const loadData = async () => {\n      try {\n        loading.value = true\n        const params = {\n          page: currentPage.value,\n          page_size: pageSize.value\n        }\n\n        // 添加搜索参数\n        if (searchAccession.value) {\n          params.search = searchAccession.value\n        }\n\n        // 添加亚群筛选参数\n        if (selectedSubPopulations.value.length > 0) {\n          params.sub_populations = selectedSubPopulations.value.join(',')\n        }\n\n        const response = await axios.get('/admin/data-management/list/', { params })\n        const data = response.data\n\n        if (data.success) {\n          tableData.value = data.data || []\n          totalCount.value = data.total || 0\n\n          // 调试信息：检查第一条记录的文件状态\n          if (data.data && data.data.length > 0) {\n            console.log('第一条记录的文件状态:', data.data[0])\n          }\n        } else {\n          throw new Error(data.message || '获取数据失败')\n        }\n\n      } catch (error) {\n        console.error('加载数据失败:', error)\n        ElMessage.error('加载数据失败')\n      } finally {\n        loading.value = false\n      }\n    }\n\n    // 刷新数据\n    const refreshData = () => {\n      loadData()\n    }\n\n    // 处理亚群变化（表单中的）\n    const handleSubPopulationChange = (value) => {\n      // 如果是新输入的亚群，添加到选项列表中\n      if (value && !subPopulationOptions.value.find(opt => opt.value === value)) {\n        subPopulationOptions.value.push({\n          label: value,\n          value: value\n        })\n      }\n    }\n\n    // 加载所有Accession选项\n    const loadAllAccessions = async () => {\n      try {\n        loadingAccessions.value = true\n        const response = await axios.get('/admin/data-management/list/', {\n          params: {\n            page: 1,\n            page_size: 1000  // 获取所有数据\n          }\n        })\n\n        if (response.data.success) {\n          allAccessionOptions.value = response.data.data.map(item => item.accession).sort()\n        }\n      } catch (error) {\n        console.error('加载Accession选项失败:', error)\n      } finally {\n        loadingAccessions.value = false\n      }\n    }\n\n    // 处理Accession选择变化\n    const handleAccessionChange = (value) => {\n      searchAccession.value = value\n      currentPage.value = 1\n      loadData()\n    }\n\n    // 清除Accession搜索\n    const handleAccessionClear = () => {\n      searchAccession.value = ''\n      currentPage.value = 1\n      loadData()\n    }\n\n    // 处理SubPopulation筛选变化\n    const handleSubPopulationFilterChange = () => {\n      currentPage.value = 1\n      loadData()\n    }\n\n    // 重置筛选\n    const resetFilters = () => {\n      searchAccession.value = ''\n      selectedSubPopulations.value = []\n      currentPage.value = 1\n      loadData()\n    }\n\n    // 处理表格选择变化\n    const handleSelectionChange = (selection) => {\n      selectedRows.value = selection\n    }\n\n    // 批量删除\n    const handleBatchDelete = async () => {\n      if (selectedRows.value.length === 0) {\n        ElMessage.warning('请先选择要删除的记录')\n        return\n      }\n\n      try {\n        const accessions = selectedRows.value.map(row => row.accession)\n        const message = `确定要删除选中的 ${accessions.length} 个 Accession 吗？\\n\\n${accessions.join(', ')}\\n\\n此操作将同时删除相关的所有数据文件，且无法恢复！`\n\n        await ElMessageBox.confirm(message, '批量删除确认', {\n          confirmButtonText: '确定删除',\n          cancelButtonText: '取消',\n          type: 'warning',\n          dangerouslyUseHTMLString: false\n        })\n\n        // 调用批量删除API\n        const response = await axios.post('/admin/data-management/batch-delete/', {\n          accessions: accessions\n        })\n\n        if (response.data.success) {\n          ElMessage.success(`成功删除 ${accessions.length} 个 Accession`)\n          selectedRows.value = [] // 清空选择\n          loadData()\n          loadAllAccessions() // 重新加载Accession选项\n        } else {\n          ElMessage.error(response.data.message || '批量删除失败')\n        }\n\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('批量删除失败:', error)\n          ElMessage.error('批量删除失败')\n        }\n      }\n    }\n\n    // 更新数据（重新扫描文件）\n    const handleUpdateData = async () => {\n      try {\n        loading.value = true\n        const response = await axios.post('/admin/rescan/')\n\n        if (response.data.success) {\n          ElMessage.success(response.data.message)\n          loadData()\n        } else {\n          ElMessage.error(response.data.message)\n        }\n      } catch (error) {\n        console.error('更新数据失败:', error)\n        ElMessage.error('更新数据失败')\n      } finally {\n        loading.value = false\n      }\n    }\n\n    // 分页处理\n    const handleSizeChange = (size) => {\n      pageSize.value = size\n      currentPage.value = 1\n      loadData()\n    }\n\n    const handleCurrentChange = (page) => {\n      currentPage.value = page\n      loadData()\n    }\n\n    // 编辑行\n    const editRow = (row) => {\n      editingRow.value = row\n      formData.accession = row.accession\n      formData.subPopulation = row.subPopulation || ''\n      formData.seqData = row.seqData === '-' ? '' : (row.seqData || '')\n      formData.longitude = row.longitude\n      formData.latitude = row.latitude\n\n      // 加载文件信息\n      formData.files.genome = row.genomeFile || ''\n      formData.files.annotation = row.annotationFile || ''\n      formData.files.transcriptomeAll = row.transcriptomeAllFile || ''\n      formData.files.transcriptomeLeaf = row.transcriptomeLeafFile || ''\n      formData.files.transcriptomePanicles = row.transcriptomePaniclesFile || ''\n      formData.files.transcriptomeShoot = row.transcriptomeShootFile || ''\n      formData.files.transcriptomeStem = row.transcriptomeStemFile || ''\n      formData.files.transcriptomeRoot = row.transcriptomeRootFile || ''\n      formData.files.codon = row.codonFile || ''\n      formData.files.centromere = row.centromereFile || ''\n      formData.files.TEs = row.tesFile || ''\n      formData.files.coreBlocks = row.coreBlocksFile || ''\n      formData.files.miRNA = row.miRNAFile || ''\n      formData.files.tRNA = row.tRNAFile || ''\n      formData.files.rRNA = row.rRNAFile || ''\n\n      formData.filesToUpload = {} // 清空待上传文件\n      showAddDialog.value = true\n    }\n\n    // 删除行\n    const deleteRow = async (row) => {\n      try {\n        await ElMessageBox.confirm(\n          `确定要删除 Accession \"${row.accession}\" 吗？这将删除该条目的所有相关数据。`,\n          '确认删除',\n          {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }\n        )\n        \n        // 调用删除API\n        await axios.delete(`/admin/data-management/accession/${row.accession}/delete/`)\n        \n        ElMessage.success('删除成功')\n        loadData()\n        loadAllAccessions() // 重新加载Accession选项\n        \n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('删除失败:', error)\n          ElMessage.error('删除失败')\n        }\n      }\n    }\n\n    // 保存数据\n    const saveData = async () => {\n      try {\n        await formRef.value.validate()\n\n        saving.value = true\n\n        const data = {\n          accession: formData.accession,\n          subPopulation: formData.subPopulation || '-',\n          seqData: formData.seqData || '-',\n          longitude: formData.longitude,\n          latitude: formData.latitude\n        }\n\n        // 先保存基本信息\n        if (editingRow.value) {\n          // 编辑\n          await axios.put(`/admin/data-management/accession/${editingRow.value.accession}/update/`, data)\n        } else {\n          // 新增\n          await axios.post('/admin/data-management/accession/', data)\n        }\n\n        // 处理文件上传\n        const accession = formData.accession\n\n        // 文件类型映射：前端字段名 -> API参数\n        const fileTypeMapping = {\n          'genome': 'genome',\n          'annotation': 'annotation',\n          'transcriptomeAll': 'transcriptome.all',\n          'transcriptomeLeaf': 'transcriptome.leaf',\n          'transcriptomePanicles': 'transcriptome.panicles',\n          'transcriptomeShoot': 'transcriptome.shoot',\n          'transcriptomeStem': 'transcriptome.stem',\n          'transcriptomeRoot': 'transcriptome.root',\n          'codon': 'codon',\n          'centromere': 'centromere',\n          'TEs': 'TEs',\n          'coreBlocks': 'coreBlocks',\n          'miRNA': 'miRNA',\n          'tRNA': 'tRNA',\n          'rRNA': 'rRNA'\n        }\n\n        for (const [frontendFileType, file] of Object.entries(formData.filesToUpload)) {\n          if (file) {\n            try {\n              const apiFileType = fileTypeMapping[frontendFileType] || frontendFileType\n              const fileFormData = new FormData()\n              fileFormData.append('file', file)\n              fileFormData.append('accession', accession)\n              fileFormData.append('fileType', apiFileType)\n\n              await axios.post('/admin/data-management/upload-file/', fileFormData, {\n                headers: {\n                  'Content-Type': 'multipart/form-data'\n                }\n              })\n            } catch (fileError) {\n              console.error(`文件 ${frontendFileType} 上传失败:`, fileError)\n              ElMessage.warning(`文件 ${frontendFileType} 上传失败`)\n            }\n          }\n        }\n\n        // 处理文件删除（如果文件名被清空但原来有文件）\n        if (editingRow.value) {\n          const fileTypeMappings = [\n            { formKey: 'genome', apiKey: 'genome', originalKey: 'genomeFile' },\n            { formKey: 'annotation', apiKey: 'annotation', originalKey: 'annotationFile' },\n            { formKey: 'transcriptomeAll', apiKey: 'transcriptome.all', originalKey: 'transcriptomeAllFile' },\n            { formKey: 'transcriptomeLeaf', apiKey: 'transcriptome.leaf', originalKey: 'transcriptomeLeafFile' },\n            { formKey: 'transcriptomePanicles', apiKey: 'transcriptome.panicles', originalKey: 'transcriptomePaniclesFile' },\n            { formKey: 'transcriptomeShoot', apiKey: 'transcriptome.shoot', originalKey: 'transcriptomeShootFile' },\n            { formKey: 'transcriptomeStem', apiKey: 'transcriptome.stem', originalKey: 'transcriptomeStemFile' },\n            { formKey: 'transcriptomeRoot', apiKey: 'transcriptome.root', originalKey: 'transcriptomeRootFile' },\n            { formKey: 'codon', apiKey: 'codon', originalKey: 'codonFile' },\n            { formKey: 'centromere', apiKey: 'centromere', originalKey: 'centromereFile' },\n            { formKey: 'TEs', apiKey: 'TEs', originalKey: 'tesFile' },\n            { formKey: 'coreBlocks', apiKey: 'coreBlocks', originalKey: 'coreBlocksFile' },\n            { formKey: 'miRNA', apiKey: 'miRNA', originalKey: 'miRNAFile' },\n            { formKey: 'tRNA', apiKey: 'tRNA', originalKey: 'tRNAFile' },\n            { formKey: 'rRNA', apiKey: 'rRNA', originalKey: 'rRNAFile' }\n          ]\n\n          for (const mapping of fileTypeMappings) {\n            const originalFile = editingRow.value[mapping.originalKey]\n            const currentFile = formData.files[mapping.formKey]\n\n            // 如果原来有文件，现在没有，且没有新上传的文件，则删除\n            if (originalFile && !currentFile && !formData.filesToUpload[mapping.formKey]) {\n              try {\n                await axios.delete(`/admin/data-management/delete-file/${accession}/${mapping.apiKey}/`)\n              } catch (deleteError) {\n                console.error(`文件 ${mapping.apiKey} 删除失败:`, deleteError)\n              }\n            }\n          }\n        }\n\n        ElMessage.success(editingRow.value ? '更新成功' : '新增成功')\n        showAddDialog.value = false\n        loadData()\n        loadSubPopulationOptions() // 重新加载亚群选项\n        loadAllAccessions() // 重新加载Accession选项\n\n      } catch (error) {\n        console.error('保存失败:', error)\n        ElMessage.error('保存失败')\n      } finally {\n        saving.value = false\n      }\n    }\n\n    // 重置表单\n    const resetForm = () => {\n      editingRow.value = null\n      formData.accession = ''\n      formData.subPopulation = ''\n      formData.seqData = ''\n      formData.longitude = null\n      formData.latitude = null\n\n      // 重置文件信息\n      formData.files = {\n        genome: '',\n        annotation: '',\n        transcriptomeAll: '',\n        transcriptomeLeaf: '',\n        transcriptomePanicles: '',\n        transcriptomeShoot: '',\n        transcriptomeStem: '',\n        transcriptomeRoot: '',\n        codon: '',\n        centromere: '',\n        TEs: '',\n        coreBlocks: '',\n        miRNA: '',\n        tRNA: '',\n        rRNA: ''\n      }\n      formData.filesToUpload = {}\n\n      if (formRef.value) {\n        formRef.value.clearValidate()\n      }\n    }\n\n    // 文件选择方法（用于编辑对话框）\n    const selectFile = (fileType) => {\n      const input = document.createElement('input')\n      input.type = 'file'\n\n      // 根据文件类型设置接受的文件格式\n      const fileExtensions = {\n        'genome': '.fasta,.fa,.fas',\n        'annotation': '.gff,.gff3',\n        'transcriptomeAll': '.tar.gz',\n        'transcriptomeLeaf': '.tar.gz',\n        'transcriptomePanicles': '.tar.gz',\n        'transcriptomeShoot': '.tar.gz',\n        'transcriptomeStem': '.tar.gz',\n        'transcriptomeRoot': '.tar.gz',\n        'codon': '.tar.gz',\n        'centromere': '.bed',\n        'TEs': '.tar.gz',\n        'coreBlocks': '.bed',\n        'miRNA': '.bed',\n        'tRNA': '.bed',\n        'rRNA': '.bed'\n      }\n\n      input.accept = fileExtensions[fileType] || '*'\n\n      input.onchange = (event) => {\n        const file = event.target.files[0]\n        if (file) {\n          formData.files[fileType] = file.name\n          formData.filesToUpload[fileType] = file\n        }\n      }\n\n      input.click()\n    }\n\n    // 移除文件方法（用于编辑对话框）\n    const removeFile = (fileType) => {\n      formData.files[fileType] = ''\n      delete formData.filesToUpload[fileType]\n    }\n\n    // 文件操作方法（用于表格中的下载）\n    const uploadFile = (accession, fileType) => {\n      // 创建文件输入元素\n      const input = document.createElement('input')\n      input.type = 'file'\n\n      // 根据文件类型设置接受的文件格式\n      const fileExtensions = {\n        'genome': '.fasta,.fa,.fas',\n        'annotation': '.gff,.gff3',\n        'transcriptome.all': '.tar.gz',\n        'transcriptome.leaf': '.tar.gz',\n        'transcriptome.panicles': '.tar.gz',\n        'transcriptome.shoot': '.tar.gz',\n        'transcriptome.stem': '.tar.gz',\n        'transcriptome.root': '.tar.gz',\n        'codon': '.tar.gz',\n        'centromere': '.bed',\n        'TEs': '.tar.gz',\n        'coreBlocks': '.bed',\n        'miRNA': '.bed',\n        'tRNA': '.bed',\n        'rRNA': '.bed'\n      }\n\n      input.accept = fileExtensions[fileType] || '*'\n\n      input.onchange = async (event) => {\n        const file = event.target.files[0]\n        if (!file) return\n\n        try {\n          const formData = new FormData()\n          formData.append('file', file)\n          formData.append('accession', accession)\n          formData.append('fileType', fileType)\n\n          const response = await axios.post('/admin/data-management/upload-file/', formData, {\n            headers: {\n              'Content-Type': 'multipart/form-data'\n            }\n          })\n\n          if (response.data.success) {\n            ElMessage.success(`${fileType} 文件上传成功`)\n            loadData() // 刷新数据\n          } else {\n            ElMessage.error(response.data.message || '上传失败')\n          }\n        } catch (error) {\n          console.error('文件上传失败:', error)\n          ElMessage.error('文件上传失败')\n        }\n      }\n\n      input.click()\n    }\n\n    const downloadFile = async (accession, fileType) => {\n      try {\n        const response = await axios.get(`/admin/data-management/download-file/${accession}/${fileType}/`, {\n          responseType: 'blob'\n        })\n\n        // 创建下载链接\n        const url = window.URL.createObjectURL(new Blob([response.data]))\n        const link = document.createElement('a')\n        link.href = url\n\n        // 根据文件类型设置文件名\n        const extensions = {\n          'genome': 'fasta',\n          'annotation': 'gff',\n          'transcriptome': 'tar.gz',\n          'codon': 'tar.gz',\n          'centromere': 'bed',\n          'TEs': 'tar.gz',\n          'coreBlocks': 'bed',\n          'miRNA': 'bed',\n          'tRNA': 'bed',\n          'rRNA': 'bed'\n        }\n\n        link.download = `${fileType}.${accession}.${extensions[fileType]}`\n        document.body.appendChild(link)\n        link.click()\n        document.body.removeChild(link)\n        window.URL.revokeObjectURL(url)\n\n      } catch (error) {\n        console.error('文件下载失败:', error)\n        ElMessage.error('文件下载失败')\n      }\n    }\n\n    const deleteFile = async (accession, fileType) => {\n      try {\n        await ElMessageBox.confirm(\n          `确定要删除 ${accession} 的 ${fileType} 文件吗？`,\n          '确认删除',\n          {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }\n        )\n\n        const response = await axios.delete(`/admin/data-management/delete-file/${accession}/${fileType}/`)\n\n        if (response.data.success) {\n          ElMessage.success(`${fileType} 文件删除成功`)\n          loadData() // 刷新数据\n        } else {\n          ElMessage.error(response.data.message || '删除失败')\n        }\n\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('文件删除失败:', error)\n          ElMessage.error('文件删除失败')\n        }\n      }\n    }\n\n    onMounted(() => {\n      loadData()\n      loadSubPopulationOptions()\n      loadAllAccessions()\n    })\n\n    return {\n      loading,\n      saving,\n      tableData,\n      currentPage,\n      pageSize,\n      totalCount,\n      showAddDialog,\n      editingRow,\n      formRef,\n      formData,\n      formRules,\n      subPopulationOptions,\n      // 搜索和筛选相关\n      searchAccession,\n      selectedSubPopulations,\n      allAccessionOptions,\n      loadingAccessions,\n      loadAllAccessions,\n      handleAccessionChange,\n      handleAccessionClear,\n      handleSubPopulationFilterChange,\n      resetFilters,\n      // 多选相关\n      selectedRows,\n      handleSelectionChange,\n      handleBatchDelete,\n      // 原有方法\n      loadData,\n      refreshData,\n      handleUpdateData,\n      handleSizeChange,\n      handleCurrentChange,\n      editRow,\n      deleteRow,\n      saveData,\n      resetForm,\n      selectFile,\n      removeFile,\n      uploadFile,\n      downloadFile,\n      deleteFile\n    }\n  }\n}\n</script>\n\n<style scoped>\n.admin-data-manager {\n  padding: 20px;\n}\n\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.page-header h2 {\n  margin: 0;\n  color: #1a56db;\n}\n\n.header-actions {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  gap: 15px;\n  width: 100%;\n}\n\n.stats-info {\n  color: #666;\n  font-size: 14px;\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.stats-info span {\n  white-space: nowrap;\n}\n\n.selected-info {\n  color: #409eff;\n  font-weight: 500;\n}\n\n.action-buttons {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n/* 筛选区域样式 */\n.filter-section {\n  background: #f8f9fa;\n  padding: 20px;\n  border-radius: 8px;\n  margin-bottom: 20px;\n  border: 1px solid #e9ecef;\n}\n\n.filter-row {\n  display: flex;\n  align-items: center;\n  gap: 20px;\n  flex-wrap: wrap;\n}\n\n.filter-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.filter-item label {\n  font-weight: 500;\n  color: #495057;\n  white-space: nowrap;\n  min-width: fit-content;\n}\n\n.file-link {\n  color: #1a56db;\n  cursor: pointer;\n  text-decoration: none;\n  word-break: break-all;\n  font-size: 12px;\n}\n\n.file-link:hover {\n  text-decoration: underline;\n}\n\n.file-management-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 15px;\n  margin-top: 10px;\n}\n\n.file-item {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.file-item label {\n  font-weight: bold;\n  color: #333;\n  font-size: 14px;\n}\n\n.file-controls {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  flex-wrap: wrap;\n}\n\n.current-file {\n  color: #67c23a;\n  font-size: 12px;\n  word-break: break-all;\n  flex: 1;\n  min-width: 0;\n}\n\n.no-file {\n  color: #999;\n  font-size: 12px;\n}\n\n.data-link {\n  color: #1a56db;\n  text-decoration: none;\n  word-break: break-all;\n}\n\n.data-link:hover {\n  text-decoration: underline;\n}\n\n.data-empty {\n  color: #999;\n}\n\n.pagination-container {\n  margin-top: 20px;\n  display: flex;\n  justify-content: center;\n}\n\n.dialog-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 10px;\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAa;;EAEjBA,KAAK,EAAC;AAAc;;;;;;EAKcA,KAAK,EAAC;;;EAO1CA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAa;;EAmBnBA,KAAK,EAAC;AAAa;;EAoBnBA,KAAK,EAAC;AAAa;;;;EAgCTA,KAAK,EAAC;;;;;EAeNA,KAAK,EAAC;;;;;EAYNA,KAAK,EAAC;;;;;EAYNA,KAAK,EAAC;;;;;EAYNA,KAAK,EAAC;;;;;EAYNA,KAAK,EAAC;;;;;EAYNA,KAAK,EAAC;;;;;EAYNA,KAAK,EAAC;;;;;EAYNA,KAAK,EAAC;;;;;EAYNA,KAAK,EAAC;;;;;EAYNA,KAAK,EAAC;;;;;EAYNA,KAAK,EAAC;;;;;EAYNA,KAAK,EAAC;;;;;EAYNA,KAAK,EAAC;;;;;EAYNA,KAAK,EAAC;;;;;EAYNA,KAAK,EAAC;;;EAapBA,KAAK,EAAC;AAAsB;;EAwDxBA,KAAK,EAAC;AAAsB;;EAE1BA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAe;;;EACWA,KAAK,EAAC;;;;EAC5BA,KAAK,EAAC;;;EAOlBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAe;;;EACeA,KAAK,EAAC;;;;EAChCA,KAAK,EAAC;;;EAOlBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAe;;;EACqBA,KAAK,EAAC;;;;EACtCA,KAAK,EAAC;;;EAOlBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAe;;;EACsBA,KAAK,EAAC;;;;EACvCA,KAAK,EAAC;;;EAOlBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAe;;;EAC0BA,KAAK,EAAC;;;;EAC3CA,KAAK,EAAC;;;EAOlBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAe;;;EACuBA,KAAK,EAAC;;;;EACxCA,KAAK,EAAC;;;EAOlBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAe;;;EACsBA,KAAK,EAAC;;;;EACvCA,KAAK,EAAC;;;EAOlBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAe;;;EACsBA,KAAK,EAAC;;;;EACvCA,KAAK,EAAC;;;EAOlBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAe;;;EACUA,KAAK,EAAC;;;;EAC3BA,KAAK,EAAC;;;EAOlBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAe;;;EACeA,KAAK,EAAC;;;;EAChCA,KAAK,EAAC;;;EAOlBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAe;;;EACQA,KAAK,EAAC;;;;EACzBA,KAAK,EAAC;;;EAOlBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAe;;;EACeA,KAAK,EAAC;;;;EAChCA,KAAK,EAAC;;;EAOlBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAe;;;EACUA,KAAK,EAAC;;;;EAC3BA,KAAK,EAAC;;;EAOlBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAe;;;EACSA,KAAK,EAAC;;;;EAC1BA,KAAK,EAAC;;;EAOlBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAe;;;EACSA,KAAK,EAAC;;;;EAC1BA,KAAK,EAAC;;;EASnBA,KAAK,EAAC;AAAe;;;;;;;;;;;;;;;;;uBA9fjCC,mBAAA,CAogBM,OApgBNC,UAogBM,GAngBJC,mBAAA,CAWM,OAXNC,UAWM,G,4BAVJD,mBAAA,CAAe,YAAX,QAAM,qBACVA,mBAAA,CAQM,OARNE,UAQM,GAPJF,mBAAA,CAAqC,cAA/B,MAAI,GAAAG,gBAAA,CAAGC,MAAA,CAAAC,UAAU,IAAG,MAAI,iBAClBD,MAAA,CAAAE,SAAS,CAACC,MAAM,Q,cAA5BT,mBAAA,CAEO,QAAAU,UAAA,EAF2B,SAC1B,GAAAL,gBAAA,CAAGC,MAAA,CAAAE,SAAS,CAACC,MAAM,IAAG,MAC9B,mB,mCACYH,MAAA,CAAAK,YAAY,CAACF,MAAM,Q,cAA/BT,mBAAA,CAEO,QAFPY,UAEO,EAFoD,QACpD,GAAAP,gBAAA,CAAGC,MAAA,CAAAK,YAAY,CAACF,MAAM,IAAG,KAChC,mB,uCAIJI,mBAAA,aAAgB,EAChBX,mBAAA,CAiDM,OAjDNY,UAiDM,GAhDJZ,mBAAA,CA+CM,OA/CNa,UA+CM,GA9CJF,mBAAA,kBAAqB,EACrBX,mBAAA,CAgBM,OAhBNc,UAgBM,G,4BAfJd,mBAAA,CAA2B,eAApB,cAAY,qBACnBe,YAAA,CAaYC,oBAAA;gBAZDZ,MAAA,CAAAa,eAAe;+DAAfb,MAAA,CAAAa,eAAe,GAAAC,MAAA;IACxBC,WAAW,EAAC,cAAc;IAC1BC,UAAU,EAAV,EAAU;IACVC,SAAS,EAAT,EAAS;IACTC,KAAqB,EAArB;MAAA;IAAA,CAAqB;IACpBC,QAAM,EAAEnB,MAAA,CAAAoB,qBAAqB;IAC7BC,OAAK,EAAErB,MAAA,CAAAsB;;sBAEN,MAAwC,E,kBAD1C5B,mBAAA,CAIuB6B,SAAA,QAAAC,WAAA,CAHDxB,MAAA,CAAAyB,mBAAmB,EAAhCC,SAAS;2BADlBC,YAAA,CAIuBC,oBAAA;QAFpBC,GAAG,EAAEH,SAAS;QACdI,KAAK,EAAEJ,SAAS;QAChBK,KAAK,EAAEL;;;;8DAIdnB,mBAAA,qBAAwB,EACxBX,mBAAA,CAiBM,OAjBNoC,UAiBM,G,4BAhBJpC,mBAAA,CAA+B,eAAxB,kBAAgB,qBACvBe,YAAA,CAcYC,oBAAA;gBAbDZ,MAAA,CAAAiC,sBAAsB;+DAAtBjC,MAAA,CAAAiC,sBAAsB,GAAAnB,MAAA;IAC/BC,WAAW,EAAC,OAAO;IACnBmB,QAAQ,EAAR,EAAQ;IACR,eAAa,EAAb,EAAa;IACb,uBAAqB,EAArB,EAAqB;IACrBjB,SAAS,EAAT,EAAS;IACTC,KAAqB,EAArB;MAAA;IAAA,CAAqB;IACpBC,QAAM,EAAEnB,MAAA,CAAAmC;;sBAEP,MAAsC,E,kBADxCzC,mBAAA,CAI0B6B,SAAA,QAAAC,WAAA,CAHPxB,MAAA,CAAAoC,oBAAoB,EAA9BC,MAAM;2BADfV,YAAA,CAI0BC,oBAAA;QAFvBC,GAAG,EAAEQ,MAAM,CAACN,KAAK;QACjBD,KAAK,EAAEO,MAAM,CAACP,KAAK;QACnBC,KAAK,EAAEM,MAAM,CAACN;;;;mDAIrBxB,mBAAA,UAAa,EACbX,mBAAA,CAKM,OALN0C,WAKM,GAJJ3B,YAAA,CAGY4B,oBAAA;IAHAC,OAAK,EAAExC,MAAA,CAAAyC;EAAY;sBAC7B,MAA8B,CAA9B9B,YAAA,CAA8B+B,kBAAA;wBAArB,MAAW,CAAX/B,YAAA,CAAWgC,kBAAA,E;;qDAAU,QAEhC,G;;;wCAKNpC,mBAAA,UAAa,E,+BACboB,YAAA,CAuNWiB,mBAAA;IAtNRC,IAAI,EAAE7C,MAAA,CAAAE,SAAS;IAEhB4C,MAAM,EAAN,EAAM;IACNC,MAAM,EAAN,EAAM;IACN7B,KAAmB,EAAnB;MAAA;IAAA,CAAmB;IAClB,mBAAiB,EAAE;MAAA8B,UAAA;MAAAC,KAAA;MAAAC,UAAA;IAAA,CAA+D;IAClFC,iBAAgB,EAAEnD,MAAA,CAAAoD;;sBAEnB,MAAa,CAAb7C,mBAAA,UAAa,EACbI,YAAA,CAA4D0C,0BAAA;MAA3CC,IAAI,EAAC,WAAW;MAACC,KAAK,EAAC,IAAI;MAACC,KAAK,EAAC;QAEnD7C,YAAA,CAA+E0C,0BAAA;MAA9DI,IAAI,EAAC,WAAW;MAAC3B,KAAK,EAAC,WAAW;MAACyB,KAAK,EAAC,KAAK;MAACC,KAAK,EAAC;QACtE7C,YAAA,CAA0E0C,0BAAA;MAAzDI,IAAI,EAAC,eAAe;MAAC3B,KAAK,EAAC,eAAe;MAACyB,KAAK,EAAC;QAClE5C,YAAA,CAUkB0C,0BAAA;MAVDI,IAAI,EAAC,SAAS;MAAC3B,KAAK,EAAC,SAAS;MAACyB,KAAK,EAAC;;MACzCG,OAAO,EAAAC,QAAA,CAMZC,KANmB,KACdA,KAAK,CAACC,GAAG,CAACC,OAAO,IAAIF,KAAK,CAACC,GAAG,CAACC,OAAO,Y,cAA/CpE,mBAAA,CAKI;;QAJAqE,IAAI,EAAEH,KAAK,CAACC,GAAG,CAACC,OAAO;QACxBE,MAAM,EAAC,QAAQ;QACfvE,KAAK,EAAC;0BACJmE,KAAK,CAACC,GAAG,CAACC,OAAO,wBAAAG,WAAA,M,cAEtBvE,mBAAA,CAAwC,QAAxCwE,WAAwC,EAAR,GAAC,G;;QAGrCvD,YAAA,CAAkE0C,0BAAA;MAAjDI,IAAI,EAAC,WAAW;MAAC3B,KAAK,EAAC,WAAW;MAACyB,KAAK,EAAC;QAC1D5C,YAAA,CAAgE0C,0BAAA;MAA/CI,IAAI,EAAC,UAAU;MAAC3B,KAAK,EAAC,UAAU;MAACyB,KAAK,EAAC;QAExDhD,mBAAA,WAAc,EACdI,YAAA,CAUkB0C,0BAAA;MAVDvB,KAAK,EAAC,QAAQ;MAACyB,KAAK,EAAC;;MACzBG,OAAO,EAAAC,QAAA,CAMTC,KANgB,KACXA,KAAK,CAACC,GAAG,CAACM,UAAU,I,cAAhCzE,mBAAA,CAKO;;QAJDD,KAAK,EAAC,WAAW;QAChB+C,OAAK,EAAA1B,MAAA,IAAEd,MAAA,CAAAoE,YAAY,CAACR,KAAK,CAACC,GAAG,CAACnC,SAAS;QACvC2C,KAAK,EAAET,KAAK,CAACC,GAAG,CAACM;0BACnBP,KAAK,CAACC,GAAG,CAACM,UAAU,wBAAAG,WAAA,M,cAEzB5E,mBAAA,CAAwC,QAAxC6E,WAAwC,EAAR,GAAC,G;;QAIrC5D,YAAA,CAUkB0C,0BAAA;MAVDvB,KAAK,EAAC,YAAY;MAACyB,KAAK,EAAC;;MAC7BG,OAAO,EAAAC,QAAA,CAMTC,KANgB,KACXA,KAAK,CAACC,GAAG,CAACW,cAAc,I,cAApC9E,mBAAA,CAKO;;QAJDD,KAAK,EAAC,WAAW;QAChB+C,OAAK,EAAA1B,MAAA,IAAEd,MAAA,CAAAoE,YAAY,CAACR,KAAK,CAACC,GAAG,CAACnC,SAAS;QACvC2C,KAAK,EAAET,KAAK,CAACC,GAAG,CAACW;0BACnBZ,KAAK,CAACC,GAAG,CAACW,cAAc,wBAAAC,WAAA,M,cAE7B/E,mBAAA,CAAwC,QAAxCgF,WAAwC,EAAR,GAAC,G;;QAIrC/D,YAAA,CAUkB0C,0BAAA;MAVDvB,KAAK,EAAC,mBAAmB;MAACyB,KAAK,EAAC;;MACpCG,OAAO,EAAAC,QAAA,CAMTC,KANgB,KACXA,KAAK,CAACC,GAAG,CAACc,oBAAoB,I,cAA1CjF,mBAAA,CAKO;;QAJDD,KAAK,EAAC,WAAW;QAChB+C,OAAK,EAAA1B,MAAA,IAAEd,MAAA,CAAAoE,YAAY,CAACR,KAAK,CAACC,GAAG,CAACnC,SAAS;QACvC2C,KAAK,EAAET,KAAK,CAACC,GAAG,CAACc;0BACnBf,KAAK,CAACC,GAAG,CAACc,oBAAoB,wBAAAC,WAAA,M,cAEnClF,mBAAA,CAAwC,QAAxCmF,WAAwC,EAAR,GAAC,G;;QAIrClE,YAAA,CAUkB0C,0BAAA;MAVDvB,KAAK,EAAC,oBAAoB;MAACyB,KAAK,EAAC;;MACrCG,OAAO,EAAAC,QAAA,CAMTC,KANgB,KACXA,KAAK,CAACC,GAAG,CAACiB,qBAAqB,I,cAA3CpF,mBAAA,CAKO;;QAJDD,KAAK,EAAC,WAAW;QAChB+C,OAAK,EAAA1B,MAAA,IAAEd,MAAA,CAAAoE,YAAY,CAACR,KAAK,CAACC,GAAG,CAACnC,SAAS;QACvC2C,KAAK,EAAET,KAAK,CAACC,GAAG,CAACiB;0BACnBlB,KAAK,CAACC,GAAG,CAACiB,qBAAqB,wBAAAC,WAAA,M,cAEpCrF,mBAAA,CAAwC,QAAxCsF,WAAwC,EAAR,GAAC,G;;QAIrCrE,YAAA,CAUkB0C,0BAAA;MAVDvB,KAAK,EAAC,wBAAwB;MAACyB,KAAK,EAAC;;MACzCG,OAAO,EAAAC,QAAA,CAMTC,KANgB,KACXA,KAAK,CAACC,GAAG,CAACoB,yBAAyB,I,cAA/CvF,mBAAA,CAKO;;QAJDD,KAAK,EAAC,WAAW;QAChB+C,OAAK,EAAA1B,MAAA,IAAEd,MAAA,CAAAoE,YAAY,CAACR,KAAK,CAACC,GAAG,CAACnC,SAAS;QACvC2C,KAAK,EAAET,KAAK,CAACC,GAAG,CAACoB;0BACnBrB,KAAK,CAACC,GAAG,CAACoB,yBAAyB,wBAAAC,WAAA,M,cAExCxF,mBAAA,CAAwC,QAAxCyF,WAAwC,EAAR,GAAC,G;;QAIrCxE,YAAA,CAUkB0C,0BAAA;MAVDvB,KAAK,EAAC,qBAAqB;MAACyB,KAAK,EAAC;;MACtCG,OAAO,EAAAC,QAAA,CAMTC,KANgB,KACXA,KAAK,CAACC,GAAG,CAACuB,sBAAsB,I,cAA5C1F,mBAAA,CAKO;;QAJDD,KAAK,EAAC,WAAW;QAChB+C,OAAK,EAAA1B,MAAA,IAAEd,MAAA,CAAAoE,YAAY,CAACR,KAAK,CAACC,GAAG,CAACnC,SAAS;QACvC2C,KAAK,EAAET,KAAK,CAACC,GAAG,CAACuB;0BACnBxB,KAAK,CAACC,GAAG,CAACuB,sBAAsB,wBAAAC,WAAA,M,cAErC3F,mBAAA,CAAwC,QAAxC4F,WAAwC,EAAR,GAAC,G;;QAIrC3E,YAAA,CAUkB0C,0BAAA;MAVDvB,KAAK,EAAC,oBAAoB;MAACyB,KAAK,EAAC;;MACrCG,OAAO,EAAAC,QAAA,CAMTC,KANgB,KACXA,KAAK,CAACC,GAAG,CAAC0B,qBAAqB,I,cAA3C7F,mBAAA,CAKO;;QAJDD,KAAK,EAAC,WAAW;QAChB+C,OAAK,EAAA1B,MAAA,IAAEd,MAAA,CAAAoE,YAAY,CAACR,KAAK,CAACC,GAAG,CAACnC,SAAS;QACvC2C,KAAK,EAAET,KAAK,CAACC,GAAG,CAAC0B;0BACnB3B,KAAK,CAACC,GAAG,CAAC0B,qBAAqB,wBAAAC,WAAA,M,cAEpC9F,mBAAA,CAAwC,QAAxC+F,WAAwC,EAAR,GAAC,G;;QAIrC9E,YAAA,CAUkB0C,0BAAA;MAVDvB,KAAK,EAAC,oBAAoB;MAACyB,KAAK,EAAC;;MACrCG,OAAO,EAAAC,QAAA,CAMTC,KANgB,KACXA,KAAK,CAACC,GAAG,CAAC6B,qBAAqB,I,cAA3ChG,mBAAA,CAKO;;QAJDD,KAAK,EAAC,WAAW;QAChB+C,OAAK,EAAA1B,MAAA,IAAEd,MAAA,CAAAoE,YAAY,CAACR,KAAK,CAACC,GAAG,CAACnC,SAAS;QACvC2C,KAAK,EAAET,KAAK,CAACC,GAAG,CAAC6B;0BACnB9B,KAAK,CAACC,GAAG,CAAC6B,qBAAqB,wBAAAC,WAAA,M,cAEpCjG,mBAAA,CAAwC,QAAxCkG,WAAwC,EAAR,GAAC,G;;QAIrCjF,YAAA,CAUkB0C,0BAAA;MAVDvB,KAAK,EAAC,OAAO;MAACyB,KAAK,EAAC;;MACxBG,OAAO,EAAAC,QAAA,CAMTC,KANgB,KACXA,KAAK,CAACC,GAAG,CAACgC,SAAS,I,cAA/BnG,mBAAA,CAKO;;QAJDD,KAAK,EAAC,WAAW;QAChB+C,OAAK,EAAA1B,MAAA,IAAEd,MAAA,CAAAoE,YAAY,CAACR,KAAK,CAACC,GAAG,CAACnC,SAAS;QACvC2C,KAAK,EAAET,KAAK,CAACC,GAAG,CAACgC;0BACnBjC,KAAK,CAACC,GAAG,CAACgC,SAAS,wBAAAC,WAAA,M,cAExBpG,mBAAA,CAAwC,QAAxCqG,WAAwC,EAAR,GAAC,G;;QAIrCpF,YAAA,CAUkB0C,0BAAA;MAVDvB,KAAK,EAAC,YAAY;MAACyB,KAAK,EAAC;;MAC7BG,OAAO,EAAAC,QAAA,CAMTC,KANgB,KACXA,KAAK,CAACC,GAAG,CAACmC,cAAc,I,cAApCtG,mBAAA,CAKO;;QAJDD,KAAK,EAAC,WAAW;QAChB+C,OAAK,EAAA1B,MAAA,IAAEd,MAAA,CAAAoE,YAAY,CAACR,KAAK,CAACC,GAAG,CAACnC,SAAS;QACvC2C,KAAK,EAAET,KAAK,CAACC,GAAG,CAACmC;0BACnBpC,KAAK,CAACC,GAAG,CAACmC,cAAc,wBAAAC,WAAA,M,cAE7BvG,mBAAA,CAAwC,QAAxCwG,WAAwC,EAAR,GAAC,G;;QAIrCvF,YAAA,CAUkB0C,0BAAA;MAVDvB,KAAK,EAAC,KAAK;MAACyB,KAAK,EAAC;;MACtBG,OAAO,EAAAC,QAAA,CAMTC,KANgB,KACXA,KAAK,CAACC,GAAG,CAACsC,OAAO,I,cAA7BzG,mBAAA,CAKO;;QAJDD,KAAK,EAAC,WAAW;QAChB+C,OAAK,EAAA1B,MAAA,IAAEd,MAAA,CAAAoE,YAAY,CAACR,KAAK,CAACC,GAAG,CAACnC,SAAS;QACvC2C,KAAK,EAAET,KAAK,CAACC,GAAG,CAACsC;0BACnBvC,KAAK,CAACC,GAAG,CAACsC,OAAO,wBAAAC,WAAA,M,cAEtB1G,mBAAA,CAAwC,QAAxC2G,WAAwC,EAAR,GAAC,G;;QAIrC1F,YAAA,CAUkB0C,0BAAA;MAVDvB,KAAK,EAAC,YAAY;MAACyB,KAAK,EAAC;;MAC7BG,OAAO,EAAAC,QAAA,CAMTC,KANgB,KACXA,KAAK,CAACC,GAAG,CAACyC,cAAc,I,cAApC5G,mBAAA,CAKO;;QAJDD,KAAK,EAAC,WAAW;QAChB+C,OAAK,EAAA1B,MAAA,IAAEd,MAAA,CAAAoE,YAAY,CAACR,KAAK,CAACC,GAAG,CAACnC,SAAS;QACvC2C,KAAK,EAAET,KAAK,CAACC,GAAG,CAACyC;0BACnB1C,KAAK,CAACC,GAAG,CAACyC,cAAc,wBAAAC,WAAA,M,cAE7B7G,mBAAA,CAAwC,QAAxC8G,WAAwC,EAAR,GAAC,G;;QAIrC7F,YAAA,CAUkB0C,0BAAA;MAVDvB,KAAK,EAAC,OAAO;MAACyB,KAAK,EAAC;;MACxBG,OAAO,EAAAC,QAAA,CAMTC,KANgB,KACXA,KAAK,CAACC,GAAG,CAAC4C,SAAS,I,cAA/B/G,mBAAA,CAKO;;QAJDD,KAAK,EAAC,WAAW;QAChB+C,OAAK,EAAA1B,MAAA,IAAEd,MAAA,CAAAoE,YAAY,CAACR,KAAK,CAACC,GAAG,CAACnC,SAAS;QACvC2C,KAAK,EAAET,KAAK,CAACC,GAAG,CAAC4C;0BACnB7C,KAAK,CAACC,GAAG,CAAC4C,SAAS,wBAAAC,WAAA,M,cAExBhH,mBAAA,CAAwC,QAAxCiH,WAAwC,EAAR,GAAC,G;;QAIrChG,YAAA,CAUkB0C,0BAAA;MAVDvB,KAAK,EAAC,MAAM;MAACyB,KAAK,EAAC;;MACvBG,OAAO,EAAAC,QAAA,CAMTC,KANgB,KACXA,KAAK,CAACC,GAAG,CAAC+C,QAAQ,I,cAA9BlH,mBAAA,CAKO;;QAJDD,KAAK,EAAC,WAAW;QAChB+C,OAAK,EAAA1B,MAAA,IAAEd,MAAA,CAAAoE,YAAY,CAACR,KAAK,CAACC,GAAG,CAACnC,SAAS;QACvC2C,KAAK,EAAET,KAAK,CAACC,GAAG,CAAC+C;0BACnBhD,KAAK,CAACC,GAAG,CAAC+C,QAAQ,wBAAAC,WAAA,M,cAEvBnH,mBAAA,CAAwC,QAAxCoH,WAAwC,EAAR,GAAC,G;;QAIrCnG,YAAA,CAUkB0C,0BAAA;MAVDvB,KAAK,EAAC,MAAM;MAACyB,KAAK,EAAC;;MACvBG,OAAO,EAAAC,QAAA,CAMTC,KANgB,KACXA,KAAK,CAACC,GAAG,CAACkD,QAAQ,I,cAA9BrH,mBAAA,CAKO;;QAJDD,KAAK,EAAC,WAAW;QAChB+C,OAAK,EAAA1B,MAAA,IAAEd,MAAA,CAAAoE,YAAY,CAACR,KAAK,CAACC,GAAG,CAACnC,SAAS;QACvC2C,KAAK,EAAET,KAAK,CAACC,GAAG,CAACkD;0BACnBnD,KAAK,CAACC,GAAG,CAACkD,QAAQ,wBAAAC,WAAA,M,cAEvBtH,mBAAA,CAAwC,QAAxCuH,WAAwC,EAAR,GAAC,G;;QAIrCtG,YAAA,CAKkB0C,0BAAA;MALDvB,KAAK,EAAC,IAAI;MAACyB,KAAK,EAAC,KAAK;MAACC,KAAK,EAAC;;MACjCE,OAAO,EAAAC,QAAA,CACiEC,KAD1D,KACvBjD,YAAA,CAAiF4B,oBAAA;QAAtEe,IAAI,EAAC,SAAS;QAAC4D,IAAI,EAAC,OAAO;QAAE1E,OAAK,EAAA1B,MAAA,IAAEd,MAAA,CAAAmH,OAAO,CAACvD,KAAK,CAACC,GAAG;;0BAAG,MAAEuD,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;wDACrEzG,YAAA,CAAkF4B,oBAAA;QAAvEe,IAAI,EAAC,QAAQ;QAAC4D,IAAI,EAAC,OAAO;QAAE1E,OAAK,EAAA1B,MAAA,IAAEd,MAAA,CAAAqH,SAAS,CAACzD,KAAK,CAACC,GAAG;;0BAAG,MAAEuD,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;;;;;2EAlN/DpH,MAAA,CAAAsH,OAAO,E,GAuNpB/G,mBAAA,QAAW,EACXX,mBAAA,CAUM,OAVN2H,WAUM,GATJ5G,YAAA,CAQE6G,wBAAA;IAPQ,cAAY,EAAExH,MAAA,CAAAyH,WAAW;gEAAXzH,MAAA,CAAAyH,WAAW,GAAA3G,MAAA;IACzB,WAAS,EAAEd,MAAA,CAAA0H,QAAQ;6DAAR1H,MAAA,CAAA0H,QAAQ,GAAA5G,MAAA;IAC1B,YAAU,EAAE,aAAa;IACzB6G,KAAK,EAAE3H,MAAA,CAAAC,UAAU;IAClB2H,MAAM,EAAC,yCAAyC;IAC/CC,YAAW,EAAE7H,MAAA,CAAA8H,gBAAgB;IAC7BC,eAAc,EAAE/H,MAAA,CAAAgI;wGAIrBzH,mBAAA,cAAiB,EACjBI,YAAA,CAyNYsH,oBAAA;gBAxNDjI,MAAA,CAAAkI,aAAa;iEAAblI,MAAA,CAAAkI,aAAa,GAAApH,MAAA;IACrBuD,KAAK,EAAErE,MAAA,CAAAmI,UAAU;IAClB5E,KAAK,EAAC,OAAO;IACZ6E,OAAK,EAAEpI,MAAA,CAAAqI;;IA+MGC,MAAM,EAAA3E,QAAA,CACf,MAGO,CAHP/D,mBAAA,CAGO,QAHP2I,YAGO,GAFL5H,YAAA,CAAwD4B,oBAAA;MAA5CC,OAAK,EAAA4E,MAAA,SAAAA,MAAA,OAAAtG,MAAA,IAAEd,MAAA,CAAAkI,aAAa;;wBAAU,MAAEd,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;QAC5CzG,YAAA,CAA4E4B,oBAAA;MAAjEe,IAAI,EAAC,SAAS;MAAEd,OAAK,EAAExC,MAAA,CAAAwI,QAAQ;MAAGlB,OAAO,EAAEtH,MAAA,CAAAyI;;wBAAQ,MAAErB,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;;sBAhNpE,MA2MU,CA3MVzG,YAAA,CA2MU+H,kBAAA;MA3MAC,KAAK,EAAE3I,MAAA,CAAA4I,QAAQ;MAAGC,KAAK,EAAE7I,MAAA,CAAA8I,SAAS;MAAEC,GAAG,EAAC,SAAS;MAAC,aAAW,EAAC;;wBACtE,MAEe,CAFfpI,YAAA,CAEeqI,uBAAA;QAFDlH,KAAK,EAAC,WAAW;QAAC2B,IAAI,EAAC;;0BACnC,MAAgE,CAAhE9C,YAAA,CAAgEsI,mBAAA;sBAA7CjJ,MAAA,CAAA4I,QAAQ,CAAClH,SAAS;qEAAlB1B,MAAA,CAAA4I,QAAQ,CAAClH,SAAS,GAAAZ,MAAA;UAAGoI,QAAQ,EAAElJ,MAAA,CAAAmI;;;UAGpDxH,YAAA,CAeeqI,uBAAA;QAfDlH,KAAK,EAAC,eAAe;QAAC2B,IAAI,EAAC;;0BACvC,MAaY,CAbZ9C,YAAA,CAaYC,oBAAA;sBAZDZ,MAAA,CAAA4I,QAAQ,CAACO,aAAa;qEAAtBnJ,MAAA,CAAA4I,QAAQ,CAACO,aAAa,GAAArI,MAAA;UAC/BC,WAAW,EAAC,UAAU;UACtBC,UAAU,EAAV,EAAU;UACV,cAAY,EAAZ,EAAY;UACZ,sBAAoB,EAApB,EAAoB;UACnB,iBAAe,EAAE,KAAK;UACtBG,QAAM,EAAEiI,IAAA,CAAAC;;4BAEP,MAAsC,E,kBADxC3J,mBAAA,CAI0B6B,SAAA,QAAAC,WAAA,CAHPxB,MAAA,CAAAoC,oBAAoB,EAA9BC,MAAM;iCADfV,YAAA,CAI0BC,oBAAA;cAFvBC,GAAG,EAAEQ,MAAM,CAACN,KAAK;cACjBD,KAAK,EAAEO,MAAM,CAACP,KAAK;cACnBC,KAAK,EAAEM,MAAM,CAACN;;;;;;UAIrBpB,YAAA,CAEeqI,uBAAA;QAFDlH,KAAK,EAAC,aAAa;QAAC2B,IAAI,EAAC;;0BACrC,MAAyE,CAAzE9C,YAAA,CAAyEsI,mBAAA;sBAAtDjJ,MAAA,CAAA4I,QAAQ,CAAC9E,OAAO;qEAAhB9D,MAAA,CAAA4I,QAAQ,CAAC9E,OAAO,GAAAhD,MAAA;UAAEC,WAAW,EAAC;;;UAGnDJ,YAAA,CAEeqI,uBAAA;QAFDlH,KAAK,EAAC,IAAI;QAAC2B,IAAI,EAAC;;0BAC5B,MAAgF,CAAhF9C,YAAA,CAAgF2I,0BAAA;sBAAtDtJ,MAAA,CAAA4I,QAAQ,CAACW,SAAS;qEAAlBvJ,MAAA,CAAA4I,QAAQ,CAACW,SAAS,GAAAzI,MAAA;UAAG0I,SAAS,EAAE,CAAC;UAAEzI,WAAW,EAAC;;;UAG3EJ,YAAA,CAEeqI,uBAAA;QAFDlH,KAAK,EAAC,IAAI;QAAC2B,IAAI,EAAC;;0BAC5B,MAA+E,CAA/E9C,YAAA,CAA+E2I,0BAAA;sBAArDtJ,MAAA,CAAA4I,QAAQ,CAACa,QAAQ;qEAAjBzJ,MAAA,CAAA4I,QAAQ,CAACa,QAAQ,GAAA3I,MAAA;UAAG0I,SAAS,EAAE,CAAC;UAAEzI,WAAW,EAAC;;;UAG1ER,mBAAA,YAAe,EACfI,YAAA,CAAqD+I,qBAAA;QAAzC,kBAAgB,EAAC;MAAM;0BAAC,MAAItC,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UAExCxH,mBAAA,CAqKM,OArKN+J,WAqKM,GApKJpJ,mBAAA,YAAe,EACfX,mBAAA,CAQM,OARNgK,WAQM,G,4BAPJhK,mBAAA,CAAsB,eAAf,SAAO,qBACdA,mBAAA,CAKM,OALNiK,WAKM,GAJQ7J,MAAA,CAAA4I,QAAQ,CAACkB,KAAK,CAACC,MAAM,I,cAAjCrK,mBAAA,CAA0F,QAA1FsK,WAA0F,EAAAjK,gBAAA,CAA/BC,MAAA,CAAA4I,QAAQ,CAACkB,KAAK,CAACC,MAAM,qB,cAChFrK,mBAAA,CAAuC,QAAvCuK,WAAuC,EAAV,KAAG,IAChCtJ,YAAA,CAAsE4B,oBAAA;QAA3D2E,IAAI,EAAC,OAAO;QAAE1E,OAAK,EAAA4E,MAAA,QAAAA,MAAA,MAAAtG,MAAA,IAAEd,MAAA,CAAAkK,UAAU;;0BAAY,MAAI9C,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UACzCpH,MAAA,CAAA4I,QAAQ,CAACkB,KAAK,CAACC,MAAM,I,cAAtCpI,YAAA,CAA+GY,oBAAA;;QAAvE2E,IAAI,EAAC,OAAO;QAAC5D,IAAI,EAAC,QAAQ;QAAEd,OAAK,EAAA4E,MAAA,SAAAA,MAAA,OAAAtG,MAAA,IAAEd,MAAA,CAAAmK,UAAU;;0BAAY,MAAE/C,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;mDAIvG7G,mBAAA,gBAAmB,EACnBX,mBAAA,CAQM,OARNwK,WAQM,G,4BAPJxK,mBAAA,CAA0B,eAAnB,aAAW,qBAClBA,mBAAA,CAKM,OALNyK,WAKM,GAJQrK,MAAA,CAAA4I,QAAQ,CAACkB,KAAK,CAACQ,UAAU,I,cAArC5K,mBAAA,CAAkG,QAAlG6K,WAAkG,EAAAxK,gBAAA,CAAnCC,MAAA,CAAA4I,QAAQ,CAACkB,KAAK,CAACQ,UAAU,qB,cACxF5K,mBAAA,CAAuC,QAAvC8K,WAAuC,EAAV,KAAG,IAChC7J,YAAA,CAA0E4B,oBAAA;QAA/D2E,IAAI,EAAC,OAAO;QAAE1E,OAAK,EAAA4E,MAAA,SAAAA,MAAA,OAAAtG,MAAA,IAAEd,MAAA,CAAAkK,UAAU;;0BAAgB,MAAI9C,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UAC7CpH,MAAA,CAAA4I,QAAQ,CAACkB,KAAK,CAACQ,UAAU,I,cAA1C3I,YAAA,CAAuHY,oBAAA;;QAA3E2E,IAAI,EAAC,OAAO;QAAC5D,IAAI,EAAC,QAAQ;QAAEd,OAAK,EAAA4E,MAAA,SAAAA,MAAA,OAAAtG,MAAA,IAAEd,MAAA,CAAAmK,UAAU;;0BAAgB,MAAE/C,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;mDAI/G7G,mBAAA,uBAA0B,EAC1BX,mBAAA,CAQM,OARN6K,WAQM,G,4BAPJ7K,mBAAA,CAAiC,eAA1B,oBAAkB,qBACzBA,mBAAA,CAKM,OALN8K,WAKM,GAJQ1K,MAAA,CAAA4I,QAAQ,CAACkB,KAAK,CAACa,gBAAgB,I,cAA3CjL,mBAAA,CAA8G,QAA9GkL,WAA8G,EAAA7K,gBAAA,CAAzCC,MAAA,CAAA4I,QAAQ,CAACkB,KAAK,CAACa,gBAAgB,qB,cACpGjL,mBAAA,CAAuC,QAAvCmL,WAAuC,EAAV,KAAG,IAChClK,YAAA,CAAgF4B,oBAAA;QAArE2E,IAAI,EAAC,OAAO;QAAE1E,OAAK,EAAA4E,MAAA,SAAAA,MAAA,OAAAtG,MAAA,IAAEd,MAAA,CAAAkK,UAAU;;0BAAsB,MAAI9C,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UACnDpH,MAAA,CAAA4I,QAAQ,CAACkB,KAAK,CAACa,gBAAgB,I,cAAhDhJ,YAAA,CAAmIY,oBAAA;;QAAjF2E,IAAI,EAAC,OAAO;QAAC5D,IAAI,EAAC,QAAQ;QAAEd,OAAK,EAAA4E,MAAA,SAAAA,MAAA,OAAAtG,MAAA,IAAEd,MAAA,CAAAmK,UAAU;;0BAAsB,MAAE/C,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;mDAI3H7G,mBAAA,wBAA2B,EAC3BX,mBAAA,CAQM,OARNkL,WAQM,G,4BAPJlL,mBAAA,CAAkC,eAA3B,qBAAmB,qBAC1BA,mBAAA,CAKM,OALNmL,WAKM,GAJQ/K,MAAA,CAAA4I,QAAQ,CAACkB,KAAK,CAACkB,iBAAiB,I,cAA5CtL,mBAAA,CAAgH,QAAhHuL,WAAgH,EAAAlL,gBAAA,CAA1CC,MAAA,CAAA4I,QAAQ,CAACkB,KAAK,CAACkB,iBAAiB,qB,cACtGtL,mBAAA,CAAuC,QAAvCwL,WAAuC,EAAV,KAAG,IAChCvK,YAAA,CAAiF4B,oBAAA;QAAtE2E,IAAI,EAAC,OAAO;QAAE1E,OAAK,EAAA4E,MAAA,SAAAA,MAAA,OAAAtG,MAAA,IAAEd,MAAA,CAAAkK,UAAU;;0BAAuB,MAAI9C,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UACpDpH,MAAA,CAAA4I,QAAQ,CAACkB,KAAK,CAACkB,iBAAiB,I,cAAjDrJ,YAAA,CAAqIY,oBAAA;;QAAlF2E,IAAI,EAAC,OAAO;QAAC5D,IAAI,EAAC,QAAQ;QAAEd,OAAK,EAAA4E,MAAA,SAAAA,MAAA,OAAAtG,MAAA,IAAEd,MAAA,CAAAmK,UAAU;;0BAAuB,MAAE/C,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;mDAI7H7G,mBAAA,4BAA+B,EAC/BX,mBAAA,CAQM,OARNuL,WAQM,G,4BAPJvL,mBAAA,CAAsC,eAA/B,yBAAuB,qBAC9BA,mBAAA,CAKM,OALNwL,WAKM,GAJQpL,MAAA,CAAA4I,QAAQ,CAACkB,KAAK,CAACuB,qBAAqB,I,cAAhD3L,mBAAA,CAAwH,QAAxH4L,WAAwH,EAAAvL,gBAAA,CAA9CC,MAAA,CAAA4I,QAAQ,CAACkB,KAAK,CAACuB,qBAAqB,qB,cAC9G3L,mBAAA,CAAuC,QAAvC6L,WAAuC,EAAV,KAAG,IAChC5K,YAAA,CAAqF4B,oBAAA;QAA1E2E,IAAI,EAAC,OAAO;QAAE1E,OAAK,EAAA4E,MAAA,SAAAA,MAAA,OAAAtG,MAAA,IAAEd,MAAA,CAAAkK,UAAU;;0BAA2B,MAAI9C,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UACxDpH,MAAA,CAAA4I,QAAQ,CAACkB,KAAK,CAACuB,qBAAqB,I,cAArD1J,YAAA,CAA6IY,oBAAA;;QAAtF2E,IAAI,EAAC,OAAO;QAAC5D,IAAI,EAAC,QAAQ;QAAEd,OAAK,EAAA4E,MAAA,SAAAA,MAAA,OAAAtG,MAAA,IAAEd,MAAA,CAAAmK,UAAU;;0BAA2B,MAAE/C,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;mDAIrI7G,mBAAA,yBAA4B,EAC5BX,mBAAA,CAQM,OARN4L,WAQM,G,4BAPJ5L,mBAAA,CAAmC,eAA5B,sBAAoB,qBAC3BA,mBAAA,CAKM,OALN6L,WAKM,GAJQzL,MAAA,CAAA4I,QAAQ,CAACkB,KAAK,CAAC4B,kBAAkB,I,cAA7ChM,mBAAA,CAAkH,QAAlHiM,WAAkH,EAAA5L,gBAAA,CAA3CC,MAAA,CAAA4I,QAAQ,CAACkB,KAAK,CAAC4B,kBAAkB,qB,cACxGhM,mBAAA,CAAuC,QAAvCkM,WAAuC,EAAV,KAAG,IAChCjL,YAAA,CAAkF4B,oBAAA;QAAvE2E,IAAI,EAAC,OAAO;QAAE1E,OAAK,EAAA4E,MAAA,SAAAA,MAAA,OAAAtG,MAAA,IAAEd,MAAA,CAAAkK,UAAU;;0BAAwB,MAAI9C,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UACrDpH,MAAA,CAAA4I,QAAQ,CAACkB,KAAK,CAAC4B,kBAAkB,I,cAAlD/J,YAAA,CAAuIY,oBAAA;;QAAnF2E,IAAI,EAAC,OAAO;QAAC5D,IAAI,EAAC,QAAQ;QAAEd,OAAK,EAAA4E,MAAA,SAAAA,MAAA,OAAAtG,MAAA,IAAEd,MAAA,CAAAmK,UAAU;;0BAAwB,MAAE/C,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;mDAI/H7G,mBAAA,wBAA2B,EAC3BX,mBAAA,CAQM,OARNiM,WAQM,G,4BAPJjM,mBAAA,CAAkC,eAA3B,qBAAmB,qBAC1BA,mBAAA,CAKM,OALNkM,WAKM,GAJQ9L,MAAA,CAAA4I,QAAQ,CAACkB,KAAK,CAACiC,iBAAiB,I,cAA5CrM,mBAAA,CAAgH,QAAhHsM,WAAgH,EAAAjM,gBAAA,CAA1CC,MAAA,CAAA4I,QAAQ,CAACkB,KAAK,CAACiC,iBAAiB,qB,cACtGrM,mBAAA,CAAuC,QAAvCuM,WAAuC,EAAV,KAAG,IAChCtL,YAAA,CAAiF4B,oBAAA;QAAtE2E,IAAI,EAAC,OAAO;QAAE1E,OAAK,EAAA4E,MAAA,SAAAA,MAAA,OAAAtG,MAAA,IAAEd,MAAA,CAAAkK,UAAU;;0BAAuB,MAAI9C,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UACpDpH,MAAA,CAAA4I,QAAQ,CAACkB,KAAK,CAACiC,iBAAiB,I,cAAjDpK,YAAA,CAAqIY,oBAAA;;QAAlF2E,IAAI,EAAC,OAAO;QAAC5D,IAAI,EAAC,QAAQ;QAAEd,OAAK,EAAA4E,MAAA,SAAAA,MAAA,OAAAtG,MAAA,IAAEd,MAAA,CAAAmK,UAAU;;0BAAuB,MAAE/C,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;mDAI7H7G,mBAAA,wBAA2B,EAC3BX,mBAAA,CAQM,OARNsM,WAQM,G,4BAPJtM,mBAAA,CAAkC,eAA3B,qBAAmB,qBAC1BA,mBAAA,CAKM,OALNuM,WAKM,GAJQnM,MAAA,CAAA4I,QAAQ,CAACkB,KAAK,CAACsC,iBAAiB,I,cAA5C1M,mBAAA,CAAgH,QAAhH2M,WAAgH,EAAAtM,gBAAA,CAA1CC,MAAA,CAAA4I,QAAQ,CAACkB,KAAK,CAACsC,iBAAiB,qB,cACtG1M,mBAAA,CAAuC,QAAvC4M,WAAuC,EAAV,KAAG,IAChC3L,YAAA,CAAiF4B,oBAAA;QAAtE2E,IAAI,EAAC,OAAO;QAAE1E,OAAK,EAAA4E,MAAA,SAAAA,MAAA,OAAAtG,MAAA,IAAEd,MAAA,CAAAkK,UAAU;;0BAAuB,MAAI9C,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UACpDpH,MAAA,CAAA4I,QAAQ,CAACkB,KAAK,CAACsC,iBAAiB,I,cAAjDzK,YAAA,CAAqIY,oBAAA;;QAAlF2E,IAAI,EAAC,OAAO;QAAC5D,IAAI,EAAC,QAAQ;QAAEd,OAAK,EAAA4E,MAAA,SAAAA,MAAA,OAAAtG,MAAA,IAAEd,MAAA,CAAAmK,UAAU;;0BAAuB,MAAE/C,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;mDAI7H7G,mBAAA,WAAc,EACdX,mBAAA,CAQM,OARN2M,WAQM,G,4BAPJ3M,mBAAA,CAAqB,eAAd,QAAM,qBACbA,mBAAA,CAKM,OALN4M,WAKM,GAJQxM,MAAA,CAAA4I,QAAQ,CAACkB,KAAK,CAAC2C,KAAK,I,cAAhC/M,mBAAA,CAAwF,QAAxFgN,WAAwF,EAAA3M,gBAAA,CAA9BC,MAAA,CAAA4I,QAAQ,CAACkB,KAAK,CAAC2C,KAAK,qB,cAC9E/M,mBAAA,CAAuC,QAAvCiN,WAAuC,EAAV,KAAG,IAChChM,YAAA,CAAqE4B,oBAAA;QAA1D2E,IAAI,EAAC,OAAO;QAAE1E,OAAK,EAAA4E,MAAA,SAAAA,MAAA,OAAAtG,MAAA,IAAEd,MAAA,CAAAkK,UAAU;;0BAAW,MAAI9C,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UACxCpH,MAAA,CAAA4I,QAAQ,CAACkB,KAAK,CAAC2C,KAAK,I,cAArC9K,YAAA,CAA6GY,oBAAA;;QAAtE2E,IAAI,EAAC,OAAO;QAAC5D,IAAI,EAAC,QAAQ;QAAEd,OAAK,EAAA4E,MAAA,SAAAA,MAAA,OAAAtG,MAAA,IAAEd,MAAA,CAAAmK,UAAU;;0BAAW,MAAE/C,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;mDAIrG7G,mBAAA,gBAAmB,EACnBX,mBAAA,CAQM,OARNgN,WAQM,G,4BAPJhN,mBAAA,CAA0B,eAAnB,aAAW,qBAClBA,mBAAA,CAKM,OALNiN,WAKM,GAJQ7M,MAAA,CAAA4I,QAAQ,CAACkB,KAAK,CAACgD,UAAU,I,cAArCpN,mBAAA,CAAkG,QAAlGqN,WAAkG,EAAAhN,gBAAA,CAAnCC,MAAA,CAAA4I,QAAQ,CAACkB,KAAK,CAACgD,UAAU,qB,cACxFpN,mBAAA,CAAuC,QAAvCsN,WAAuC,EAAV,KAAG,IAChCrM,YAAA,CAA0E4B,oBAAA;QAA/D2E,IAAI,EAAC,OAAO;QAAE1E,OAAK,EAAA4E,MAAA,SAAAA,MAAA,OAAAtG,MAAA,IAAEd,MAAA,CAAAkK,UAAU;;0BAAgB,MAAI9C,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UAC7CpH,MAAA,CAAA4I,QAAQ,CAACkB,KAAK,CAACgD,UAAU,I,cAA1CnL,YAAA,CAAuHY,oBAAA;;QAA3E2E,IAAI,EAAC,OAAO;QAAC5D,IAAI,EAAC,QAAQ;QAAEd,OAAK,EAAA4E,MAAA,SAAAA,MAAA,OAAAtG,MAAA,IAAEd,MAAA,CAAAmK,UAAU;;0BAAgB,MAAE/C,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;mDAI/G7G,mBAAA,SAAY,EACZX,mBAAA,CAQM,OARNqN,WAQM,G,4BAPJrN,mBAAA,CAAmB,eAAZ,MAAI,qBACXA,mBAAA,CAKM,OALNsN,WAKM,GAJQlN,MAAA,CAAA4I,QAAQ,CAACkB,KAAK,CAACqD,GAAG,I,cAA9BzN,mBAAA,CAAoF,QAApF0N,WAAoF,EAAArN,gBAAA,CAA5BC,MAAA,CAAA4I,QAAQ,CAACkB,KAAK,CAACqD,GAAG,qB,cAC1EzN,mBAAA,CAAuC,QAAvC2N,WAAuC,EAAV,KAAG,IAChC1M,YAAA,CAAmE4B,oBAAA;QAAxD2E,IAAI,EAAC,OAAO;QAAE1E,OAAK,EAAA4E,MAAA,SAAAA,MAAA,OAAAtG,MAAA,IAAEd,MAAA,CAAAkK,UAAU;;0BAAS,MAAI9C,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UACtCpH,MAAA,CAAA4I,QAAQ,CAACkB,KAAK,CAACqD,GAAG,I,cAAnCxL,YAAA,CAAyGY,oBAAA;;QAApE2E,IAAI,EAAC,OAAO;QAAC5D,IAAI,EAAC,QAAQ;QAAEd,OAAK,EAAA4E,MAAA,SAAAA,MAAA,OAAAtG,MAAA,IAAEd,MAAA,CAAAmK,UAAU;;0BAAS,MAAE/C,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;mDAIjG7G,mBAAA,gBAAmB,EACnBX,mBAAA,CAQM,OARN0N,WAQM,G,4BAPJ1N,mBAAA,CAA0B,eAAnB,aAAW,qBAClBA,mBAAA,CAKM,OALN2N,WAKM,GAJQvN,MAAA,CAAA4I,QAAQ,CAACkB,KAAK,CAAC0D,UAAU,I,cAArC9N,mBAAA,CAAkG,QAAlG+N,WAAkG,EAAA1N,gBAAA,CAAnCC,MAAA,CAAA4I,QAAQ,CAACkB,KAAK,CAAC0D,UAAU,qB,cACxF9N,mBAAA,CAAuC,QAAvCgO,WAAuC,EAAV,KAAG,IAChC/M,YAAA,CAA0E4B,oBAAA;QAA/D2E,IAAI,EAAC,OAAO;QAAE1E,OAAK,EAAA4E,MAAA,SAAAA,MAAA,OAAAtG,MAAA,IAAEd,MAAA,CAAAkK,UAAU;;0BAAgB,MAAI9C,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UAC7CpH,MAAA,CAAA4I,QAAQ,CAACkB,KAAK,CAAC0D,UAAU,I,cAA1C7L,YAAA,CAAuHY,oBAAA;;QAA3E2E,IAAI,EAAC,OAAO;QAAC5D,IAAI,EAAC,QAAQ;QAAEd,OAAK,EAAA4E,MAAA,SAAAA,MAAA,OAAAtG,MAAA,IAAEd,MAAA,CAAAmK,UAAU;;0BAAgB,MAAE/C,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;mDAI/G7G,mBAAA,WAAc,EACdX,mBAAA,CAQM,OARN+N,WAQM,G,4BAPJ/N,mBAAA,CAAqB,eAAd,QAAM,qBACbA,mBAAA,CAKM,OALNgO,WAKM,GAJQ5N,MAAA,CAAA4I,QAAQ,CAACkB,KAAK,CAAC+D,KAAK,I,cAAhCnO,mBAAA,CAAwF,QAAxFoO,WAAwF,EAAA/N,gBAAA,CAA9BC,MAAA,CAAA4I,QAAQ,CAACkB,KAAK,CAAC+D,KAAK,qB,cAC9EnO,mBAAA,CAAuC,QAAvCqO,WAAuC,EAAV,KAAG,IAChCpN,YAAA,CAAqE4B,oBAAA;QAA1D2E,IAAI,EAAC,OAAO;QAAE1E,OAAK,EAAA4E,MAAA,SAAAA,MAAA,OAAAtG,MAAA,IAAEd,MAAA,CAAAkK,UAAU;;0BAAW,MAAI9C,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UACxCpH,MAAA,CAAA4I,QAAQ,CAACkB,KAAK,CAAC+D,KAAK,I,cAArClM,YAAA,CAA6GY,oBAAA;;QAAtE2E,IAAI,EAAC,OAAO;QAAC5D,IAAI,EAAC,QAAQ;QAAEd,OAAK,EAAA4E,MAAA,SAAAA,MAAA,OAAAtG,MAAA,IAAEd,MAAA,CAAAmK,UAAU;;0BAAW,MAAE/C,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;mDAIrG7G,mBAAA,UAAa,EACbX,mBAAA,CAQM,OARNoO,WAQM,G,4BAPJpO,mBAAA,CAAoB,eAAb,OAAK,qBACZA,mBAAA,CAKM,OALNqO,WAKM,GAJQjO,MAAA,CAAA4I,QAAQ,CAACkB,KAAK,CAACoE,IAAI,I,cAA/BxO,mBAAA,CAAsF,QAAtFyO,WAAsF,EAAApO,gBAAA,CAA7BC,MAAA,CAAA4I,QAAQ,CAACkB,KAAK,CAACoE,IAAI,qB,cAC5ExO,mBAAA,CAAuC,QAAvC0O,YAAuC,EAAV,KAAG,IAChCzN,YAAA,CAAoE4B,oBAAA;QAAzD2E,IAAI,EAAC,OAAO;QAAE1E,OAAK,EAAA4E,MAAA,SAAAA,MAAA,OAAAtG,MAAA,IAAEd,MAAA,CAAAkK,UAAU;;0BAAU,MAAI9C,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UACvCpH,MAAA,CAAA4I,QAAQ,CAACkB,KAAK,CAACoE,IAAI,I,cAApCvM,YAAA,CAA2GY,oBAAA;;QAArE2E,IAAI,EAAC,OAAO;QAAC5D,IAAI,EAAC,QAAQ;QAAEd,OAAK,EAAA4E,MAAA,SAAAA,MAAA,OAAAtG,MAAA,IAAEd,MAAA,CAAAmK,UAAU;;0BAAU,MAAE/C,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;mDAInG7G,mBAAA,UAAa,EACbX,mBAAA,CAQM,OARNyO,YAQM,G,4BAPJzO,mBAAA,CAAoB,eAAb,OAAK,qBACZA,mBAAA,CAKM,OALN0O,YAKM,GAJQtO,MAAA,CAAA4I,QAAQ,CAACkB,KAAK,CAACyE,IAAI,I,cAA/B7O,mBAAA,CAAsF,QAAtF8O,YAAsF,EAAAzO,gBAAA,CAA7BC,MAAA,CAAA4I,QAAQ,CAACkB,KAAK,CAACyE,IAAI,qB,cAC5E7O,mBAAA,CAAuC,QAAvC+O,YAAuC,EAAV,KAAG,IAChC9N,YAAA,CAAoE4B,oBAAA;QAAzD2E,IAAI,EAAC,OAAO;QAAE1E,OAAK,EAAA4E,MAAA,SAAAA,MAAA,OAAAtG,MAAA,IAAEd,MAAA,CAAAkK,UAAU;;0BAAU,MAAI9C,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UACvCpH,MAAA,CAAA4I,QAAQ,CAACkB,KAAK,CAACyE,IAAI,I,cAApC5M,YAAA,CAA2GY,oBAAA;;QAArE2E,IAAI,EAAC,OAAO;QAAC5D,IAAI,EAAC,QAAQ;QAAEd,OAAK,EAAA4E,MAAA,SAAAA,MAAA,OAAAtG,MAAA,IAAEd,MAAA,CAAAmK,UAAU;;0BAAU,MAAE/C,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
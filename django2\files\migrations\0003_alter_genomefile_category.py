# Generated by Django 3.2.25 on 2025-07-02 03:52

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('files', '0002_auto_20250701_2239'),
    ]

    operations = [
        migrations.AlterField(
            model_name='genomefile',
            name='category',
            field=models.CharField(choices=[('genome', '基因组序列'), ('transcriptome.all', '转录组-All'), ('transcriptome.root', '转录组-Root'), ('transcriptome.stem', '转录组-Stem'), ('transcriptome.leaf', '转录组-Leaf'), ('transcriptome.panicles', '转录组-Panicles'), ('transcriptome.shoot', '转录组-Shoot'), ('miRNA', '微RNA'), ('tRNA', '转运RNA'), ('rRNA', '核糖体RNA'), ('telomere', '端粒'), ('centromere', '着丝粒'), ('TEs', '转座子'), ('annotation', '基因注释'), ('other', '其他')], max_length=50, verbose_name='文件类别'),
        ),
    ]

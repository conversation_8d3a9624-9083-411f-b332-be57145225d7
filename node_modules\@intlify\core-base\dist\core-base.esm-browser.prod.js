/*!
  * core-base v9.14.5
  * (c) 2025 ka<PERSON><PERSON> ka<PERSON>
  * Released under the MIT License.
  */
function warn(e,t){"undefined"!=typeof console&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const RE_ARGS=/\{([0-9a-zA-Z]+)\}/g;function format$1(e,...t){return 1===t.length&&isObject(t[0])&&(t=t[0]),t&&t.hasOwnProperty||(t={}),e.replace(RE_ARGS,((e,r)=>t.hasOwnProperty(r)?t[r]:""))}const generateFormatCacheKey=(e,t,r)=>friendlyJSONstringify({l:e,k:t,s:r}),friendlyJSONstringify=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),isNumber=e=>"number"==typeof e&&isFinite(e),isDate=e=>"[object Date]"===toTypeString(e),isRegExp=e=>"[object RegExp]"===toTypeString(e),isEmptyObject=e=>isPlainObject(e)&&0===Object.keys(e).length,assign=Object.assign,_create=Object.create,create=(e=null)=>_create(e);function escapeHtml(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;").replace(/\//g,"&#x2F;").replace(/=/g,"&#x3D;")}function escapeAttributeValue(e){return e.replace(/&(?![a-zA-Z0-9#]{2,6};)/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&apos;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}function sanitizeTranslatedHtml(e){e=(e=e.replace(/(\w+)\s*=\s*"([^"]*)"/g,((e,t,r)=>`${t}="${escapeAttributeValue(r)}"`))).replace(/(\w+)\s*=\s*'([^']*)'/g,((e,t,r)=>`${t}='${escapeAttributeValue(r)}'`));/\s*on\w+\s*=\s*["']?[^"'>]+["']?/gi.test(e)&&(e=e.replace(/(\s+)(on)(\w+\s*=)/gi,"$1&#111;n$3"));return[/(\s+(?:href|src|action|formaction)\s*=\s*["']?)\s*javascript:/gi,/(style\s*=\s*["'][^"']*url\s*\(\s*)javascript:/gi].forEach((t=>{e=e.replace(t,"$1javascript&#58;")})),e}const hasOwnProperty=Object.prototype.hasOwnProperty;function hasOwn(e,t){return hasOwnProperty.call(e,t)}const isArray=Array.isArray,isFunction=e=>"function"==typeof e,isString=e=>"string"==typeof e,isBoolean=e=>"boolean"==typeof e,isObject=e=>null!==e&&"object"==typeof e,isPromise=e=>isObject(e)&&isFunction(e.then)&&isFunction(e.catch),objectToString=Object.prototype.toString,toTypeString=e=>objectToString.call(e),isPlainObject=e=>{if(!isObject(e))return!1;const t=Object.getPrototypeOf(e);return null===t||t.constructor===Object},toDisplayString=e=>null==e?"":isArray(e)||isPlainObject(e)&&e.toString===objectToString?JSON.stringify(e,null,2):String(e);function join(e,t=""){return e.reduce(((e,r,n)=>0===n?e+r:e+t+r),"")}function incrementer(e){let t=e;return()=>++t}function createPosition(e,t,r){return{line:e,column:t,offset:r}}function createLocation(e,t,r){const n={start:e,end:t};return null!=r&&(n.source=r),n}const CompileWarnCodes={USE_MODULO_SYNTAX:1,__EXTEND_POINT__:2};function createCompileWarn(e,t,...r){const n={message:String(e),code:e};return t&&(n.location=t),n}const CompileErrorCodes={EXPECTED_TOKEN:1,INVALID_TOKEN_IN_PLACEHOLDER:2,UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER:3,UNKNOWN_ESCAPE_SEQUENCE:4,INVALID_UNICODE_ESCAPE_SEQUENCE:5,UNBALANCED_CLOSING_BRACE:6,UNTERMINATED_CLOSING_BRACE:7,EMPTY_PLACEHOLDER:8,NOT_ALLOW_NEST_PLACEHOLDER:9,INVALID_LINKED_FORMAT:10,MUST_HAVE_MESSAGES_IN_PLURAL:11,UNEXPECTED_EMPTY_LINKED_MODIFIER:12,UNEXPECTED_EMPTY_LINKED_KEY:13,UNEXPECTED_LEXICAL_ANALYSIS:14,UNHANDLED_CODEGEN_NODE_TYPE:15,UNHANDLED_MINIFIER_NODE_TYPE:16,__EXTEND_POINT__:17};function createCompileError(e,t,r={}){const{domain:n,messages:o,args:s}=r,a=new SyntaxError(String(e));return a.code=e,t&&(a.location=t),a.domain=n,a}function defaultOnError(e){throw e}CompileErrorCodes.EXPECTED_TOKEN,CompileErrorCodes.INVALID_TOKEN_IN_PLACEHOLDER,CompileErrorCodes.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER,CompileErrorCodes.UNKNOWN_ESCAPE_SEQUENCE,CompileErrorCodes.INVALID_UNICODE_ESCAPE_SEQUENCE,CompileErrorCodes.UNBALANCED_CLOSING_BRACE,CompileErrorCodes.UNTERMINATED_CLOSING_BRACE,CompileErrorCodes.EMPTY_PLACEHOLDER,CompileErrorCodes.NOT_ALLOW_NEST_PLACEHOLDER,CompileErrorCodes.INVALID_LINKED_FORMAT,CompileErrorCodes.MUST_HAVE_MESSAGES_IN_PLURAL,CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_MODIFIER,CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_KEY,CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,CompileErrorCodes.UNHANDLED_CODEGEN_NODE_TYPE,CompileErrorCodes.UNHANDLED_MINIFIER_NODE_TYPE;const CHAR_SP=" ",CHAR_CR="\r",CHAR_LF="\n",CHAR_LS=String.fromCharCode(8232),CHAR_PS=String.fromCharCode(8233);function createScanner(e){const t=e;let r=0,n=1,o=1,s=0;const a=e=>t[e]===CHAR_CR&&t[e+1]===CHAR_LF,i=e=>t[e]===CHAR_PS,c=e=>t[e]===CHAR_LS,l=e=>a(e)||(e=>t[e]===CHAR_LF)(e)||i(e)||c(e),u=e=>a(e)||i(e)||c(e)?CHAR_LF:t[e];function E(){return s=0,l(r)&&(n++,o=0),a(r)&&r++,r++,o++,t[r]}return{index:()=>r,line:()=>n,column:()=>o,peekOffset:()=>s,charAt:u,currentChar:()=>u(r),currentPeek:()=>u(r+s),next:E,peek:function(){return a(r+s)&&s++,s++,t[r+s]},reset:function(){r=0,n=1,o=1,s=0},resetPeek:function(e=0){s=e},skipToPeek:function(){const e=r+s;for(;e!==r;)E();s=0}}}const EOF=void 0,DOT=".",LITERAL_DELIMITER="'",ERROR_DOMAIN$1="tokenizer";function createTokenizer(e,t={}){const r=!1!==t.location,n=createScanner(e),o=()=>n.index(),s=()=>createPosition(n.line(),n.column(),n.index()),a=s(),i=o(),c={currentType:14,offset:i,startLoc:a,endLoc:a,lastType:14,lastOffset:i,lastStartLoc:a,lastEndLoc:a,braceNest:0,inLinked:!1,text:""},l=()=>c,{onError:u}=t;function E(e,t,n){e.endLoc=s(),e.currentType=t;const o={type:t};return r&&(o.loc=createLocation(e.startLoc,e.endLoc)),null!=n&&(o.value=n),o}const f=e=>E(e,14);function _(e,t){return e.currentChar()===t?(e.next(),t):(CompileErrorCodes.EXPECTED_TOKEN,s(),"")}function m(e){let t="";for(;e.currentPeek()===CHAR_SP||e.currentPeek()===CHAR_LF;)t+=e.currentPeek(),e.peek();return t}function p(e){const t=m(e);return e.skipToPeek(),t}function C(e){if(e===EOF)return!1;const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||95===t}function d(e,t){const{currentType:r}=t;if(2!==r)return!1;m(e);const n=function(e){if(e===EOF)return!1;const t=e.charCodeAt(0);return t>=48&&t<=57}("-"===e.currentPeek()?e.peek():e.currentPeek());return e.resetPeek(),n}function T(e){m(e);const t="|"===e.currentPeek();return e.resetPeek(),t}function g(e,t=!0){const r=(t=!1,n="",o=!1)=>{const s=e.currentPeek();return"{"===s?"%"!==n&&t:"@"!==s&&s?"%"===s?(e.peek(),r(t,"%",!0)):"|"===s?!("%"!==n&&!o)||!(n===CHAR_SP||n===CHAR_LF):s===CHAR_SP?(e.peek(),r(!0,CHAR_SP,o)):s!==CHAR_LF||(e.peek(),r(!0,CHAR_LF,o)):"%"===n||t},n=r();return t&&e.resetPeek(),n}function N(e,t){const r=e.currentChar();return r===EOF?EOF:t(r)?(e.next(),r):null}function A(e){const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t}function O(e){return N(e,A)}function S(e){const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t||45===t}function L(e){return N(e,S)}function P(e){const t=e.charCodeAt(0);return t>=48&&t<=57}function h(e){return N(e,P)}function b(e){const t=e.charCodeAt(0);return t>=48&&t<=57||t>=65&&t<=70||t>=97&&t<=102}function k(e){return N(e,b)}function I(e){let t="",r="";for(;t=h(e);)r+=t;return r}function R(e){let t="";for(;;){const r=e.currentChar();if("{"===r||"}"===r||"@"===r||"|"===r||!r)break;if("%"===r){if(!g(e))break;t+=r,e.next()}else if(r===CHAR_SP||r===CHAR_LF)if(g(e))t+=r,e.next();else{if(T(e))break;t+=r,e.next()}else t+=r,e.next()}return t}function M(e){return e!==LITERAL_DELIMITER&&e!==CHAR_LF}function y(e){const t=e.currentChar();switch(t){case"\\":case"'":return e.next(),`\\${t}`;case"u":return D(e,t,4);case"U":return D(e,t,6);default:return CompileErrorCodes.UNKNOWN_ESCAPE_SEQUENCE,s(),""}}function D(e,t,r){_(e,t);let n="";for(let o=0;o<r;o++){const t=k(e);if(!t){CompileErrorCodes.INVALID_UNICODE_ESCAPE_SEQUENCE,s(),e.currentChar();break}n+=t}return`\\${t}${n}`}function F(e){return"{"!==e&&"}"!==e&&e!==CHAR_SP&&e!==CHAR_LF}function v(e){p(e);const t=_(e,"|");return p(e),t}function U(e,t){let r=null;switch(e.currentChar()){case"{":return t.braceNest>=1&&(CompileErrorCodes.NOT_ALLOW_NEST_PLACEHOLDER,s()),e.next(),r=E(t,2,"{"),p(e),t.braceNest++,r;case"}":return t.braceNest>0&&2===t.currentType&&(CompileErrorCodes.EMPTY_PLACEHOLDER,s()),e.next(),r=E(t,3,"}"),t.braceNest--,t.braceNest>0&&p(e),t.inLinked&&0===t.braceNest&&(t.inLinked=!1),r;case"@":return t.braceNest>0&&(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE,s()),r=x(e,t)||f(t),t.braceNest=0,r;default:{let n=!0,o=!0,a=!0;if(T(e))return t.braceNest>0&&(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE,s()),r=E(t,1,v(e)),t.braceNest=0,t.inLinked=!1,r;if(t.braceNest>0&&(5===t.currentType||6===t.currentType||7===t.currentType))return CompileErrorCodes.UNTERMINATED_CLOSING_BRACE,s(),t.braceNest=0,w(e,t);if(n=function(e,t){const{currentType:r}=t;if(2!==r)return!1;m(e);const n=C(e.currentPeek());return e.resetPeek(),n}(e,t))return r=E(t,5,function(e){p(e);let t="",r="";for(;t=L(e);)r+=t;return e.currentChar()===EOF&&(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE,s()),r}(e)),p(e),r;if(o=d(e,t))return r=E(t,6,function(e){p(e);let t="";return"-"===e.currentChar()?(e.next(),t+=`-${I(e)}`):t+=I(e),e.currentChar()===EOF&&(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE,s()),t}(e)),p(e),r;if(a=function(e,t){const{currentType:r}=t;if(2!==r)return!1;m(e);const n=e.currentPeek()===LITERAL_DELIMITER;return e.resetPeek(),n}(e,t))return r=E(t,7,function(e){p(e),_(e,"'");let t="",r="";for(;t=N(e,M);)r+="\\"===t?y(e):t;const n=e.currentChar();return n===CHAR_LF||n===EOF?(CompileErrorCodes.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER,s(),n===CHAR_LF&&(e.next(),_(e,"'")),r):(_(e,"'"),r)}(e)),p(e),r;if(!n&&!o&&!a)return r=E(t,13,function(e){p(e);let t="",r="";for(;t=N(e,F);)r+=t;return r}(e)),CompileErrorCodes.INVALID_TOKEN_IN_PLACEHOLDER,s(),r.value,p(e),r;break}}return r}function x(e,t){const{currentType:r}=t;let n=null;const o=e.currentChar();switch(8!==r&&9!==r&&12!==r&&10!==r||o!==CHAR_LF&&o!==CHAR_SP||(CompileErrorCodes.INVALID_LINKED_FORMAT,s()),o){case"@":return e.next(),n=E(t,8,"@"),t.inLinked=!0,n;case".":return p(e),e.next(),E(t,9,".");case":":return p(e),e.next(),E(t,10,":");default:return T(e)?(n=E(t,1,v(e)),t.braceNest=0,t.inLinked=!1,n):function(e,t){const{currentType:r}=t;if(8!==r)return!1;m(e);const n="."===e.currentPeek();return e.resetPeek(),n}(e,t)||function(e,t){const{currentType:r}=t;if(8!==r&&12!==r)return!1;m(e);const n=":"===e.currentPeek();return e.resetPeek(),n}(e,t)?(p(e),x(e,t)):function(e,t){const{currentType:r}=t;if(9!==r)return!1;m(e);const n=C(e.currentPeek());return e.resetPeek(),n}(e,t)?(p(e),E(t,12,function(e){let t="",r="";for(;t=O(e);)r+=t;return r}(e))):function(e,t){const{currentType:r}=t;if(10!==r)return!1;const n=()=>{const t=e.currentPeek();return"{"===t?C(e.peek()):!("@"===t||"%"===t||"|"===t||":"===t||"."===t||t===CHAR_SP||!t)&&(t===CHAR_LF?(e.peek(),n()):g(e,!1))},o=n();return e.resetPeek(),o}(e,t)?(p(e),"{"===o?U(e,t)||n:E(t,11,function(e){const t=r=>{const n=e.currentChar();return"{"!==n&&"%"!==n&&"@"!==n&&"|"!==n&&"("!==n&&")"!==n&&n?n===CHAR_SP?r:(r+=n,e.next(),t(r)):r};return t("")}(e))):(8===r&&(CompileErrorCodes.INVALID_LINKED_FORMAT,s()),t.braceNest=0,t.inLinked=!1,w(e,t))}}function w(e,t){let r={type:14};if(t.braceNest>0)return U(e,t)||f(t);if(t.inLinked)return x(e,t)||f(t);switch(e.currentChar()){case"{":return U(e,t)||f(t);case"}":return CompileErrorCodes.UNBALANCED_CLOSING_BRACE,s(),e.next(),E(t,3,"}");case"@":return x(e,t)||f(t);default:{if(T(e))return r=E(t,1,v(e)),t.braceNest=0,t.inLinked=!1,r;const{isModulo:n,hasSpace:o}=function(e){const t=m(e),r="%"===e.currentPeek()&&"{"===e.peek();return e.resetPeek(),{isModulo:r,hasSpace:t.length>0}}(e);if(n)return o?E(t,0,R(e)):E(t,4,function(e){p(e);const t=e.currentChar();return"%"!==t&&(CompileErrorCodes.EXPECTED_TOKEN,s()),e.next(),"%"}(e));if(g(e))return E(t,0,R(e));break}}return r}return{nextToken:function(){const{currentType:e,offset:t,startLoc:r,endLoc:a}=c;return c.lastType=e,c.lastOffset=t,c.lastStartLoc=r,c.lastEndLoc=a,c.offset=o(),c.startLoc=s(),n.currentChar()===EOF?E(c,14):w(n,c)},currentOffset:o,currentPosition:s,context:l}}const ERROR_DOMAIN="parser",KNOWN_ESCAPES=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function fromEscapeSequence(e,t,r){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const e=parseInt(t||r,16);return e<=55295||e>=57344?String.fromCodePoint(e):"�"}}}function createParser(e={}){const t=!1!==e.location,{onError:r,onWarn:n}=e;function o(e,r,n){const o={type:e};return t&&(o.start=r,o.end=r,o.loc={start:n,end:n}),o}function s(e,r,n,o){o&&(e.type=o),t&&(e.end=r,e.loc&&(e.loc.end=n))}function a(e,t){const r=e.context(),n=o(3,r.offset,r.startLoc);return n.value=t,s(n,e.currentOffset(),e.currentPosition()),n}function i(e,t){const r=e.context(),{lastOffset:n,lastStartLoc:a}=r,i=o(5,n,a);return i.index=parseInt(t,10),e.nextToken(),s(i,e.currentOffset(),e.currentPosition()),i}function c(e,t,r){const n=e.context(),{lastOffset:a,lastStartLoc:i}=n,c=o(4,a,i);return c.key=t,!0===r&&(c.modulo=!0),e.nextToken(),s(c,e.currentOffset(),e.currentPosition()),c}function l(e,t){const r=e.context(),{lastOffset:n,lastStartLoc:a}=r,i=o(9,n,a);return i.value=t.replace(KNOWN_ESCAPES,fromEscapeSequence),e.nextToken(),s(i,e.currentOffset(),e.currentPosition()),i}function u(e){const t=e.context(),r=o(6,t.offset,t.startLoc);let n=e.nextToken();if(9===n.type){const t=function(e){const t=e.nextToken(),r=e.context(),{lastOffset:n,lastStartLoc:a}=r,i=o(8,n,a);return 12!==t.type?(CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_MODIFIER,r.lastStartLoc,i.value="",s(i,n,a),{nextConsumeToken:t,node:i}):(null==t.value&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,r.lastStartLoc,getTokenCaption(t)),i.value=t.value||"",s(i,e.currentOffset(),e.currentPosition()),{node:i})}(e);r.modifier=t.node,n=t.nextConsumeToken||e.nextToken()}switch(10!==n.type&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,getTokenCaption(n)),n=e.nextToken(),2===n.type&&(n=e.nextToken()),n.type){case 11:null==n.value&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,getTokenCaption(n)),r.key=function(e,t){const r=e.context(),n=o(7,r.offset,r.startLoc);return n.value=t,s(n,e.currentOffset(),e.currentPosition()),n}(e,n.value||"");break;case 5:null==n.value&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,getTokenCaption(n)),r.key=c(e,n.value||"");break;case 6:null==n.value&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,getTokenCaption(n)),r.key=i(e,n.value||"");break;case 7:null==n.value&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,getTokenCaption(n)),r.key=l(e,n.value||"");break;default:{CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_KEY,t.lastStartLoc;const a=e.context(),i=o(7,a.offset,a.startLoc);return i.value="",s(i,a.offset,a.startLoc),r.key=i,s(r,a.offset,a.startLoc),{nextConsumeToken:n,node:r}}}return s(r,e.currentOffset(),e.currentPosition()),{node:r}}function E(e){const t=e.context(),r=o(2,1===t.currentType?e.currentOffset():t.offset,1===t.currentType?t.endLoc:t.startLoc);r.items=[];let n=null,E=null;do{const o=n||e.nextToken();switch(n=null,o.type){case 0:null==o.value&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,getTokenCaption(o)),r.items.push(a(e,o.value||""));break;case 6:null==o.value&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,getTokenCaption(o)),r.items.push(i(e,o.value||""));break;case 4:E=!0;break;case 5:null==o.value&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,getTokenCaption(o)),r.items.push(c(e,o.value||"",!!E)),E&&(CompileWarnCodes.USE_MODULO_SYNTAX,t.lastStartLoc,getTokenCaption(o),E=null);break;case 7:null==o.value&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,getTokenCaption(o)),r.items.push(l(e,o.value||""));break;case 8:{const t=u(e);r.items.push(t.node),n=t.nextConsumeToken||null;break}}}while(14!==t.currentType&&1!==t.currentType);return s(r,1===t.currentType?t.lastOffset:e.currentOffset(),1===t.currentType?t.lastEndLoc:e.currentPosition()),r}function f(e){const t=e.context(),{offset:r,startLoc:n}=t,a=E(e);return 14===t.currentType?a:function(e,t,r,n){const a=e.context();let i=0===n.items.length;const c=o(1,t,r);c.cases=[],c.cases.push(n);do{const t=E(e);i||(i=0===t.items.length),c.cases.push(t)}while(14!==a.currentType);return i&&CompileErrorCodes.MUST_HAVE_MESSAGES_IN_PLURAL,s(c,e.currentOffset(),e.currentPosition()),c}(e,r,n,a)}return{parse:function(r){const n=createTokenizer(r,assign({},e)),a=n.context(),i=o(0,a.offset,a.startLoc);return t&&i.loc&&(i.loc.source=r),i.body=f(n),e.onCacheKey&&(i.cacheKey=e.onCacheKey(r)),14!==a.currentType&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,a.lastStartLoc,r[a.offset]),s(i,n.currentOffset(),n.currentPosition()),i}}}function getTokenCaption(e){if(14===e.type)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function createTransformer(e,t={}){const r={ast:e,helpers:new Set};return{context:()=>r,helper:e=>(r.helpers.add(e),e)}}function traverseNodes(e,t){for(let r=0;r<e.length;r++)traverseNode(e[r],t)}function traverseNode(e,t){switch(e.type){case 1:traverseNodes(e.cases,t),t.helper("plural");break;case 2:traverseNodes(e.items,t);break;case 6:traverseNode(e.key,t),t.helper("linked"),t.helper("type");break;case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named")}}function transform(e,t={}){const r=createTransformer(e);r.helper("normalize"),e.body&&traverseNode(e.body,r);const n=r.context();e.helpers=Array.from(n.helpers)}function optimize(e){const t=e.body;return 2===t.type?optimizeMessageNode(t):t.cases.forEach((e=>optimizeMessageNode(e))),e}function optimizeMessageNode(e){if(1===e.items.length){const t=e.items[0];3!==t.type&&9!==t.type||(e.static=t.value,delete t.value)}else{const t=[];for(let r=0;r<e.items.length;r++){const n=e.items[r];if(3!==n.type&&9!==n.type)break;if(null==n.value)break;t.push(n.value)}if(t.length===e.items.length){e.static=join(t);for(let t=0;t<e.items.length;t++){const r=e.items[t];3!==r.type&&9!==r.type||delete r.value}}}}function minify(e){switch(e.t=e.type,e.type){case 0:{const t=e;minify(t.body),t.b=t.body,delete t.body;break}case 1:{const t=e,r=t.cases;for(let e=0;e<r.length;e++)minify(r[e]);t.c=r,delete t.cases;break}case 2:{const t=e,r=t.items;for(let e=0;e<r.length;e++)minify(r[e]);t.i=r,delete t.items,t.static&&(t.s=t.static,delete t.static);break}case 3:case 9:case 8:case 7:{const t=e;t.value&&(t.v=t.value,delete t.value);break}case 6:{const t=e;minify(t.key),t.k=t.key,delete t.key,t.modifier&&(minify(t.modifier),t.m=t.modifier,delete t.modifier);break}case 5:{const t=e;t.i=t.index,delete t.index;break}case 4:{const t=e;t.k=t.key,delete t.key;break}}delete e.type}function createCodeGenerator(e,t){const{sourceMap:r,filename:n,breakLineCode:o,needIndent:s}=t,a=!1!==t.location,i={filename:n,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:o,needIndent:s,indentLevel:0};a&&e.loc&&(i.source=e.loc.source);function c(e,t){i.code+=e}function l(e,t=!0){const r=t?o:"";c(s?r+"  ".repeat(e):r)}return{context:()=>i,push:c,indent:function(e=!0){const t=++i.indentLevel;e&&l(t)},deindent:function(e=!0){const t=--i.indentLevel;e&&l(t)},newline:function(){l(i.indentLevel)},helper:e=>`_${e}`,needIndent:()=>i.needIndent}}function generateLinkedNode(e,t){const{helper:r}=e;e.push(`${r("linked")}(`),generateNode(e,t.key),t.modifier?(e.push(", "),generateNode(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}function generateMessageNode(e,t){const{helper:r,needIndent:n}=e;e.push(`${r("normalize")}([`),e.indent(n());const o=t.items.length;for(let s=0;s<o&&(generateNode(e,t.items[s]),s!==o-1);s++)e.push(", ");e.deindent(n()),e.push("])")}function generatePluralNode(e,t){const{helper:r,needIndent:n}=e;if(t.cases.length>1){e.push(`${r("plural")}([`),e.indent(n());const o=t.cases.length;for(let r=0;r<o&&(generateNode(e,t.cases[r]),r!==o-1);r++)e.push(", ");e.deindent(n()),e.push("])")}}function generateResource(e,t){t.body?generateNode(e,t.body):e.push("null")}function generateNode(e,t){const{helper:r}=e;switch(t.type){case 0:generateResource(e,t);break;case 1:generatePluralNode(e,t);break;case 2:generateMessageNode(e,t);break;case 6:generateLinkedNode(e,t);break;case 8:case 7:case 9:case 3:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${r("interpolate")}(${r("list")}(${t.index}))`,t);break;case 4:e.push(`${r("interpolate")}(${r("named")}(${JSON.stringify(t.key)}))`,t)}}const generate=(e,t={})=>{const r=isString(t.mode)?t.mode:"normal",n=isString(t.filename)?t.filename:"message.intl",o=!!t.sourceMap,s=null!=t.breakLineCode?t.breakLineCode:"arrow"===r?";":"\n",a=t.needIndent?t.needIndent:"arrow"!==r,i=e.helpers||[],c=createCodeGenerator(e,{mode:r,filename:n,sourceMap:o,breakLineCode:s,needIndent:a});c.push("normal"===r?"function __msg__ (ctx) {":"(ctx) => {"),c.indent(a),i.length>0&&(c.push(`const { ${join(i.map((e=>`${e}: _${e}`)),", ")} } = ctx`),c.newline()),c.push("return "),generateNode(c,e),c.deindent(a),c.push("}"),delete e.helpers;const{code:l,map:u}=c.context();return{ast:e,code:l,map:u?u.toJSON():void 0}};function baseCompile$1(e,t={}){const r=assign({},t),n=!!r.jit,o=!!r.minify,s=null==r.optimize||r.optimize,a=createParser(r).parse(e);return n?(s&&optimize(a),o&&minify(a),{ast:a,code:""}):(transform(a,r),generate(a,r))}function isMessageAST(e){return isObject(e)&&0===resolveType(e)&&(hasOwn(e,"b")||hasOwn(e,"body"))}const PROPS_BODY=["b","body"];function resolveBody(e){return resolveProps(e,PROPS_BODY)}const PROPS_CASES=["c","cases"];function resolveCases(e){return resolveProps(e,PROPS_CASES,[])}const PROPS_STATIC=["s","static"];function resolveStatic(e){return resolveProps(e,PROPS_STATIC)}const PROPS_ITEMS=["i","items"];function resolveItems(e){return resolveProps(e,PROPS_ITEMS,[])}const PROPS_TYPE=["t","type"];function resolveType(e){return resolveProps(e,PROPS_TYPE)}const PROPS_VALUE=["v","value"];function resolveValue$1(e,t){const r=resolveProps(e,PROPS_VALUE);if(null!=r)return r;throw createUnhandleNodeError(t)}const PROPS_MODIFIER=["m","modifier"];function resolveLinkedModifier(e){return resolveProps(e,PROPS_MODIFIER)}const PROPS_KEY=["k","key"];function resolveLinkedKey(e){const t=resolveProps(e,PROPS_KEY);if(t)return t;throw createUnhandleNodeError(6)}function resolveProps(e,t,r){for(let n=0;n<t.length;n++){const r=t[n];if(hasOwn(e,r)&&null!=e[r])return e[r]}return r}const AST_NODE_PROPS_KEYS=[...PROPS_BODY,...PROPS_CASES,...PROPS_STATIC,...PROPS_ITEMS,...PROPS_KEY,...PROPS_MODIFIER,...PROPS_VALUE,...PROPS_TYPE];function createUnhandleNodeError(e){return new Error(`unhandled node type: ${e}`)}const pathStateMachine=[];pathStateMachine[0]={w:[0],i:[3,0],"[":[4],o:[7]},pathStateMachine[1]={w:[1],".":[2],"[":[4],o:[7]},pathStateMachine[2]={w:[2],i:[3,0],0:[3,0]},pathStateMachine[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]},pathStateMachine[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]},pathStateMachine[5]={"'":[4,0],o:8,l:[5,0]},pathStateMachine[6]={'"':[4,0],o:8,l:[6,0]};const literalValueRE=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function isLiteral(e){return literalValueRE.test(e)}function stripQuotes(e){const t=e.charCodeAt(0);return t!==e.charCodeAt(e.length-1)||34!==t&&39!==t?e:e.slice(1,-1)}function getPathCharType(e){if(null==e)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function formatSubPath(e){const t=e.trim();return("0"!==e.charAt(0)||!isNaN(parseInt(e)))&&(isLiteral(t)?stripQuotes(t):"*"+t)}function parse(e){const t=[];let r,n,o,s,a,i,c,l=-1,u=0,E=0;const f=[];function _(){const t=e[l+1];if(5===u&&"'"===t||6===u&&'"'===t)return l++,o="\\"+t,f[0](),!0}for(f[0]=()=>{void 0===n?n=o:n+=o},f[1]=()=>{void 0!==n&&(t.push(n),n=void 0)},f[2]=()=>{f[0](),E++},f[3]=()=>{if(E>0)E--,u=4,f[0]();else{if(E=0,void 0===n)return!1;if(n=formatSubPath(n),!1===n)return!1;f[1]()}};null!==u;)if(l++,r=e[l],"\\"!==r||!_()){if(s=getPathCharType(r),c=pathStateMachine[u],a=c[s]||c.l||8,8===a)return;if(u=a[0],void 0!==a[1]&&(i=f[a[1]],i&&(o=r,!1===i())))return;if(7===u)return t}}const cache=new Map;function resolveWithKeyValue(e,t){return isObject(e)?e[t]:null}function resolveValue(e,t){if(!isObject(e))return null;let r=cache.get(t);if(r||(r=parse(t),r&&cache.set(t,r)),!r)return null;const n=r.length;let o=e,s=0;for(;s<n;){const e=r[s];if(AST_NODE_PROPS_KEYS.includes(e)&&isMessageAST(o))return null;const t=o[e];if(void 0===t)return null;if(isFunction(o))return null;o=t,s++}return o}const DEFAULT_MODIFIER=e=>e,DEFAULT_MESSAGE=e=>"",DEFAULT_MESSAGE_DATA_TYPE="text",DEFAULT_NORMALIZE=e=>0===e.length?"":join(e),DEFAULT_INTERPOLATE=toDisplayString;function pluralDefault(e,t){return e=Math.abs(e),2===t?e?e>1?1:0:1:e?Math.min(e,2):0}function getPluralIndex(e){const t=isNumber(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(isNumber(e.named.count)||isNumber(e.named.n))?isNumber(e.named.count)?e.named.count:isNumber(e.named.n)?e.named.n:t:t}function normalizeNamed(e,t){t.count||(t.count=e),t.n||(t.n=e)}function createMessageContext(e={}){const t=e.locale,r=getPluralIndex(e),n=isObject(e.pluralRules)&&isString(t)&&isFunction(e.pluralRules[t])?e.pluralRules[t]:pluralDefault,o=isObject(e.pluralRules)&&isString(t)&&isFunction(e.pluralRules[t])?pluralDefault:void 0,s=e.list||[],a=e.named||create();isNumber(e.pluralIndex)&&normalizeNamed(r,a);function i(t){const r=isFunction(e.messages)?e.messages(t):!!isObject(e.messages)&&e.messages[t];return r||(e.parent?e.parent.message(t):DEFAULT_MESSAGE)}const c=isPlainObject(e.processor)&&isFunction(e.processor.normalize)?e.processor.normalize:DEFAULT_NORMALIZE,l=isPlainObject(e.processor)&&isFunction(e.processor.interpolate)?e.processor.interpolate:DEFAULT_INTERPOLATE,u={list:e=>s[e],named:e=>a[e],plural:e=>e[n(r,e.length,o)],linked:(t,...r)=>{const[n,o]=r;let s="text",a="";1===r.length?isObject(n)?(a=n.modifier||a,s=n.type||s):isString(n)&&(a=n||a):2===r.length&&(isString(n)&&(a=n||a),isString(o)&&(s=o||s));const c=i(t)(u),l="vnode"===s&&isArray(c)&&a?c[0]:c;return a?(E=a,e.modifiers?e.modifiers[E]:DEFAULT_MODIFIER)(l,s):l;var E},message:i,type:isPlainObject(e.processor)&&isString(e.processor.type)?e.processor.type:DEFAULT_MESSAGE_DATA_TYPE,interpolate:l,normalize:c,values:assign(create(),s,a)};return u}let devtools=null;function setDevToolsHook(e){devtools=e}function getDevToolsHook(){return devtools}function initI18nDevTools(e,t,r){devtools&&devtools.emit("i18n:init",{timestamp:Date.now(),i18n:e,version:t,meta:r})}const translateDevTools=createDevToolsHook("function:translate");function createDevToolsHook(e){return t=>devtools&&devtools.emit(e,t)}const code$1=CompileWarnCodes.__EXTEND_POINT__,inc$1=incrementer(code$1),CoreWarnCodes={NOT_FOUND_KEY:code$1,FALLBACK_TO_TRANSLATE:inc$1(),CANNOT_FORMAT_NUMBER:inc$1(),FALLBACK_TO_NUMBER_FORMAT:inc$1(),CANNOT_FORMAT_DATE:inc$1(),FALLBACK_TO_DATE_FORMAT:inc$1(),EXPERIMENTAL_CUSTOM_MESSAGE_COMPILER:inc$1(),__EXTEND_POINT__:inc$1()},warnMessages={[CoreWarnCodes.NOT_FOUND_KEY]:"Not found '{key}' key in '{locale}' locale messages.",[CoreWarnCodes.FALLBACK_TO_TRANSLATE]:"Fall back to translate '{key}' key with '{target}' locale.",[CoreWarnCodes.CANNOT_FORMAT_NUMBER]:"Cannot format a number value due to not supported Intl.NumberFormat.",[CoreWarnCodes.FALLBACK_TO_NUMBER_FORMAT]:"Fall back to number format '{key}' key with '{target}' locale.",[CoreWarnCodes.CANNOT_FORMAT_DATE]:"Cannot format a date value due to not supported Intl.DateTimeFormat.",[CoreWarnCodes.FALLBACK_TO_DATE_FORMAT]:"Fall back to datetime format '{key}' key with '{target}' locale.",[CoreWarnCodes.EXPERIMENTAL_CUSTOM_MESSAGE_COMPILER]:"This project is using Custom Message Compiler, which is an experimental feature. It may receive breaking changes or be removed in the future."};function getWarnMessage(e,...t){return format$1(warnMessages[e],...t)}const code=CompileErrorCodes.__EXTEND_POINT__,inc=incrementer(code),CoreErrorCodes={INVALID_ARGUMENT:code,INVALID_DATE_ARGUMENT:inc(),INVALID_ISO_DATE_ARGUMENT:inc(),NOT_SUPPORT_NON_STRING_MESSAGE:inc(),NOT_SUPPORT_LOCALE_PROMISE_VALUE:inc(),NOT_SUPPORT_LOCALE_ASYNC_FUNCTION:inc(),NOT_SUPPORT_LOCALE_TYPE:inc(),__EXTEND_POINT__:inc()};function createCoreError(e){return createCompileError(e,null,void 0)}function getLocale(e,t){return null!=t.locale?resolveLocale(t.locale):resolveLocale(e.locale)}let _resolveLocale;function resolveLocale(e){if(isString(e))return e;if(isFunction(e)){if(e.resolvedOnce&&null!=_resolveLocale)return _resolveLocale;if("Function"===e.constructor.name){const t=e();if(isPromise(t))throw Error(CoreErrorCodes.NOT_SUPPORT_LOCALE_PROMISE_VALUE);return _resolveLocale=t}throw Error(CoreErrorCodes.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION)}throw Error(CoreErrorCodes.NOT_SUPPORT_LOCALE_TYPE)}function fallbackWithSimple(e,t,r){return[...new Set([r,...isArray(t)?t:isObject(t)?Object.keys(t):isString(t)?[t]:[r]])]}function fallbackWithLocaleChain(e,t,r){const n=isString(r)?r:DEFAULT_LOCALE,o=e;o.__localeChainCache||(o.__localeChainCache=new Map);let s=o.__localeChainCache.get(n);if(!s){s=[];let e=[r];for(;isArray(e);)e=appendBlockToChain(s,e,t);const a=isArray(t)||!isPlainObject(t)?t:t.default?t.default:null;e=isString(a)?[a]:a,isArray(e)&&appendBlockToChain(s,e,!1),o.__localeChainCache.set(n,s)}return s}function appendBlockToChain(e,t,r){let n=!0;for(let o=0;o<t.length&&isBoolean(n);o++){const s=t[o];isString(s)&&(n=appendLocaleToChain(e,t[o],r))}return n}function appendLocaleToChain(e,t,r){let n;const o=t.split("-");do{n=appendItemToChain(e,o.join("-"),r),o.splice(-1,1)}while(o.length&&!0===n);return n}function appendItemToChain(e,t,r){let n=!1;if(!e.includes(t)&&(n=!0,t)){n="!"!==t[t.length-1];const o=t.replace(/!/g,"");e.push(o),(isArray(r)||isPlainObject(r))&&r[o]&&(n=r[o])}return n}CoreErrorCodes.INVALID_ARGUMENT,CoreErrorCodes.INVALID_DATE_ARGUMENT,CoreErrorCodes.INVALID_ISO_DATE_ARGUMENT,CoreErrorCodes.NOT_SUPPORT_NON_STRING_MESSAGE,CoreErrorCodes.NOT_SUPPORT_LOCALE_PROMISE_VALUE,CoreErrorCodes.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION,CoreErrorCodes.NOT_SUPPORT_LOCALE_TYPE;const VERSION="9.14.5",NOT_REOSLVED=-1,DEFAULT_LOCALE="en-US",MISSING_RESOLVE_VALUE="",capitalize=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;function getDefaultLinkedModifiers(){return{upper:(e,t)=>"text"===t&&isString(e)?e.toUpperCase():"vnode"===t&&isObject(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>"text"===t&&isString(e)?e.toLowerCase():"vnode"===t&&isObject(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>"text"===t&&isString(e)?capitalize(e):"vnode"===t&&isObject(e)&&"__v_isVNode"in e?capitalize(e.children):e}}let _compiler,_resolver,_fallbacker;function registerMessageCompiler(e){_compiler=e}function registerMessageResolver(e){_resolver=e}function registerLocaleFallbacker(e){_fallbacker=e}let _additionalMeta=null;const setAdditionalMeta=e=>{_additionalMeta=e},getAdditionalMeta=()=>_additionalMeta;let _fallbackContext=null;const setFallbackContext=e=>{_fallbackContext=e},getFallbackContext=()=>_fallbackContext;let _cid=0;function createCoreContext(e={}){const t=isFunction(e.onWarn)?e.onWarn:warn,r=isString(e.version)?e.version:VERSION,n=isString(e.locale)||isFunction(e.locale)?e.locale:DEFAULT_LOCALE,o=isFunction(n)?DEFAULT_LOCALE:n,s=isArray(e.fallbackLocale)||isPlainObject(e.fallbackLocale)||isString(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:o,a=isPlainObject(e.messages)?e.messages:createResources(o),i=isPlainObject(e.datetimeFormats)?e.datetimeFormats:createResources(o),c=isPlainObject(e.numberFormats)?e.numberFormats:createResources(o),l=assign(create(),e.modifiers,getDefaultLinkedModifiers()),u=e.pluralRules||create(),E=isFunction(e.missing)?e.missing:null,f=!isBoolean(e.missingWarn)&&!isRegExp(e.missingWarn)||e.missingWarn,_=!isBoolean(e.fallbackWarn)&&!isRegExp(e.fallbackWarn)||e.fallbackWarn,m=!!e.fallbackFormat,p=!!e.unresolving,C=isFunction(e.postTranslation)?e.postTranslation:null,d=isPlainObject(e.processor)?e.processor:null,T=!isBoolean(e.warnHtmlMessage)||e.warnHtmlMessage,g=!!e.escapeParameter,N=isFunction(e.messageCompiler)?e.messageCompiler:_compiler,A=isFunction(e.messageResolver)?e.messageResolver:_resolver||resolveWithKeyValue,O=isFunction(e.localeFallbacker)?e.localeFallbacker:_fallbacker||fallbackWithSimple,S=isObject(e.fallbackContext)?e.fallbackContext:void 0,L=e,P=isObject(L.__datetimeFormatters)?L.__datetimeFormatters:new Map,h=isObject(L.__numberFormatters)?L.__numberFormatters:new Map,b=isObject(L.__meta)?L.__meta:{};_cid++;const k={version:r,cid:_cid,locale:n,fallbackLocale:s,messages:a,modifiers:l,pluralRules:u,missing:E,missingWarn:f,fallbackWarn:_,fallbackFormat:m,unresolving:p,postTranslation:C,processor:d,warnHtmlMessage:T,escapeParameter:g,messageCompiler:N,messageResolver:A,localeFallbacker:O,fallbackContext:S,onWarn:t,__meta:b};return k.datetimeFormats=i,k.numberFormats=c,k.__datetimeFormatters=P,k.__numberFormatters=h,k}const createResources=e=>({[e]:create()});function isTranslateFallbackWarn(e,t){return e instanceof RegExp?e.test(t):e}function isTranslateMissingWarn(e,t){return e instanceof RegExp?e.test(t):e}function handleMissing(e,t,r,n,o){const{missing:s,onWarn:a}=e;if(null!==s){const n=s(e,r,t,o);return isString(n)?n:t}return t}function updateFallbackLocale(e,t,r){e.__localeChainCache=new Map,e.localeFallbacker(e,r,t)}function isAlmostSameLocale(e,t){return e!==t&&e.split("-")[0]===t.split("-")[0]}function isImplicitFallback(e,t){const r=t.indexOf(e);if(-1===r)return!1;for(let n=r+1;n<t.length;n++)if(isAlmostSameLocale(e,t[n]))return!0;return!1}function format(e){return t=>formatParts(t,e)}function formatParts(e,t){const r=resolveBody(t);if(null==r)throw createUnhandleNodeError(0);if(1===resolveType(r)){const t=resolveCases(r);return e.plural(t.reduce(((t,r)=>[...t,formatMessageParts(e,r)]),[]))}return formatMessageParts(e,r)}function formatMessageParts(e,t){const r=resolveStatic(t);if(null!=r)return"text"===e.type?r:e.normalize([r]);{const r=resolveItems(t).reduce(((t,r)=>[...t,formatMessagePart(e,r)]),[]);return e.normalize(r)}}function formatMessagePart(e,t){const r=resolveType(t);switch(r){case 3:case 9:case 7:case 8:return resolveValue$1(t,r);case 4:{const n=t;if(hasOwn(n,"k")&&n.k)return e.interpolate(e.named(n.k));if(hasOwn(n,"key")&&n.key)return e.interpolate(e.named(n.key));throw createUnhandleNodeError(r)}case 5:{const n=t;if(hasOwn(n,"i")&&isNumber(n.i))return e.interpolate(e.list(n.i));if(hasOwn(n,"index")&&isNumber(n.index))return e.interpolate(e.list(n.index));throw createUnhandleNodeError(r)}case 6:{const r=t,n=resolveLinkedModifier(r),o=resolveLinkedKey(r);return e.linked(formatMessagePart(e,o),n?formatMessagePart(e,n):void 0,e.type)}default:throw new Error(`unhandled node on format message part: ${r}`)}}const defaultOnCacheKey=e=>e;let compileCache=create();function clearCompileCache(){compileCache=create()}function baseCompile(e,t={}){let r=!1;const n=t.onError||defaultOnError;return t.onError=e=>{r=!0,n(e)},{...baseCompile$1(e,t),detectError:r}}const compileToFunction=(e,t)=>{if(!isString(e))throw Error(CoreErrorCodes.NOT_SUPPORT_NON_STRING_MESSAGE);{!isBoolean(t.warnHtmlMessage)||t.warnHtmlMessage;const r=(t.onCacheKey||defaultOnCacheKey)(e),n=compileCache[r];if(n)return n;const{code:o,detectError:s}=baseCompile(e,t),a=new Function(`return ${o}`)();return s?a:compileCache[r]=a}};function compile(e,t){if(isString(e)){!isBoolean(t.warnHtmlMessage)||t.warnHtmlMessage;const r=(t.onCacheKey||defaultOnCacheKey)(e),n=compileCache[r];if(n)return n;const{ast:o,detectError:s}=baseCompile(e,{...t,location:!1,jit:!0}),a=format(o);return s?a:compileCache[r]=a}{const t=e.cacheKey;if(t){const r=compileCache[t];return r||(compileCache[t]=format(e))}return format(e)}}const NOOP_MESSAGE_FUNCTION=()=>"",isMessageFunction=e=>isFunction(e);function translate(e,...t){const{fallbackFormat:r,postTranslation:n,unresolving:o,messageCompiler:s,fallbackLocale:a,messages:i}=e,[c,l]=parseTranslateArgs(...t),u=isBoolean(l.missingWarn)?l.missingWarn:e.missingWarn,E=isBoolean(l.fallbackWarn)?l.fallbackWarn:e.fallbackWarn,f=isBoolean(l.escapeParameter)?l.escapeParameter:e.escapeParameter,_=!!l.resolvedMessage,m=isString(l.default)||isBoolean(l.default)?isBoolean(l.default)?s?c:()=>c:l.default:r?s?c:()=>c:"",p=r||""!==m,C=getLocale(e,l);f&&escapeParams(l);let[d,T,g]=_?[c,C,i[C]||create()]:resolveMessageFormat(e,c,C,a,E,u),N=d,A=c;if(_||isString(N)||isMessageAST(N)||isMessageFunction(N)||p&&(N=m,A=N),!(_||(isString(N)||isMessageAST(N)||isMessageFunction(N))&&isString(T)))return o?-1:c;let O=!1;const S=isMessageFunction(N)?N:compileMessageFormat(e,c,T,N,A,(()=>{O=!0}));if(O)return N;const L=evaluateMessage(e,S,createMessageContext(getMessageContextOptions(e,T,g,l)));let P=n?n(L,c):L;return f&&isString(P)&&(P=sanitizeTranslatedHtml(P)),P}function escapeParams(e){isArray(e.list)?e.list=e.list.map((e=>isString(e)?escapeHtml(e):e)):isObject(e.named)&&Object.keys(e.named).forEach((t=>{isString(e.named[t])&&(e.named[t]=escapeHtml(e.named[t]))}))}function resolveMessageFormat(e,t,r,n,o,s){const{messages:a,onWarn:i,messageResolver:c,localeFallbacker:l}=e,u=l(e,n,r);let E,f=create(),_=null;for(let m=0;m<u.length&&(E=u[m],f=a[E]||create(),null===(_=c(f,t))&&(_=f[t]),!(isString(_)||isMessageAST(_)||isMessageFunction(_)));m++)if(!isImplicitFallback(E,u)){const r=handleMissing(e,t,E,s,"translate");r!==t&&(_=r)}return[_,E,f]}function compileMessageFormat(e,t,r,n,o,s){const{messageCompiler:a,warnHtmlMessage:i}=e;if(isMessageFunction(n)){const e=n;return e.locale=e.locale||r,e.key=e.key||t,e}if(null==a){const e=()=>n;return e.locale=r,e.key=t,e}const c=a(n,getCompileContext(e,r,o,n,i,s));return c.locale=r,c.key=t,c.source=n,c}function evaluateMessage(e,t,r){return t(r)}function parseTranslateArgs(...e){const[t,r,n]=e,o=create();if(!(isString(t)||isNumber(t)||isMessageFunction(t)||isMessageAST(t)))throw Error(CoreErrorCodes.INVALID_ARGUMENT);const s=isNumber(t)?String(t):(isMessageFunction(t),t);return isNumber(r)?o.plural=r:isString(r)?o.default=r:isPlainObject(r)&&!isEmptyObject(r)?o.named=r:isArray(r)&&(o.list=r),isNumber(n)?o.plural=n:isString(n)?o.default=n:isPlainObject(n)&&assign(o,n),[s,o]}function getCompileContext(e,t,r,n,o,s){return{locale:t,key:r,warnHtmlMessage:o,onError:e=>{throw s&&s(e),e},onCacheKey:e=>generateFormatCacheKey(t,r,e)}}function getMessageContextOptions(e,t,r,n){const{modifiers:o,pluralRules:s,messageResolver:a,fallbackLocale:i,fallbackWarn:c,missingWarn:l,fallbackContext:u}=e,E={locale:t,modifiers:o,pluralRules:s,messages:n=>{let o=a(r,n);if(null==o&&u){const[,,e]=resolveMessageFormat(u,n,t,i,c,l);o=a(e,n)}if(isString(o)||isMessageAST(o)){let r=!1;const s=compileMessageFormat(e,n,t,o,n,(()=>{r=!0}));return r?NOOP_MESSAGE_FUNCTION:s}return isMessageFunction(o)?o:NOOP_MESSAGE_FUNCTION}};return e.processor&&(E.processor=e.processor),n.list&&(E.list=n.list),n.named&&(E.named=n.named),isNumber(n.plural)&&(E.pluralIndex=n.plural),E}function datetime(e,...t){const{datetimeFormats:r,unresolving:n,fallbackLocale:o,onWarn:s,localeFallbacker:a}=e,{__datetimeFormatters:i}=e,[c,l,u,E]=parseDateTimeArgs(...t),f=isBoolean(u.missingWarn)?u.missingWarn:e.missingWarn;isBoolean(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn;const _=!!u.part,m=getLocale(e,u),p=a(e,o,m);if(!isString(c)||""===c)return new Intl.DateTimeFormat(m,E).format(l);let C,d={},T=null;for(let A=0;A<p.length&&(C=p[A],d=r[C]||{},T=d[c],!isPlainObject(T));A++)handleMissing(e,c,C,f,"datetime format");if(!isPlainObject(T)||!isString(C))return n?-1:c;let g=`${C}__${c}`;isEmptyObject(E)||(g=`${g}__${JSON.stringify(E)}`);let N=i.get(g);return N||(N=new Intl.DateTimeFormat(C,assign({},T,E)),i.set(g,N)),_?N.formatToParts(l):N.format(l)}const DATETIME_FORMAT_OPTIONS_KEYS=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function parseDateTimeArgs(...e){const[t,r,n,o]=e,s=create();let a,i=create();if(isString(t)){const e=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!e)throw Error(CoreErrorCodes.INVALID_ISO_DATE_ARGUMENT);const r=e[3]?e[3].trim().startsWith("T")?`${e[1].trim()}${e[3].trim()}`:`${e[1].trim()}T${e[3].trim()}`:e[1].trim();a=new Date(r);try{a.toISOString()}catch(c){throw Error(CoreErrorCodes.INVALID_ISO_DATE_ARGUMENT)}}else if(isDate(t)){if(isNaN(t.getTime()))throw Error(CoreErrorCodes.INVALID_DATE_ARGUMENT);a=t}else{if(!isNumber(t))throw Error(CoreErrorCodes.INVALID_ARGUMENT);a=t}return isString(r)?s.key=r:isPlainObject(r)&&Object.keys(r).forEach((e=>{DATETIME_FORMAT_OPTIONS_KEYS.includes(e)?i[e]=r[e]:s[e]=r[e]})),isString(n)?s.locale=n:isPlainObject(n)&&(i=n),isPlainObject(o)&&(i=o),[s.key||"",a,s,i]}function clearDateTimeFormat(e,t,r){const n=e;for(const o in r){const e=`${t}__${o}`;n.__datetimeFormatters.has(e)&&n.__datetimeFormatters.delete(e)}}function number(e,...t){const{numberFormats:r,unresolving:n,fallbackLocale:o,onWarn:s,localeFallbacker:a}=e,{__numberFormatters:i}=e,[c,l,u,E]=parseNumberArgs(...t),f=isBoolean(u.missingWarn)?u.missingWarn:e.missingWarn;isBoolean(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn;const _=!!u.part,m=getLocale(e,u),p=a(e,o,m);if(!isString(c)||""===c)return new Intl.NumberFormat(m,E).format(l);let C,d={},T=null;for(let A=0;A<p.length&&(C=p[A],d=r[C]||{},T=d[c],!isPlainObject(T));A++)handleMissing(e,c,C,f,"number format");if(!isPlainObject(T)||!isString(C))return n?-1:c;let g=`${C}__${c}`;isEmptyObject(E)||(g=`${g}__${JSON.stringify(E)}`);let N=i.get(g);return N||(N=new Intl.NumberFormat(C,assign({},T,E)),i.set(g,N)),_?N.formatToParts(l):N.format(l)}const NUMBER_FORMAT_OPTIONS_KEYS=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function parseNumberArgs(...e){const[t,r,n,o]=e,s=create();let a=create();if(!isNumber(t))throw Error(CoreErrorCodes.INVALID_ARGUMENT);const i=t;return isString(r)?s.key=r:isPlainObject(r)&&Object.keys(r).forEach((e=>{NUMBER_FORMAT_OPTIONS_KEYS.includes(e)?a[e]=r[e]:s[e]=r[e]})),isString(n)?s.locale=n:isPlainObject(n)&&(a=n),isPlainObject(o)&&(a=o),[s.key||"",i,s,a]}function clearNumberFormat(e,t,r){const n=e;for(const o in r){const e=`${t}__${o}`;n.__numberFormatters.has(e)&&n.__numberFormatters.delete(e)}}export{AST_NODE_PROPS_KEYS,CompileErrorCodes,CoreErrorCodes,CoreWarnCodes,DATETIME_FORMAT_OPTIONS_KEYS,DEFAULT_LOCALE,DEFAULT_MESSAGE_DATA_TYPE,MISSING_RESOLVE_VALUE,NOT_REOSLVED,NUMBER_FORMAT_OPTIONS_KEYS,VERSION,clearCompileCache,clearDateTimeFormat,clearNumberFormat,compile,compileToFunction,createCompileError,createCoreContext,createCoreError,createMessageContext,datetime,fallbackWithLocaleChain,fallbackWithSimple,getAdditionalMeta,getDevToolsHook,getFallbackContext,getLocale,getWarnMessage,handleMissing,initI18nDevTools,isAlmostSameLocale,isImplicitFallback,isMessageAST,isMessageFunction,isTranslateFallbackWarn,isTranslateMissingWarn,number,parse,parseDateTimeArgs,parseNumberArgs,parseTranslateArgs,registerLocaleFallbacker,registerMessageCompiler,registerMessageResolver,resolveLocale,resolveValue,resolveWithKeyValue,setAdditionalMeta,setDevToolsHook,setFallbackContext,translate,translateDevTools,updateFallbackLocale};

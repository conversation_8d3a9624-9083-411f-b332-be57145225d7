# 文件管理功能说明

## 功能概述

在管理后台的数据表格管理页面中，现在可以直接对每个 Accession 的各种文件类型进行管理操作。

## 新增功能

### 1. 文件类型列显示
替换了原来的"文件状态"列，现在每种文件类型都有独立的列：
- **Genome** - 基因组文件
- **Annotation** - 注释文件
- **Transcriptome** - 转录组文件
- **Codon** - 密码子文件
- **Centromere** - 着丝粒文件
- **TEs** - 转座子文件
- **CoreBlocks** - 核心区块文件
- **miRNA** - 微RNA文件
- **tRNA** - 转运RNA文件
- **rRNA** - 核糖体RNA文件

### 2. 文件显示方式
每个文件类型列的显示方式：

#### 📄 文件存在时
- **显示**: 完整的文件名（如 `genome.IR64.fasta`）
- **功能**: 点击文件名可直接下载文件
- **样式**: 蓝色链接，鼠标悬停时有下划线

#### ➖ 文件不存在时
- **显示**: 灰色的 `-` 符号
- **功能**: 无操作，仅作为占位符

### 3. 文件管理功能集成到编辑面板
上传和删除功能已移动到新增/编辑 Accession 的对话框中：

#### 📤 文件上传
- **位置**: 编辑对话框的"文件管理"部分
- **功能**:
  - 选择本地文件进行上传
  - 如果已存在文件，上传新文件将覆盖原文件
  - 支持多个文件类型同时管理
- **支持格式**:
  - Genome: `.fasta`, `.fa`, `.fas`
  - Annotation: `.gff`, `.gff3`
  - Transcriptome: `.tar.gz`
  - Codon: `.tar.gz`
  - Centromere: `.bed`
  - TEs: `.tar.gz`
  - CoreBlocks: `.bed`
  - miRNA: `.bed`
  - tRNA: `.bed`
  - rRNA: `.bed`

#### 🗑️ 文件删除
- **位置**: 编辑对话框的"文件管理"部分
- **功能**: 删除已存在的文件
- **显示条件**: 只有当文件存在时才显示删除按钮

## 技术实现

### 前端功能
- **文件上传**: 使用 HTML5 File API 和 FormData
- **文件下载**: 通过 Blob 和 URL.createObjectURL 实现
- **文件删除**: 带确认对话框的安全删除
- **状态更新**: 操作完成后自动刷新表格数据

### 后端API

#### 1. 文件上传 API
```
POST /admin/data-management/upload-file/
```
- **参数**: 
  - `file`: 上传的文件
  - `accession`: Accession标识
  - `fileType`: 文件类型
- **功能**: 
  - 验证文件格式
  - 自动备份现有文件
  - 保存新文件到 `manual_files/` 目录

#### 2. 文件下载 API
```
GET /admin/data-management/download-file/{accession}/{file_type}/
```
- **功能**: 
  - 查找对应的文件
  - 返回文件流供下载

#### 3. 文件删除 API
```
DELETE /admin/data-management/delete-file/{accession}/{file_type}/
```
- **功能**: 
  - 查找并删除对应的文件
  - 支持删除多个匹配的文件（如转录组的多个组织类型）

### 文件命名规则
所有文件都遵循统一的命名规则：
```
{fileType}.{accession}.{extension}
```

例如：
- `genome.IR64.fasta`
- `annotation.IR64.gff`
- `transcriptome.root.IR64.tar.gz`
- `codon.IR64.tar.gz`
- `centromere.IR64.bed`

### 安全特性
1. **文件格式验证**: 严格检查文件扩展名
2. **直接覆盖**: 上传新文件时直接删除现有文件（无备份）
3. **错误处理**: 完善的错误提示和日志记录

## 使用方法

### 1. 查看文件状态
- 在数据表格中，每个文件类型列会显示：
  - 文件存在：显示完整文件名（可点击下载）
  - 文件不存在：显示灰色 `-` 符号

### 2. 下载文件
- **方法**: 直接点击表格中显示的文件名
- **结果**: 文件将自动下载到浏览器默认下载目录

### 3. 管理文件（上传/删除）
- **入口**: 点击表格中的"编辑"按钮，或"新增 Accession"按钮
- **位置**: 在弹出的对话框中找到"文件管理"部分

#### 上传文件：
1. 在文件管理部分找到对应的文件类型
2. 点击"选择文件"按钮
3. 从本地选择符合格式要求的文件
4. 文件名会显示在界面上
5. 点击"保存"按钮完成上传

#### 删除文件：
1. 在文件管理部分找到要删除的文件类型
2. 点击红色的"删除"按钮
3. 点击"保存"按钮确认删除

#### 覆盖文件：
- 如果文件已存在，选择新文件上传将直接覆盖原文件
- **注意**: 原文件将被永久删除，无法恢复

## 注意事项

1. **文件覆盖**: 上传新文件时会直接删除现有文件，**无法恢复**，请谨慎操作
2. **文件大小**: 建议单个文件不超过 100MB
3. **并发操作**: 避免同时对同一文件进行多个操作
4. **权限要求**: 需要管理员权限才能进行文件操作
5. **数据同步**: 文件操作完成后会自动刷新表格显示状态

## 故障排除

1. **上传失败**: 检查文件格式是否正确，文件是否损坏
2. **下载失败**: 检查文件是否存在，网络连接是否正常
3. **删除失败**: 检查文件权限，是否被其他程序占用
4. **状态不更新**: 手动点击"刷新数据"按钮

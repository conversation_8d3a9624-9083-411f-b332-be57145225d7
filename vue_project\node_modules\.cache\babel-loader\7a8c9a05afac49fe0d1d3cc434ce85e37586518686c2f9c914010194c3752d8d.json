{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { ref, reactive, onMounted } from 'vue';\nimport { useRouter } from 'vue-router';\nimport { ElMessage } from 'element-plus';\nimport { DataBoard, Document, FolderOpened, Check, Close } from '@element-plus/icons-vue';\nimport axios from 'axios';\nimport AdminFileManager from './AdminFileManager.vue';\nimport AdminDataManager from './AdminDataManager.vue';\nexport default {\n  name: 'AdminDashboard',\n  components: {\n    AdminFileManager,\n    AdminDataManager,\n    DataBoard,\n    Document,\n    FolderOpened,\n    Check,\n    Close\n  },\n  setup() {\n    const router = useRouter();\n    const activeMenu = ref('dashboard');\n    const statsLoading = ref(false);\n    const subpopStatsLoading = ref(false);\n    const userInfo = reactive({\n      username: 'root'\n    });\n    const statistics = reactive({\n      total_files: 0,\n      total_size_mb: 0,\n      existing_files: 0,\n      missing_files: 0,\n      category_stats: {},\n      organism_stats: {}\n    });\n    const subpopulationStats = ref([]);\n\n    // 检查登录状态\n    const checkAuth = () => {\n      const token = localStorage.getItem('admin_token');\n      const user = localStorage.getItem('admin_user');\n      if (!token || !user) {\n        router.push('/admin/login');\n        return false;\n      }\n      try {\n        const userData = JSON.parse(user);\n        userInfo.username = userData.username;\n      } catch (e) {\n        router.push('/admin/login');\n        return false;\n      }\n      return true;\n    };\n\n    // 加载统计信息\n    const loadStatistics = async () => {\n      try {\n        statsLoading.value = true;\n        const response = await axios.get('/admin/statistics/');\n        if (response.data.success) {\n          Object.assign(statistics, response.data.data);\n        }\n      } catch (error) {\n        console.error('加载统计信息失败:', error);\n        ElMessage.error('加载统计信息失败');\n      } finally {\n        statsLoading.value = false;\n      }\n    };\n\n    // 加载亚群统计信息\n    const loadSubpopulationStats = async () => {\n      try {\n        subpopStatsLoading.value = true;\n        const response = await axios.get('/admin/subpopulation-stats/');\n        if (response.data.success) {\n          subpopulationStats.value = response.data.data;\n        }\n      } catch (error) {\n        console.error('加载亚群统计失败:', error);\n        ElMessage.error('加载亚群统计失败');\n      } finally {\n        subpopStatsLoading.value = false;\n      }\n    };\n\n    // 获取亚群样式类名\n    const getSubPopulationClass = subPopulation => {\n      const classMap = {\n        'cA': 'sub-pop-ca',\n        'cB': 'sub-pop-cb',\n        'GJ': 'sub-pop-gj',\n        'XI': 'sub-pop-xi',\n        'WILD': 'sub-pop-wild',\n        'O.glaberrima': 'sub-pop-glaberrima',\n        '-': 'sub-pop-unknown'\n      };\n      return classMap[subPopulation] || 'sub-pop-default';\n    };\n\n    // 格式化文件大小\n    const formatFileSize = bytes => {\n      if (!bytes || bytes === 0) return '0 B';\n      const k = 1024;\n      const sizes = ['B', 'KB', 'MB', 'GB'];\n      const i = Math.floor(Math.log(bytes) / Math.log(k));\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    };\n\n    // 菜单选择\n    const handleMenuSelect = index => {\n      activeMenu.value = index;\n    };\n\n    // 退出登录\n    const handleLogout = () => {\n      localStorage.removeItem('admin_token');\n      localStorage.removeItem('admin_user');\n      ElMessage.success('已退出登录');\n      router.push('/admin/login');\n    };\n    onMounted(() => {\n      if (checkAuth()) {\n        loadStatistics();\n        loadSubpopulationStats();\n      }\n    });\n    return {\n      activeMenu,\n      userInfo,\n      statistics,\n      statsLoading,\n      subpopulationStats,\n      subpopStatsLoading,\n      handleMenuSelect,\n      handleLogout,\n      loadStatistics,\n      loadSubpopulationStats,\n      getSubPopulationClass,\n      formatFileSize\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "onMounted", "useRouter", "ElMessage", "DataBoard", "Document", "FolderOpened", "Check", "Close", "axios", "AdminFileManager", "AdminDataManager", "name", "components", "setup", "router", "activeMenu", "statsLoading", "subpopStatsLoading", "userInfo", "username", "statistics", "total_files", "total_size_mb", "existing_files", "missing_files", "category_stats", "organism_stats", "subpopulationStats", "checkAuth", "token", "localStorage", "getItem", "user", "push", "userData", "JSON", "parse", "e", "loadStatistics", "value", "response", "get", "data", "success", "Object", "assign", "error", "console", "loadSubpopulationStats", "getSubPopulationClass", "subPopulation", "classMap", "formatFileSize", "bytes", "k", "sizes", "i", "Math", "floor", "log", "parseFloat", "pow", "toFixed", "handleMenuSelect", "index", "handleLogout", "removeItem"], "sources": ["D:\\gene_manage_system\\vue_project\\src\\views\\AdminDashboard.vue"], "sourcesContent": ["<template>\n  <div class=\"admin-dashboard\">\n    <!-- 顶部导航栏 -->\n    <div class=\"admin-header\">\n      <div class=\"header-left\">\n        <h1>基因数据管理系统</h1>\n      </div>\n      <div class=\"header-right\">\n        <span class=\"user-info\">欢迎，{{ userInfo.username }}</span>\n        <el-button type=\"danger\" size=\"small\" @click=\"handleLogout\">退出登录</el-button>\n      </div>\n    </div>\n    \n    <!-- 主要内容区域 -->\n    <div class=\"admin-content\">\n      <!-- 侧边菜单 -->\n      <div class=\"admin-sidebar\">\n        <el-menu\n          :default-active=\"activeMenu\"\n          class=\"sidebar-menu\"\n          @select=\"handleMenuSelect\"\n        >\n          <el-menu-item index=\"dashboard\">\n            <el-icon><DataBoard /></el-icon>\n            <span>数据统计</span>\n          </el-menu-item>\n          <el-menu-item index=\"files\">\n            <el-icon><Document /></el-icon>\n            <span>文件管理</span>\n          </el-menu-item>\n          <el-menu-item index=\"data-management\">\n            <el-icon><FolderOpened /></el-icon>\n            <span>数据表格管理</span>\n          </el-menu-item>\n        </el-menu>\n      </div>\n      \n      <!-- 主内容区 -->\n      <div class=\"admin-main\">\n        <!-- 数据统计页面 -->\n        <div v-if=\"activeMenu === 'dashboard'\" class=\"dashboard-content\">\n          <h2>数据统计</h2>\n          \n          <div class=\"stats-cards\" v-loading=\"statsLoading\">\n            <div class=\"stat-card\">\n              <div class=\"stat-icon\">\n                <el-icon><Document /></el-icon>\n              </div>\n              <div class=\"stat-info\">\n                <div class=\"stat-number\">{{ statistics.total_files || 0 }}</div>\n                <div class=\"stat-label\">总文件数</div>\n              </div>\n            </div>\n\n            <div class=\"stat-card\">\n              <div class=\"stat-icon\">\n                <el-icon><FolderOpened /></el-icon>\n              </div>\n              <div class=\"stat-info\">\n                <div class=\"stat-number\">{{ statistics.total_size_mb || 0 }} MB</div>\n                <div class=\"stat-label\">总文件大小</div>\n              </div>\n            </div>\n          </div>\n\n          <!-- 亚群统计 -->\n          <div class=\"subpopulation-stats\">\n            <h3>按亚群统计</h3>\n            <div class=\"stats-table\" v-loading=\"subpopStatsLoading\">\n              <div class=\"stats-header\">\n                <span class=\"stats-col-1\">亚群</span>\n                <span class=\"stats-col-2\">Accession数量</span>\n                <span class=\"stats-col-3\">文件数</span>\n                <span class=\"stats-col-4\">文件总大小</span>\n              </div>\n              <div v-for=\"stat in subpopulationStats\" :key=\"stat.subpopulation\" class=\"stats-row\">\n                <span class=\"stats-col-1\">{{ stat.subpopulation }}</span>\n                <span class=\"stats-col-2\">{{ stat.accession_count }}</span>\n                <span class=\"stats-col-3\">{{ stat.file_count }}</span>\n                <span class=\"stats-col-4\">{{ formatFileSize(stat.total_size) }}</span>\n              </div>\n            </div>\n          </div>\n\n          <!-- 分类统计 -->\n          <div class=\"category-stats\">\n            <h3>按类别统计</h3>\n            <div class=\"stats-table\">\n              <div class=\"stats-header\">\n                <span class=\"stats-col-1\">类别</span>\n                <span class=\"stats-col-2\">文件数量</span>\n              </div>\n              <div v-for=\"(count, category) in statistics.category_stats\" :key=\"category\" class=\"stats-row\">\n                <span class=\"stats-col-1\">{{ category }}</span>\n                <span class=\"stats-col-2\">{{ count }}</span>\n              </div>\n            </div>\n          </div>\n          \n\n        </div>\n\n        <!-- 文件管理页面 -->\n        <AdminFileManager v-if=\"activeMenu === 'files'\" @refresh-stats=\"loadStatistics\" />\n\n        <!-- 数据表格管理页面 -->\n        <AdminDataManager v-if=\"activeMenu === 'data-management'\" />\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, onMounted } from 'vue'\nimport { useRouter } from 'vue-router'\nimport { ElMessage } from 'element-plus'\nimport { \n  DataBoard, Document, FolderOpened, Check, Close \n} from '@element-plus/icons-vue'\nimport axios from 'axios'\nimport AdminFileManager from './AdminFileManager.vue'\nimport AdminDataManager from './AdminDataManager.vue'\n\nexport default {\n  name: 'AdminDashboard',\n  components: {\n    AdminFileManager,\n    AdminDataManager,\n    DataBoard,\n    Document,\n    FolderOpened,\n    Check,\n    Close\n  },\n  setup() {\n    const router = useRouter()\n    const activeMenu = ref('dashboard')\n    const statsLoading = ref(false)\n    const subpopStatsLoading = ref(false)\n\n    const userInfo = reactive({\n      username: 'root'\n    })\n\n    const statistics = reactive({\n      total_files: 0,\n      total_size_mb: 0,\n      existing_files: 0,\n      missing_files: 0,\n      category_stats: {},\n      organism_stats: {}\n    })\n\n    const subpopulationStats = ref([])\n    \n    // 检查登录状态\n    const checkAuth = () => {\n      const token = localStorage.getItem('admin_token')\n      const user = localStorage.getItem('admin_user')\n      \n      if (!token || !user) {\n        router.push('/admin/login')\n        return false\n      }\n      \n      try {\n        const userData = JSON.parse(user)\n        userInfo.username = userData.username\n      } catch (e) {\n        router.push('/admin/login')\n        return false\n      }\n      \n      return true\n    }\n    \n    // 加载统计信息\n    const loadStatistics = async () => {\n      try {\n        statsLoading.value = true\n        const response = await axios.get('/admin/statistics/')\n\n        if (response.data.success) {\n          Object.assign(statistics, response.data.data)\n        }\n      } catch (error) {\n        console.error('加载统计信息失败:', error)\n        ElMessage.error('加载统计信息失败')\n      } finally {\n        statsLoading.value = false\n      }\n    }\n\n    // 加载亚群统计信息\n    const loadSubpopulationStats = async () => {\n      try {\n        subpopStatsLoading.value = true\n        const response = await axios.get('/admin/subpopulation-stats/')\n\n        if (response.data.success) {\n          subpopulationStats.value = response.data.data\n        }\n      } catch (error) {\n        console.error('加载亚群统计失败:', error)\n        ElMessage.error('加载亚群统计失败')\n      } finally {\n        subpopStatsLoading.value = false\n      }\n    }\n\n    // 获取亚群样式类名\n    const getSubPopulationClass = (subPopulation) => {\n      const classMap = {\n        'cA': 'sub-pop-ca',\n        'cB': 'sub-pop-cb',\n        'GJ': 'sub-pop-gj',\n        'XI': 'sub-pop-xi',\n        'WILD': 'sub-pop-wild',\n        'O.glaberrima': 'sub-pop-glaberrima',\n        '-': 'sub-pop-unknown'\n      }\n      return classMap[subPopulation] || 'sub-pop-default'\n    }\n\n    // 格式化文件大小\n    const formatFileSize = (bytes) => {\n      if (!bytes || bytes === 0) return '0 B'\n      const k = 1024\n      const sizes = ['B', 'KB', 'MB', 'GB']\n      const i = Math.floor(Math.log(bytes) / Math.log(k))\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n    }\n    \n    // 菜单选择\n    const handleMenuSelect = (index) => {\n      activeMenu.value = index\n    }\n    \n    // 退出登录\n    const handleLogout = () => {\n      localStorage.removeItem('admin_token')\n      localStorage.removeItem('admin_user')\n      ElMessage.success('已退出登录')\n      router.push('/admin/login')\n    }\n    \n    onMounted(() => {\n      if (checkAuth()) {\n        loadStatistics()\n        loadSubpopulationStats()\n      }\n    })\n\n    return {\n      activeMenu,\n      userInfo,\n      statistics,\n      statsLoading,\n      subpopulationStats,\n      subpopStatsLoading,\n      handleMenuSelect,\n      handleLogout,\n      loadStatistics,\n      loadSubpopulationStats,\n      getSubPopulationClass,\n      formatFileSize\n    }\n  }\n}\n</script>\n\n<style scoped>\n.admin-dashboard {\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n}\n\n.admin-header {\n  height: 60px;\n  background: #409eff;\n  color: white;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 20px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.header-left h1 {\n  margin: 0;\n  font-size: 20px;\n  font-weight: 500;\n}\n\n.header-right {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.user-info {\n  font-size: 14px;\n}\n\n.admin-content {\n  flex: 1;\n  display: flex;\n  overflow: hidden;\n}\n\n.admin-sidebar {\n  width: 200px;\n  background: #001529;\n  border-right: 1px solid #e6e6e6;\n}\n\n.sidebar-menu {\n  border: none;\n  background: transparent;\n}\n\n/* 菜单项样式 */\n.sidebar-menu .el-menu-item {\n  color: rgba(255, 255, 255, 0.65) !important;\n  background-color: transparent !important;\n}\n\n.sidebar-menu .el-menu-item:hover {\n  color: #fff !important;\n  background-color: #1890ff !important;\n}\n\n.sidebar-menu .el-menu-item.is-active {\n  color: #fff !important;\n  background-color: #1890ff !important;\n}\n\n.admin-main {\n  flex: 1;\n  padding: 20px;\n  overflow-y: auto;\n}\n\n.dashboard-content h2 {\n  margin-top: 0;\n  margin-bottom: 20px;\n  color: #333;\n}\n\n.stats-cards {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 30px;\n}\n\n.stat-card {\n  background: white;\n  padding: 20px;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.stat-icon {\n  width: 50px;\n  height: 50px;\n  background: #409eff;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 20px;\n}\n\n.stat-number {\n  font-size: 24px;\n  font-weight: bold;\n  color: #333;\n}\n\n.stat-label {\n  font-size: 14px;\n  color: #666;\n  margin-top: 5px;\n}\n\n.subpopulation-stats, .category-stats {\n  background: white;\n  padding: 20px;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n  margin-bottom: 20px;\n}\n\n.subpopulation-stats h3, .category-stats h3 {\n  margin-top: 0;\n  margin-bottom: 15px;\n  color: #333;\n}\n\n.stats-table {\n  border: 1px solid #e0e0e0;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.stats-header {\n  display: flex;\n  background: #f5f5f5;\n  font-weight: bold;\n  color: #333;\n  border-bottom: 1px solid #e0e0e0;\n}\n\n.stats-row {\n  display: flex;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.stats-row:last-child {\n  border-bottom: none;\n}\n\n.stats-row:hover {\n  background: #f9f9f9;\n}\n\n/* 亚群统计的列宽 */\n.subpopulation-stats .stats-col-1 {\n  flex: 1;\n  padding: 12px 15px;\n  color: #333;\n}\n\n.subpopulation-stats .stats-col-2,\n.subpopulation-stats .stats-col-3 {\n  width: 120px;\n  padding: 12px 15px;\n  text-align: center;\n  color: #409eff;\n}\n\n.subpopulation-stats .stats-col-4 {\n  width: 140px;\n  padding: 12px 15px;\n  text-align: right;\n  color: #67c23a;\n}\n\n/* 类别统计的列宽 */\n.category-stats .stats-col-1 {\n  flex: 1;\n  padding: 12px 15px;\n  color: #333;\n}\n\n.category-stats .stats-col-2 {\n  width: 120px;\n  padding: 12px 15px;\n  text-align: center;\n  color: #409eff;\n  font-weight: bold;\n}\n</style>\n"], "mappings": ";AAiHA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAQ,QAAS,KAAI;AAC7C,SAASC,SAAQ,QAAS,YAAW;AACrC,SAASC,SAAQ,QAAS,cAAa;AACvC,SACEC,SAAS,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,KAAK,EAAEC,KAAI,QACzC,yBAAwB;AAC/B,OAAOC,KAAI,MAAO,OAAM;AACxB,OAAOC,gBAAe,MAAO,wBAAuB;AACpD,OAAOC,gBAAe,MAAO,wBAAuB;AAEpD,eAAe;EACbC,IAAI,EAAE,gBAAgB;EACtBC,UAAU,EAAE;IACVH,gBAAgB;IAChBC,gBAAgB;IAChBP,SAAS;IACTC,QAAQ;IACRC,YAAY;IACZC,KAAK;IACLC;EACF,CAAC;EACDM,KAAKA,CAAA,EAAG;IACN,MAAMC,MAAK,GAAIb,SAAS,CAAC;IACzB,MAAMc,UAAS,GAAIjB,GAAG,CAAC,WAAW;IAClC,MAAMkB,YAAW,GAAIlB,GAAG,CAAC,KAAK;IAC9B,MAAMmB,kBAAiB,GAAInB,GAAG,CAAC,KAAK;IAEpC,MAAMoB,QAAO,GAAInB,QAAQ,CAAC;MACxBoB,QAAQ,EAAE;IACZ,CAAC;IAED,MAAMC,UAAS,GAAIrB,QAAQ,CAAC;MAC1BsB,WAAW,EAAE,CAAC;MACdC,aAAa,EAAE,CAAC;MAChBC,cAAc,EAAE,CAAC;MACjBC,aAAa,EAAE,CAAC;MAChBC,cAAc,EAAE,CAAC,CAAC;MAClBC,cAAc,EAAE,CAAC;IACnB,CAAC;IAED,MAAMC,kBAAiB,GAAI7B,GAAG,CAAC,EAAE;;IAEjC;IACA,MAAM8B,SAAQ,GAAIA,CAAA,KAAM;MACtB,MAAMC,KAAI,GAAIC,YAAY,CAACC,OAAO,CAAC,aAAa;MAChD,MAAMC,IAAG,GAAIF,YAAY,CAACC,OAAO,CAAC,YAAY;MAE9C,IAAI,CAACF,KAAI,IAAK,CAACG,IAAI,EAAE;QACnBlB,MAAM,CAACmB,IAAI,CAAC,cAAc;QAC1B,OAAO,KAAI;MACb;MAEA,IAAI;QACF,MAAMC,QAAO,GAAIC,IAAI,CAACC,KAAK,CAACJ,IAAI;QAChCd,QAAQ,CAACC,QAAO,GAAIe,QAAQ,CAACf,QAAO;MACtC,EAAE,OAAOkB,CAAC,EAAE;QACVvB,MAAM,CAACmB,IAAI,CAAC,cAAc;QAC1B,OAAO,KAAI;MACb;MAEA,OAAO,IAAG;IACZ;;IAEA;IACA,MAAMK,cAAa,GAAI,MAAAA,CAAA,KAAY;MACjC,IAAI;QACFtB,YAAY,CAACuB,KAAI,GAAI,IAAG;QACxB,MAAMC,QAAO,GAAI,MAAMhC,KAAK,CAACiC,GAAG,CAAC,oBAAoB;QAErD,IAAID,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;UACzBC,MAAM,CAACC,MAAM,CAACzB,UAAU,EAAEoB,QAAQ,CAACE,IAAI,CAACA,IAAI;QAC9C;MACF,EAAE,OAAOI,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChC5C,SAAS,CAAC4C,KAAK,CAAC,UAAU;MAC5B,UAAU;QACR9B,YAAY,CAACuB,KAAI,GAAI,KAAI;MAC3B;IACF;;IAEA;IACA,MAAMS,sBAAqB,GAAI,MAAAA,CAAA,KAAY;MACzC,IAAI;QACF/B,kBAAkB,CAACsB,KAAI,GAAI,IAAG;QAC9B,MAAMC,QAAO,GAAI,MAAMhC,KAAK,CAACiC,GAAG,CAAC,6BAA6B;QAE9D,IAAID,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;UACzBhB,kBAAkB,CAACY,KAAI,GAAIC,QAAQ,CAACE,IAAI,CAACA,IAAG;QAC9C;MACF,EAAE,OAAOI,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChC5C,SAAS,CAAC4C,KAAK,CAAC,UAAU;MAC5B,UAAU;QACR7B,kBAAkB,CAACsB,KAAI,GAAI,KAAI;MACjC;IACF;;IAEA;IACA,MAAMU,qBAAoB,GAAKC,aAAa,IAAK;MAC/C,MAAMC,QAAO,GAAI;QACf,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE,YAAY;QAClB,MAAM,EAAE,cAAc;QACtB,cAAc,EAAE,oBAAoB;QACpC,GAAG,EAAE;MACP;MACA,OAAOA,QAAQ,CAACD,aAAa,KAAK,iBAAgB;IACpD;;IAEA;IACA,MAAME,cAAa,GAAKC,KAAK,IAAK;MAChC,IAAI,CAACA,KAAI,IAAKA,KAAI,KAAM,CAAC,EAAE,OAAO,KAAI;MACtC,MAAMC,CAAA,GAAI,IAAG;MACb,MAAMC,KAAI,GAAI,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;MACpC,MAAMC,CAAA,GAAIC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACN,KAAK,IAAII,IAAI,CAACE,GAAG,CAACL,CAAC,CAAC;MAClD,OAAOM,UAAU,CAAC,CAACP,KAAI,GAAII,IAAI,CAACI,GAAG,CAACP,CAAC,EAAEE,CAAC,CAAC,EAAEM,OAAO,CAAC,CAAC,CAAC,IAAI,GAAE,GAAIP,KAAK,CAACC,CAAC;IACxE;;IAEA;IACA,MAAMO,gBAAe,GAAKC,KAAK,IAAK;MAClCjD,UAAU,CAACwB,KAAI,GAAIyB,KAAI;IACzB;;IAEA;IACA,MAAMC,YAAW,GAAIA,CAAA,KAAM;MACzBnC,YAAY,CAACoC,UAAU,CAAC,aAAa;MACrCpC,YAAY,CAACoC,UAAU,CAAC,YAAY;MACpChE,SAAS,CAACyC,OAAO,CAAC,OAAO;MACzB7B,MAAM,CAACmB,IAAI,CAAC,cAAc;IAC5B;IAEAjC,SAAS,CAAC,MAAM;MACd,IAAI4B,SAAS,CAAC,CAAC,EAAE;QACfU,cAAc,CAAC;QACfU,sBAAsB,CAAC;MACzB;IACF,CAAC;IAED,OAAO;MACLjC,UAAU;MACVG,QAAQ;MACRE,UAAU;MACVJ,YAAY;MACZW,kBAAkB;MAClBV,kBAAkB;MAClB8C,gBAAgB;MAChBE,YAAY;MACZ3B,cAAc;MACdU,sBAAsB;MACtBC,qBAAqB;MACrBG;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
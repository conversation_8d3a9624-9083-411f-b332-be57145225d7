# Generated by Django 3.2.20 on 2025-07-29 00:43

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('files', '0005_alter_genomefile_category'),
    ]

    operations = [
        migrations.CreateModel(
            name='TableColumnConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('table_name', models.CharField(choices=[('data', '数据一览表'), ('data-overview', '数据概览表')], max_length=50, verbose_name='表格名称')),
                ('column_key', models.CharField(max_length=100, verbose_name='列标识')),
                ('column_label', models.CharField(max_length=200, verbose_name='列名')),
                ('column_width', models.IntegerField(blank=True, null=True, verbose_name='列宽度')),
                ('is_visible', models.BooleanField(default=True, verbose_name='是否显示')),
                ('is_required', models.BooleanField(default=False, verbose_name='是否必需')),
                ('sort_order', models.IntegerField(default=0, verbose_name='排序顺序')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '表格列配置',
                'verbose_name_plural': '表格列配置',
                'db_table': 'table_column_config',
                'ordering': ['table_name', 'sort_order'],
                'unique_together': {('table_name', 'column_key')},
            },
        ),
    ]

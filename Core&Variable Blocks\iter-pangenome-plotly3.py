
import matplotlib.pyplot as plt
import numpy as np
import matplotlib.patches as mpatches
import matplotlib.pyplot as plt


"""
先将参考序列按照每行固定的碱基打印在每行上，记录每个碱基位置。
依次根据每次迭代，取交集的位置，显示碱基，其它位置上变成横线。
每次迭代显示出标记是哪个范围。每次过程都出一张图。

动画实现：考虑出多张图合并成动画；或者是用一些库直接化成动画的形式


前几次迭代只是 用色块表示,等最后一次结束再用碱基表示



特别长的可以分开点


调节色块透明度，每次迭代色块加深
"""


import re
import os
import glob
from Bio import SeqIO
import plotly.graph_objects as go
from plotly.graph_objs import Scatter
import plotly.tools as tls
from math import *
from pylab import *

# fig1 = go.Figure()
def Rectangle1(ax, loc, heigt, width, color, alpha):
    p = mpatches.Rectangle(loc, width, heigt, edgecolor="none", facecolor=color, alpha=alpha)
    ax.add_patch(p)

def Rectangle2(fig, loc, heigt, width, rectcolor, alpha, seq):
    # p = mpatches.Rectangle(loc, width, heigt, edgecolor="none", facecolor=color, alpha=alpha)
    # ax.add_patch(p)
    fig.add_shape(
    type="rect",
    x0=loc[0], y0=loc[1], x1=loc[0]+width, y1=loc[1]+heigt,
    line=dict(color="LightSkyBlue", width=1),
    fillcolor="LightSkyBlue")

    # print(fig['data'])
    # fig['data'].append(Scatter(x=[loc[0]+width/2], y=[loc[1]+heigt/2], hoverinfo="text", hovertext=["xxxxxxxx"]))
    new_scatter = go.Scatter(
        x=[loc[0]+width/2],
        y=[loc[1]+heigt/2],
        hoverinfo="text",
        hovertext=[seq],
        line=dict(color='LightSkyBlue', dash='solid')
    )
    fig.add_trace(new_scatter)

def Rectangle(fig, loc, heigt, width, rectcolor, alpha, seq):
    # p = mpatches.Rectangle(loc, width, heigt, edgecolor="none", facecolor=color, alpha=alpha)
    # ax.add_patch(p)
    fig.add_shape(
    type="rect",
    x0=loc[0], y0=loc[1], x1=loc[0]+width, y1=loc[1]+heigt,
    line=dict(color=rectcolor, width=1),
    fillcolor=rectcolor)

    new_scatter = go.Scatter(
        x=[loc[0]+width/2],
        y=[loc[1]+heigt/2],
        hoverinfo="text",
        hovertext=[seq],
        line=dict(color=rectcolor, dash='solid')
    )
    fig.add_trace(new_scatter)


def get_seq(seq, pos):
    seq_len = len(seq)
    Recordseq = ''
    target_seq = {}
    new_pos = []
    for row in pos:
        s, e = int(row[1]), int(row[2])
        k = '_'.join(row)
        new_pos.append([row[0], s, e, seq[s:e+1]])

    return new_pos

def get_pos(coords):
    pos = []
    for row in open(coords):
        rows = row.strip().split()
        pos.append(rows[:3])
    return pos


def iterpangenome(line_num, coordspos, color, savefile, seqnum):
    # line_num = 12
    total_len = len(sequence)
    one_length = 0
    last_length = 0
    if total_len % line_num == 0:
        one_length = int(total_len/line_num)
    else:
        one_length = int(total_len/line_num)
        last_length = total_len - (line_num-1)*one_length
        # line_num += 1

    # print(total_len, line_num, one_length, last_length, one_length*(line_num-1)+last_length)

    gl1, gl2 = 0.92, 0.92
    start = 0.04
    end = 0.96

    hight = gl1/line_num
    fig=plt.figure(figsize=(10, 10))
    root = plt.axes([0, 0, 1, 1])


    hight_pos = []
    s, e = 0, 0
    for k in range(1, line_num+1):
        plt.plot([start, end], [start+float(hight/2)+hight*(k-1), start+float(hight/2)+hight*(k-1)], color='grey', lw=2, alpha=.5)

        if k == line_num:
            s = one_length*(k-1)
            e = s+last_length
            pixhight = start+float(hight/2)+hight*(k-1)
            hight_pos.append([s, e, pixhight, gl2/last_length])
        else:
            s = one_length*(k-1)
            e = s+one_length
            pixhight = start+float(hight/2)+hight*(k-1)
            hight_pos.append([s, e, pixhight, gl2/one_length])

    root = tls.mpl_to_plotly(fig)
    # root.show()
    bar_height = 0.01
    for row in coordspos:
        s, e = int(row[1]), int(row[2])
        middseq = str(row[3])
        row[3] = "<br>".join([middseq[i:i+seqnum] for i in range(0, len(middseq), seqnum)])
        for k in hight_pos:
            sp, ep, pixh, pixstep = k
            if sp < s and e < ep:
                Rectangle(root, [(s-sp+1)*pixstep+start, pixh-bar_height/2], bar_height, abs(e-s+1)*pixstep, color, 1, row[3])
            elif sp < s < ep and e > ep:
                Rectangle(root, [(s-sp+1)*pixstep+start, pixh-bar_height/2], bar_height, abs(ep-s+1)*pixstep, color, 1, row[3])
            elif sp > s and sp < e < ep:
                Rectangle(root, [(sp-sp)*pixstep+start, pixh-bar_height/2], bar_height, abs(e-sp+1)*pixstep, color, 1, row[3])
            elif sp > s and e > ep:
                Rectangle(root, [(sp-sp)*pixstep+start, pixh-bar_height/2], bar_height, abs(ep-sp+1)*pixstep, color, 1, row[3])

    root['layout']['hovermode'] = 'closest'
    fig = go.Figure(root)
    root.write_html(savefile)


def iterpangenome2(line_num, coordspos, color, savefile, seqnum):
    # line_num = 12
    total_len = len(sequence)
    one_length = 0
    last_length = 0
    if total_len % line_num == 0:
        one_length = int(total_len/line_num)
    else:
        one_length = int(total_len/line_num)
        last_length = total_len - (line_num-1)*one_length
        # line_num += 1

    # print(total_len, line_num, one_length, last_length, one_length*(line_num-1)+last_length)

    gl1, gl2 = 0.92, 0.92
    start = 0.04
    end = 0.96

    hight = gl1/line_num
    fig=plt.figure(figsize=(10, 10))
    root = plt.axes([0, 0, 1, 1])


    hight_pos = []
    s, e = 0, 0
    for k in range(1, line_num+1):
        plt.plot([start, end], [start+float(hight/2)+hight*(k-1), start+float(hight/2)+hight*(k-1)], color='grey', lw=2, alpha=.5)

        if k == line_num:
            s = one_length*(k-1)
            e = s+last_length
            pixhight = end-(float(hight/2)+hight*(k-1))
            hight_pos.append([s, e, pixhight, gl2/last_length])
        else:
            s = one_length*(k-1)
            e = s+one_length
            pixhight = end-(float(hight/2)+hight*(k-1))
            hight_pos.append([s, e, pixhight, gl2/one_length])

    one = 0
    for k in hight_pos[:-1]:
        sp, ep, pixh, pixstep = k
        if one%2 == 0:
            sm_r = hight/2
            sm_t = np.arange(30-2*pi/30, 30+pi-pi/30, pi/30.)
            sm_x = end + sm_r*cos(sm_t)
            sm_y = pixh-hight/2 + sm_r*sin(sm_t)
            plt.plot(sm_x, sm_y, "grey", lw=2, alpha=0.5)
        else:
            sm_r = hight/2
            sm_t = np.arange(30-pi-2*pi/30, 30-pi/30, pi/30.)
            sm_x = start + sm_r*cos(sm_t)
            sm_y = pixh-hight/2 + sm_r*sin(sm_t)
            plt.plot(sm_x, sm_y, "grey", lw=2, alpha=0.5)
        one += 1

    index_x, index = {}, 0
    for k in hight_pos:
        sp, ep, pixh, pixstep = k
        index_x[ep] = index
        index += 1

    root = tls.mpl_to_plotly(fig)
    # root.show()
    bar_height = 0.01
    for row in coordspos:
        s, e = int(row[1]), int(row[2])
        middseq = str(row[3])
        row[3] = "<br>".join([middseq[i:i+seqnum] for i in range(0, len(middseq), seqnum)])
        for k in hight_pos:
            sp, ep, pixh, pixstep = k
            
            if index_x[ep]%2 ==0:
                if sp < s and e < ep:
                    Rectangle(root, [(s-sp+1)*pixstep+start, pixh-bar_height/2], bar_height, abs(e-s+1)*pixstep, color, 1, row[3])
                elif sp < s < ep and e > ep:
                    Rectangle(root, [(s-sp+1)*pixstep+start, pixh-bar_height/2], bar_height, abs(ep-s+1)*pixstep, color, 1, row[3])
                elif sp > s and sp < e < ep:
                    Rectangle(root, [(sp-sp)*pixstep+start, pixh-bar_height/2], bar_height, abs(e-sp+1)*pixstep, color, 1, row[3])
                elif sp > s and e > ep:
                    Rectangle(root, [(sp-sp)*pixstep+start, pixh-bar_height/2], bar_height, abs(ep-sp+1)*pixstep, color, 1, row[3])

            else:
                if sp < s and e < ep:
                    Rectangle(root, [(ep-sp-(s-sp+1))*pixstep+start-abs(e-s+1)*pixstep, pixh-bar_height/2], bar_height, abs(e-s+1)*pixstep, color, 1, row[3])
                elif sp < s < ep and e > ep:
                    Rectangle(root, [(ep-sp-(s-sp+1))*pixstep+start-abs(ep-s+1)*pixstep, pixh-bar_height/2], bar_height, abs(ep-s+1)*pixstep, color, 1, row[3])
                elif sp > s and sp < e < ep:
                    Rectangle(root, [(ep-sp-(sp-sp))*pixstep+start-abs(e-sp+1)*pixstep, pixh-bar_height/2], bar_height, abs(e-sp+1)*pixstep, color, 1, row[3])
                elif sp > s and e > ep:
                    Rectangle(root, [(ep-sp-(sp-sp))*pixstep+start-abs(ep-sp+1)*pixstep, pixh-bar_height/2], bar_height, abs(ep-sp+1)*pixstep, color, 1, row[3])


    root['layout']['hovermode'] = 'closest'
    fig = go.Figure(root)
    root.write_html(savefile)

seq = {}
for seq_record in SeqIO.parse("Chr01.fa", "fasta"):
    seq[seq_record.id] = seq_record.seq

sequence = ''
for k in seq:
    # print(len(seq[k]))
    sequence = seq[k]


# files = glob.glob('Chr01/*_RP_qryRecord.coords')
files = glob.glob('E:/Desktop/核心区块动态展示/fsdownload/Chr01.50.R150_RP_qryRecord.coords')



for file in files:
    name = os.path.basename(file).split('_')[0]
    coordspos = get_pos(file)
    newcoordspos = get_seq(sequence, coordspos)
    # for k in target_seq:
    #     print(len(target_seq[k]))

    line_num = 25
    seqnum = 90
    color = '#66c2a5'  # #66c2a5  #55a868
    savefile = f'{name}_pangenome.html'

    iterpangenome2(line_num, newcoordspos, color, savefile, seqnum)

# fig.show()
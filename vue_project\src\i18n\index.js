import { createI18n } from 'vue-i18n'

// 中文语言包
const zh = {
  common: {
    search: '搜索',
    refresh: '刷新',
    clear: '清除',
    download: '下载',
    logout: '退出',
    currentUser: '当前用户',
    loading: '加载中...',
    noData: '暂无数据'
  },
  messages: {
    openingPage: '正在打开 {species} 的 {module} 页面',
    sequenceDataNotAvailable: '{species} 的序列数据不可用',
    getSequenceDataFailed: '获取序列数据失败',
    jumpingToPage: '正在跳转到 {species} 的 {module} 页面',
    getSupplementaryDataFailed: '获取补充数据失败，使用测试数据',
    getSubPopulationsFailed: '获取亚群列表失败',
    dataRefreshed: '数据已刷新',
    dataRefreshFailed: '数据刷新失败',
    dataExported: '数据已导出',
    getOrganismsFailed: '获取生物体列表失败',
    getFileListFailed: '获取文件列表失败',
    jumpingToGeographicMap: '正在跳转到 {accession} 的地理分布图',
    getDataFailed: '获取数据失败',
    mapLoadFailed: '地图加载失败，请检查网络连接',
    jumpingToDetailsPage: '正在跳转到 {accession} 详情页面'
  },
  nav: {
    home: '首页',
    dataChart: '数据一览图',
    dataOverview: '数据一览表',
    transcriptomeOverview: '转录组表',
    accessionMap: '地理分布图',
    accession: '品种信息',
    genome: '基因组',
    annotation: '注释',
    coreVariableBlocks: '核心可变区块',
    codon: '密码子',
    tools: '工具',
    codonw: 'CodonW',
    download: '下载',
    allData: '全部数据',
    transcriptome: '转录组'
  },
  page: {
    home: {
      title: 'All data',
      searchPlaceholder: '搜索生物体 Accession',
      refreshData: '刷新数据'
    },
    dataOverview: {
      title: '数据一览表',
      searchPlaceholder: '搜索生物体 Accession',
      refreshData: '刷新数据',
      accession: 'Accession',
      subPopulation: 'SubPopulation',
      seqData: 'SeqData',
      genome: 'Genome',
      annotation: 'Annotation',
      transcriptome: 'Transcriptome',
      codon: 'Codon',
      centromere: 'Centromere',
      tes: 'TEs',
      snoRNA: 'SnoRNA',
      miRNA: 'miRNA',
      rRNA: 'rRNA',
      location: 'Location',
      selectAll: '全选',
      clear: '清除',
      totalItems: '总计',
      items: '条',
      currentPage: '第',
      page: '页',
      itemsPerPage: '每页',
      download: '下载',
      view: '查看',
      notAvailable: '不可用'
    },
    dataChart: {
      title: '数据一览图',
      searchPlaceholder: '搜索生物体 Accession',
      refreshData: '刷新数据',
      exportChart: '导出图表',
      cardView: '卡片',
      chartView: '图表',
      subPopulationDistribution: '亚群分布',
      speciesInSubPopulation: '亚群物种',
      moduleDistribution: '模块分布',
      dataTypes: '数据类型',
      clickToViewDetails: '点击查看详情',
      dataTypesLabel: '数据类型：',
      targetSpecies: '目标物种：',
      types: '种',
      totalSubPopulations: '总亚群数：',
      currentSelection: '当前选择：',
      speciesCount: '物种数：',
      none: '无',
      unknown: '未知',
      species: '个物种'
    },
    transcriptomeOverview: {
      title: '转录组表',
      searchPlaceholder: '搜索生物体 Accession',
      refreshData: '刷新数据',
      totalItems: '总计',
      items: '条',
      currentPage: '第',
      page: '页',
      itemsPerPage: '每页'
    },
    transcriptome: {
      title: '转录组',
      searchPlaceholder: '搜索生物体 Accession'
    },
    accessionMap: {
      title: '地理分布图',
      searchPlaceholder: '搜索具有地理位置的 Accession',
      refreshData: '刷新数据',
      subPopulationFilter: '亚群筛选：',
      markerSize: '标记大小',
      loadingGeographicData: 'Loading geographic data...',
      dataOverview: '数据概览',
      totalGermplasm: '种质资源总数',
      totalGermplasmDesc: '数据库中的水稻遗传资源',
      locatedGermplasm: '已定位种质',
      locatedGermplasmDesc: '具有地理坐标信息',
      geographicRegions: '地理区域',
      geographicRegionsDesc: '不同坐标聚类区域',
      activeSubPopulations: '活跃亚群',
      activeSubPopulationsDesc: '当前显示的亚群类型'
    },
    genomeCard: {
      title: 'Genome',
      searchPlaceholder: '搜索生物体 Accession',
      refreshData: '刷新数据',
      chromosome: 'Chromosome:',
      selectChromosome: 'Select chromosome',
      geneStructure: '基因结构',
      downloadData: '下载数据',
      zoomIn: '放大',
      zoomOut: '缩小',
      resetView: '重置视图'
    },
    annotation: {
      title: 'Annotation',
      searchPlaceholder: '搜索生物体 Accession',
      refreshData: '刷新数据',
      tableView: '表格',
      chartView: '图形',
      selectOrganismPrompt: '请选择一个生物体以查看注释信息',
      loadingVisualization: '正在加载可视化数据...',
      totalFeatures: '总特征数:',
      chromosomes: '染色体数:',
      featureTypes: '特征类型数:'
    },
    coreVariableBlocks: {
      title: 'Core&Variable Blocks',
      searchPlaceholder: '搜索生物体 Accession',
      refreshData: '刷新数据',
      selectOrganismPrompt: '请选择一个生物体以查看核心和可变区块信息'
    },
    codonCard: {
      title: 'Codon',
      searchPlaceholder: '搜索生物体 Accession',
      refreshData: '刷新数据',
      uploadCodonWResults: '上传CodonW结果',
      codonUsageData: '密码子使用偏好性数据',
      codonUsageStatistics: '密码子使用偏好性统计',
      statisticsSubtitle: '全面分析密码子使用模式和序列特征',
      nucleotideComposition: '同义密码子第三位核苷酸组成',
      gcContent: 'GC含量',
      codonUsageIndicators: '密码子使用偏好性指标',
      caiDescription: '密码子适应性指数，衡量基因密码子使用与参考基因集的相似程度。',
      caiRange: '范围：0-1，值越高表示密码子使用越优化',
      caiLabel: '密码子适应性指数',
      cbiDescription: '密码子偏好性指数，反映密码子使用的偏好程度。',
      cbiRange: '范围：-1到1，正值表示偏向使用最优密码子',
      cbiLabel: '密码子偏好性指数',
      fopDescription: '最优密码子频率，表示最优密码子在所有同义密码子中的使用频率。',
      fopRange: '范围：0-1，值越高表示最优密码子使用越频繁',
      fopLabel: '最优密码子频率',
      ncDescription: '有效密码子数，衡量密码子使用的均匀程度。',
      ncRange: '范围：20-61，值越小表示密码子使用越不均匀（偏好性越强）',
      ncLabel: '有效密码子数',
      otherStatistics: '其他统计指标',
      gc3sDescription: '同义密码子第三位的GC含量，反映同义密码子使用的GC偏好性。',
      gc3sNote: '与密码子使用偏好性和基因表达水平相关',
      gc3sLabel: 'GC3s含量',
      sequenceLengthDescription: '氨基酸序列的总长度，表示编码蛋白质的氨基酸数量。',
      sequenceLengthNote: '反映基因组中蛋白质编码序列的规模',
      sequenceLengthLabel: '序列长度',
      gravyDescription: '疏水性指数，衡量蛋白质的整体疏水性程度。',
      gravyNote: '正值表示疏水性，负值表示亲水性',
      gravyLabel: '疏水性指数',
      aromoDescription: '芳香性指数，表示芳香族氨基酸在蛋白质中的相对频率。',
      aromoNote: '范围：0-1，值越高表示芳香族氨基酸含量越高',
      aromoLabel: '芳香性指数',
      aminoAcidUsage: '氨基酸密码子使用情况',
      aminoAcid: '氨基酸',
      totalUsage: '总使用次数',
      codonCount: '密码子数量',
      usageFrequency: '使用频率占比',
      codonDetails: '的密码子详情',
      codon: '密码子',
      usageCount: '使用次数',
      rscuDescription: '相对同义密码子使用度，衡量某个密码子相对于编码同一氨基酸的其他同义密码子的使用频率。',
      rscuNote: 'RSCU=1表示该密码子使用频率等于平均水平，>1表示偏好使用，<1表示较少使用',
      relativeFrequency: '相对频率',
      noCodonData: '暂无密码子数据',
      noDataMessage: '很抱歉，生物体 {organism} 暂时没有密码子使用偏好性数据。',
      possibleReasons: '可能的原因：',
      reasonAnalysisIncomplete: '该生物体的密码子分析尚未完成',
      reasonDataProcessing: '数据文件正在处理中',
      reasonNotInScope: '该生物体不在密码子分析范围内',
      tryAnotherOrganism: '尝试其他生物体',
      recheck: '重新检查',
      dataLoadFailed: '数据加载失败',
      reload: '重新加载',
      codonUsageAnalysis: '密码子使用偏好性分析',
      analysisSubtitle: '探索生物体的密码子使用模式和偏好性特征',
      statisticalAnalysis: '统计指标分析',
      statisticalDescription: '提供CAI、CBI、Fop、Nc等14项专业指标，全面评估密码子使用偏好性',
      aminoAcidComposition: '氨基酸组成',
      aminoAcidDescription: '详细展示各氨基酸的密码子使用情况，支持展开查看具体密码子频率',
      gcAnalysis: 'GC含量分析',
      gcDescription: '分析总体GC含量和第三位密码子GC含量，了解序列组成特征',
      proteinProperties: '蛋白质特性',
      proteinDescription: '计算疏水性指数(Gravy)和芳香性指数(Aromo)，预测蛋白质性质',
      startAnalysis: '开始分析',
      step1: '在上方搜索框中输入或选择生物体Accession',
      step2: '系统将自动加载该生物体的密码子数据',
      step3: '查看详细的统计指标和氨基酸使用情况',
      exampleData: '示例数据',
      exampleDescription: '您可以尝试以下生物体来体验功能：',
      riceVariety: '水稻品种',
      uploadedResults: '上传的结果'
    },
    codonw: {
      title: 'CodonW',
      searchPlaceholder: '搜索生物体 Accession',
      refreshData: '刷新数据',
      aboutCodonW: '关于 CodonW',
      description: 'CodonW 是一个用于分析密码子使用偏好性的工具。支持大文件分析（最大10GB），可以计算各种密码子使用指标，包括：',
      codonUsageFrequency: '密码子使用频率 (Codon Usage Frequency)',
      effectiveNumberOfCodons: '有效密码子数 (Effective Number of Codons, ENC)',
      codonAdaptationIndex: '密码子适应指数 (Codon Adaptation Index, CAI)',
      codonBiasIndex: '密码子偏好指数 (Codon Bias Index, CBI)',
      gcContentAnalysis: 'GC含量分析',
      selectAnalysisType: '选择分析方式',
      genomicAnalysis: '基因组分析',
      genomicAnalysisDesc: '上传基因组文件(.fasta)和注释文件(.gff)，自动提取CDS序列进行分析',
      cdsAnalysis: 'CDS序列分析',
      cdsAnalysisDesc: '直接上传CDS序列文件(.fasta)进行分析',
      genomicFileUpload: '基因组分析 - 文件上传',
      genomeFile: '基因组文件 (.fasta)',
      annotationFile: '注释文件 (.gff)',
      required: '*',
      dragGenomeFile: '将基因组文件拖拽到此处，或',
      clickUpload: '点击上传',
      genomeFileFormats: '支持 .fasta, .fa, .fas, .fna 格式，文件大小不超过 10GB',
      dragAnnotationFile: '将注释文件拖拽到此处，或',
      annotationFileFormats: '支持 .gff, .gff3 格式，文件大小不超过 10GB',
      startGenomicAnalysis: '开始基因组分析',
      clearFiles: '清空文件',
      cdsFileUpload: 'CDS序列分析 - 文件上传',
      dragCdsFile: '将CDS文件拖拽到此处，或',
      cdsFileFormats: '支持 .fasta, .fa, .fas, .fna, .txt 格式，文件大小不超过 10GB',
      startCdsAnalysis: '开始CDS分析',
      analysisProgress: '分析进度',
      analysisHistory: '分析历史',
      historyTooltip: '任务记录和结果文件会在创建7天后自动删除，请及时下载重要结果！',
      refresh: '刷新',
      taskId: '任务ID',
      fileName: '文件名',
      status: '状态',
      submitTime: '提交时间',
      completionTime: '完成时间',
      operations: '操作',
      downloadResults: '下载结果',
      viewDetails: '查看详情',
      delete: '删除',
      statusPending: '等待中',
      statusRunning: '分析中',
      statusCompleted: '已完成',
      statusFailed: '失败',
      newFileReplaced: '新文件已替换之前的{type}文件',
      genomeFileType: '基因组',
      annotationFileType: '注释',
      cdsFileType: 'CDS',
      uploadGenomeFileError: '请上传FASTA格式的基因组文件！',
      uploadAnnotationFileError: '请上传GFF格式的注释文件！',
      uploadCdsFileError: '请上传FASTA格式的CDS文件！',
      fileSizeError: '文件大小不能超过10GB！',
      uploadBothFilesWarning: '请上传基因组文件和注释文件',
      selectCdsFileWarning: '请先选择CDS文件',
      uploadFailedError: '文件上传失败',
      analysisStarted: '分析中...',
      analysisCompleted: '分析完成！',
      analysisFailed: '分析失败: {message}',
      genomicAnalysisStarted: '基因组分析已开始，正在提取CDS序列...',
      cdsAnalysisStarted: 'CDS分析已开始，正在进行密码子使用分析...',
      uploadSuccessGenomicAnalysis: '文件上传成功，开始基因组分析',
      uploadSuccessCdsAnalysis: '文件上传成功，开始CDS分析',
      serverResponseError: '服务器响应格式错误',
      getHistoryFailed: '获取分析历史失败',
      downloadSuccess: '结果下载成功',
      downloadFailed: '下载失败',
      confirmDelete: '确定要删除这个分析任务吗？此操作不可恢复。',
      confirmDeleteTitle: '确认删除',
      confirm: '确定',
      cancel: '取消',
      deleteSuccess: '删除成功',
      deleteNoPermission: '无权限删除此任务',
      deleteFailed: '删除失败'
    },
    accessionCard: {
      title: 'Accession',
      searchPlaceholder: '搜索生物体 Accession',
      developing: 'AccessionCard 功能开发中',
      comingSoon: '生物体详细信息展示功能即将上线',
      currentOrganism: '当前选择的生物体'
    }
  },
  footer: {
    copyright: '© 2025 基因组数据仓库',
    version: '数据仓库系统 v1.0.0'
  }
}

// 英文语言包
const en = {
  common: {
    search: 'Search',
    refresh: 'Refresh',
    clear: 'Clear',
    download: 'Download',
    logout: 'Logout',
    currentUser: 'Current User',
    loading: 'Loading...',
    noData: 'No Data'
  },
  messages: {
    openingPage: 'Opening {species} {module} page',
    sequenceDataNotAvailable: 'Sequence data for {species} is not available',
    getSequenceDataFailed: 'Failed to get sequence data',
    jumpingToPage: 'Jumping to {species} {module} page',
    getSupplementaryDataFailed: 'Failed to get supplementary data, using test data',
    getSubPopulationsFailed: 'Failed to get subpopulations',
    dataRefreshed: 'Data refreshed',
    dataRefreshFailed: 'Data refresh failed',
    dataExported: 'Data exported',
    getOrganismsFailed: 'Failed to get organisms',
    getFileListFailed: 'Failed to get file list',
    jumpingToGeographicMap: 'Jumping to {accession} geographic distribution map',
    getDataFailed: 'Failed to get data',
    mapLoadFailed: 'Map loading failed, please check network connection',
    jumpingToDetailsPage: 'Jumping to {accession} details page'
  },
  nav: {
    home: 'Home',
    dataChart: 'Data Chart',
    dataOverview: 'Data Overview',
    transcriptomeOverview: 'Transcriptome Overview',
    accessionMap: 'Geographic Distribution',
    accession: 'Accession',
    genome: 'Genome',
    annotation: 'Annotation',
    coreVariableBlocks: 'Core&Variable Blocks',
    codon: 'Codon',
    tools: 'Tools',
    codonw: 'CodonW',
    download: 'Download',
    allData: 'All data',
    transcriptome: 'Transcriptome'
  },
  page: {
    home: {
      title: 'All data',
      searchPlaceholder: 'Search Organism Accession',
      refreshData: 'Refresh Data'
    },
    dataOverview: {
      title: 'Data Overview',
      searchPlaceholder: 'Search Organism Accession',
      refreshData: 'Refresh Data',
      accession: 'Accession',
      subPopulation: 'SubPopulation',
      seqData: 'SeqData',
      genome: 'Genome',
      annotation: 'Annotation',
      transcriptome: 'Transcriptome',
      codon: 'Codon',
      centromere: 'Centromere',
      tes: 'TEs',
      snoRNA: 'SnoRNA',
      miRNA: 'miRNA',
      rRNA: 'rRNA',
      location: 'Location',
      selectAll: 'Select All',
      clear: 'Clear',
      totalItems: 'Total',
      items: 'Items',
      currentPage: 'Page',
      page: '',
      itemsPerPage: 'Items per page',
      download: 'Download',
      view: 'View',
      notAvailable: 'Not Available'
    },
    dataChart: {
      title: 'Data Chart',
      searchPlaceholder: 'Search Organism Accession',
      refreshData: 'Refresh Data',
      exportChart: 'Export Chart',
      cardView: 'Card',
      chartView: 'Chart',
      subPopulationDistribution: 'SubPopulation Distribution',
      speciesInSubPopulation: 'SubPopulation Species',
      moduleDistribution: 'Module Distribution',
      dataTypes: 'Data Types',
      clickToViewDetails: 'Click to view details',
      dataTypesLabel: 'Data Types:',
      targetSpecies: 'Target Species:',
      types: 'Types',
      totalSubPopulations: 'Total SubPopulations:',
      currentSelection: 'Current Selection:',
      speciesCount: 'Species Count:',
      none: 'None',
      unknown: 'Unknown',
      species: 'Species'
    },
    transcriptomeOverview: {
      title: 'Transcriptome Overview',
      searchPlaceholder: 'Search Organism Accession',
      refreshData: 'Refresh Data',
      totalItems: 'Total',
      items: 'Items',
      currentPage: 'Page',
      page: '',
      itemsPerPage: 'Items per page'
    },
    transcriptome: {
      title: 'Transcriptome',
      searchPlaceholder: 'Search Organism Accession'
    },
    accessionMap: {
      title: 'Geographic Distribution Map',
      searchPlaceholder: 'Search Accession with Geographic Location',
      refreshData: 'Refresh Data',
      subPopulationFilter: 'SubPopulation Filter:',
      markerSize: 'Marker Size',
      loadingGeographicData: 'Loading geographic data...',
      dataOverview: 'Data Overview',
      totalGermplasm: 'Total Germplasm',
      totalGermplasmDesc: 'Rice genetic resources in database',
      locatedGermplasm: 'Located Germplasm',
      locatedGermplasmDesc: 'With geographic coordinates',
      geographicRegions: 'Geographic Regions',
      geographicRegionsDesc: 'Different coordinate clusters',
      activeSubPopulations: 'Active SubPopulations',
      activeSubPopulationsDesc: 'Currently displayed subpopulation types'
    },
    genomeCard: {
      title: 'Genome',
      searchPlaceholder: 'Search Organism Accession',
      refreshData: 'Refresh Data',
      chromosome: 'Chromosome:',
      selectChromosome: 'Select chromosome',
      geneStructure: 'Gene Structure',
      downloadData: 'Download Data',
      zoomIn: 'Zoom In',
      zoomOut: 'Zoom Out',
      resetView: 'Reset View'
    },
    annotation: {
      title: 'Annotation',
      searchPlaceholder: 'Search Organism Accession',
      refreshData: 'Refresh Data',
      tableView: 'Table',
      chartView: 'Chart',
      selectOrganismPrompt: 'Please select an organism to view annotation information',
      loadingVisualization: 'Loading visualization data...',
      totalFeatures: 'Total Features:',
      chromosomes: 'Chromosomes:',
      featureTypes: 'Feature Types:'
    },
    coreVariableBlocks: {
      title: 'Core&Variable Blocks',
      searchPlaceholder: 'Search Organism Accession',
      refreshData: 'Refresh Data',
      selectOrganismPrompt: 'Please select an organism to view core and variable blocks information'
    },
    codonCard: {
      title: 'Codon',
      searchPlaceholder: 'Search Organism Accession',
      refreshData: 'Refresh Data',
      uploadCodonWResults: 'Upload CodonW Results',
      codonUsageData: 'Codon Usage Bias Data',
      codonUsageStatistics: 'Codon Usage Bias Statistics',
      statisticsSubtitle: 'Comprehensive analysis of codon usage patterns and sequence features',
      nucleotideComposition: 'Nucleotide Composition at 3rd Codon Position',
      gcContent: 'GC Content',
      codonUsageIndicators: 'Codon Usage Bias Indicators',
      caiDescription: 'Codon Adaptation Index, measures the similarity of gene codon usage to reference gene set.',
      caiRange: 'Range: 0-1, higher values indicate more optimized codon usage',
      caiLabel: 'Codon Adaptation Index',
      cbiDescription: 'Codon Bias Index, reflects the degree of codon usage preference.',
      cbiRange: 'Range: -1 to 1, positive values indicate preference for optimal codons',
      cbiLabel: 'Codon Bias Index',
      fopDescription: 'Frequency of Optimal codons, represents the usage frequency of optimal codons among all synonymous codons.',
      fopRange: 'Range: 0-1, higher values indicate more frequent use of optimal codons',
      fopLabel: 'Frequency of Optimal Codons',
      ncDescription: 'Number of Codons, measures the uniformity of codon usage.',
      ncRange: 'Range: 20-61, smaller values indicate more uneven codon usage (stronger bias)',
      ncLabel: 'Number of Codons',
      otherStatistics: 'Other Statistical Indicators',
      gc3sDescription: 'GC content at 3rd position of synonymous codons, reflects GC preference in synonymous codon usage.',
      gc3sNote: 'Related to codon usage bias and gene expression levels',
      gc3sLabel: 'GC3s Content',
      sequenceLengthDescription: 'Total length of amino acid sequence, represents the number of amino acids in encoded proteins.',
      sequenceLengthNote: 'Reflects the scale of protein-coding sequences in the genome',
      sequenceLengthLabel: 'Sequence Length',
      gravyDescription: 'Grand Average of Hydropathy, measures the overall hydrophobicity of proteins.',
      gravyNote: 'Positive values indicate hydrophobicity, negative values indicate hydrophilicity',
      gravyLabel: 'Hydropathy Index',
      aromoDescription: 'Aromaticity index, represents the relative frequency of aromatic amino acids in proteins.',
      aromoNote: 'Range: 0-1, higher values indicate higher aromatic amino acid content',
      aromoLabel: 'Aromaticity Index',
      aminoAcidUsage: 'Amino Acid Codon Usage',
      aminoAcid: 'Amino Acid',
      totalUsage: 'Total Usage',
      codonCount: 'Codon Count',
      usageFrequency: 'Usage Frequency',
      codonDetails: 'Codon Details',
      codon: 'Codon',
      usageCount: 'Usage Count',
      rscuDescription: 'Relative Synonymous Codon Usage, measures the usage frequency of a codon relative to other synonymous codons encoding the same amino acid.',
      rscuNote: 'RSCU=1 indicates average usage frequency, >1 indicates preferred usage, <1 indicates less frequent usage',
      relativeFrequency: 'Relative Frequency',
      noCodonData: 'No Codon Data Available',
      noDataMessage: 'Sorry, organism {organism} currently has no codon usage bias data available.',
      possibleReasons: 'Possible reasons:',
      reasonAnalysisIncomplete: 'Codon analysis for this organism is not yet complete',
      reasonDataProcessing: 'Data files are being processed',
      reasonNotInScope: 'This organism is not within the scope of codon analysis',
      tryAnotherOrganism: 'Try Another Organism',
      recheck: 'Recheck',
      dataLoadFailed: 'Data Loading Failed',
      reload: 'Reload',
      codonUsageAnalysis: 'Codon Usage Bias Analysis',
      analysisSubtitle: 'Explore codon usage patterns and bias characteristics of organisms',
      statisticalAnalysis: 'Statistical Analysis',
      statisticalDescription: 'Provides 14 professional indicators including CAI, CBI, Fop, Nc for comprehensive evaluation of codon usage bias',
      aminoAcidComposition: 'Amino Acid Composition',
      aminoAcidDescription: 'Detailed display of codon usage for each amino acid, supports expansion to view specific codon frequencies',
      gcAnalysis: 'GC Content Analysis',
      gcDescription: 'Analyze overall GC content and 3rd position codon GC content to understand sequence composition characteristics',
      proteinProperties: 'Protein Properties',
      proteinDescription: 'Calculate hydropathy index (Gravy) and aromaticity index (Aromo) to predict protein properties',
      startAnalysis: 'Start Analysis',
      step1: 'Enter or select organism Accession in the search box above',
      step2: 'System will automatically load codon data for the organism',
      step3: 'View detailed statistical indicators and amino acid usage',
      exampleData: 'Example Data',
      exampleDescription: 'You can try the following organisms to experience the features:',
      riceVariety: 'Rice Variety',
      uploadedResults: 'Uploaded Results'
    },
    codonw: {
      title: 'CodonW',
      searchPlaceholder: 'Search Organism Accession',
      refreshData: 'Refresh Data',
      aboutCodonW: 'About CodonW',
      description: 'CodonW is a tool for analyzing codon usage bias. Supports large file analysis (up to 10GB) and can calculate various codon usage indicators, including:',
      codonUsageFrequency: 'Codon Usage Frequency',
      effectiveNumberOfCodons: 'Effective Number of Codons (ENC)',
      codonAdaptationIndex: 'Codon Adaptation Index (CAI)',
      codonBiasIndex: 'Codon Bias Index (CBI)',
      gcContentAnalysis: 'GC Content Analysis',
      selectAnalysisType: 'Select Analysis Type',
      genomicAnalysis: 'Genomic Analysis',
      genomicAnalysisDesc: 'Upload genome file (.fasta) and annotation file (.gff), automatically extract CDS sequences for analysis',
      cdsAnalysis: 'CDS Sequence Analysis',
      cdsAnalysisDesc: 'Directly upload CDS sequence file (.fasta) for analysis',
      genomicFileUpload: 'Genomic Analysis - File Upload',
      genomeFile: 'Genome File (.fasta)',
      annotationFile: 'Annotation File (.gff)',
      required: '*',
      dragGenomeFile: 'Drag genome file here, or ',
      clickUpload: 'click to upload',
      genomeFileFormats: 'Supports .fasta, .fa, .fas, .fna formats, file size up to 10GB',
      dragAnnotationFile: 'Drag annotation file here, or ',
      annotationFileFormats: 'Supports .gff, .gff3 formats, file size up to 10GB',
      startGenomicAnalysis: 'Start Genomic Analysis',
      clearFiles: 'Clear Files',
      cdsFileUpload: 'CDS Sequence Analysis - File Upload',
      dragCdsFile: 'Drag CDS file here, or ',
      cdsFileFormats: 'Supports .fasta, .fa, .fas, .fna, .txt formats, file size up to 10GB',
      startCdsAnalysis: 'Start CDS Analysis',
      analysisProgress: 'Analysis Progress',
      analysisHistory: 'Analysis History',
      historyTooltip: 'Task records and result files will be automatically deleted 7 days after creation. Please download important results in time!',
      refresh: 'Refresh',
      taskId: 'Task ID',
      fileName: 'File Name',
      status: 'Status',
      submitTime: 'Submit Time',
      completionTime: 'Completion Time',
      operations: 'Operations',
      downloadResults: 'Download Results',
      viewDetails: 'View Details',
      delete: 'Delete',
      statusPending: 'Pending',
      statusRunning: 'Running',
      statusCompleted: 'Completed',
      statusFailed: 'Failed',
      newFileReplaced: 'New file has replaced the previous {type} file',
      genomeFileType: 'genome',
      annotationFileType: 'annotation',
      cdsFileType: 'CDS',
      uploadGenomeFileError: 'Please upload a FASTA format genome file!',
      uploadAnnotationFileError: 'Please upload a GFF format annotation file!',
      uploadCdsFileError: 'Please upload a FASTA format CDS file!',
      fileSizeError: 'File size cannot exceed 10GB!',
      uploadBothFilesWarning: 'Please upload both genome file and annotation file',
      selectCdsFileWarning: 'Please select a CDS file first',
      uploadFailedError: 'File upload failed',
      analysisStarted: 'Analyzing...',
      analysisCompleted: 'Analysis completed!',
      analysisFailed: 'Analysis failed: {message}',
      genomicAnalysisStarted: 'Genomic analysis started, extracting CDS sequences...',
      cdsAnalysisStarted: 'CDS analysis started, performing codon usage analysis...',
      uploadSuccessGenomicAnalysis: 'File upload successful, starting genomic analysis',
      uploadSuccessCdsAnalysis: 'File upload successful, starting CDS analysis',
      serverResponseError: 'Server response format error',
      getHistoryFailed: 'Failed to get analysis history',
      downloadSuccess: 'Results downloaded successfully',
      downloadFailed: 'Download failed',
      confirmDelete: 'Are you sure you want to delete this analysis task? This operation cannot be undone.',
      confirmDeleteTitle: 'Confirm Delete',
      confirm: 'Confirm',
      cancel: 'Cancel',
      deleteSuccess: 'Deleted successfully',
      deleteNoPermission: 'No permission to delete this task',
      deleteFailed: 'Delete failed'
    },
    accessionCard: {
      title: 'Accession',
      searchPlaceholder: 'Search Organism Accession',
      developing: 'AccessionCard Feature Under Development',
      comingSoon: 'Organism detailed information display feature coming soon',
      currentOrganism: 'Currently selected organism'
    }
  },
  footer: {
    copyright: '© 2025 Genome Data Repository',
    version: 'Data Repository System v1.0.0'
  }
}

const i18n = createI18n({
  legacy: false, // 使用 Composition API 模式
  locale: localStorage.getItem('language') || 'zh', // 默认语言
  fallbackLocale: 'zh',
  messages: {
    zh,
    en
  }
})

export default i18n

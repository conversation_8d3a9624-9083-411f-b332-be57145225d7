{"ast": null, "code": "import { createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, renderList as _renderList, Fragment as _Fragment, createBlock as _createBlock, resolveDirective as _resolveDirective, withDirectives as _withDirectives } from \"vue\";\nconst _hoisted_1 = {\n  class: \"admin-data-manager\"\n};\nconst _hoisted_2 = {\n  class: \"page-header\"\n};\nconst _hoisted_3 = {\n  class: \"header-actions\"\n};\nconst _hoisted_4 = {\n  class: \"stats-info\"\n};\nconst _hoisted_5 = {\n  key: 0\n};\nconst _hoisted_6 = {\n  key: 1,\n  class: \"selected-info\"\n};\nconst _hoisted_7 = {\n  class: \"action-buttons\"\n};\nconst _hoisted_8 = {\n  class: \"filter-section\"\n};\nconst _hoisted_9 = {\n  class: \"filter-row\"\n};\nconst _hoisted_10 = {\n  class: \"filter-item\"\n};\nconst _hoisted_11 = {\n  class: \"filter-item\"\n};\nconst _hoisted_12 = {\n  class: \"filter-item\"\n};\nconst _hoisted_13 = [\"href\"];\nconst _hoisted_14 = {\n  key: 1,\n  class: \"data-empty\"\n};\nconst _hoisted_15 = [\"onClick\", \"title\"];\nconst _hoisted_16 = {\n  key: 1,\n  class: \"file-empty\"\n};\nconst _hoisted_17 = [\"onClick\", \"title\"];\nconst _hoisted_18 = {\n  key: 1,\n  class: \"file-empty\"\n};\nconst _hoisted_19 = [\"onClick\", \"title\"];\nconst _hoisted_20 = {\n  key: 1,\n  class: \"file-empty\"\n};\nconst _hoisted_21 = [\"onClick\", \"title\"];\nconst _hoisted_22 = {\n  key: 1,\n  class: \"file-empty\"\n};\nconst _hoisted_23 = [\"onClick\", \"title\"];\nconst _hoisted_24 = {\n  key: 1,\n  class: \"file-empty\"\n};\nconst _hoisted_25 = [\"onClick\", \"title\"];\nconst _hoisted_26 = {\n  key: 1,\n  class: \"file-empty\"\n};\nconst _hoisted_27 = [\"onClick\", \"title\"];\nconst _hoisted_28 = {\n  key: 1,\n  class: \"file-empty\"\n};\nconst _hoisted_29 = [\"onClick\", \"title\"];\nconst _hoisted_30 = {\n  key: 1,\n  class: \"file-empty\"\n};\nconst _hoisted_31 = [\"onClick\", \"title\"];\nconst _hoisted_32 = {\n  key: 1,\n  class: \"file-empty\"\n};\nconst _hoisted_33 = [\"onClick\", \"title\"];\nconst _hoisted_34 = {\n  key: 1,\n  class: \"file-empty\"\n};\nconst _hoisted_35 = [\"onClick\", \"title\"];\nconst _hoisted_36 = {\n  key: 1,\n  class: \"file-empty\"\n};\nconst _hoisted_37 = [\"onClick\", \"title\"];\nconst _hoisted_38 = {\n  key: 1,\n  class: \"file-empty\"\n};\nconst _hoisted_39 = [\"onClick\", \"title\"];\nconst _hoisted_40 = {\n  key: 1,\n  class: \"file-empty\"\n};\nconst _hoisted_41 = [\"onClick\", \"title\"];\nconst _hoisted_42 = {\n  key: 1,\n  class: \"file-empty\"\n};\nconst _hoisted_43 = [\"onClick\", \"title\"];\nconst _hoisted_44 = {\n  key: 1,\n  class: \"file-empty\"\n};\nconst _hoisted_45 = {\n  class: \"pagination-container\"\n};\nconst _hoisted_46 = {\n  class: \"file-management-grid\"\n};\nconst _hoisted_47 = {\n  class: \"file-item\"\n};\nconst _hoisted_48 = {\n  class: \"file-controls\"\n};\nconst _hoisted_49 = {\n  key: 0,\n  class: \"current-file\"\n};\nconst _hoisted_50 = {\n  key: 1,\n  class: \"no-file\"\n};\nconst _hoisted_51 = {\n  class: \"file-item\"\n};\nconst _hoisted_52 = {\n  class: \"file-controls\"\n};\nconst _hoisted_53 = {\n  key: 0,\n  class: \"current-file\"\n};\nconst _hoisted_54 = {\n  key: 1,\n  class: \"no-file\"\n};\nconst _hoisted_55 = {\n  class: \"file-item\"\n};\nconst _hoisted_56 = {\n  class: \"file-controls\"\n};\nconst _hoisted_57 = {\n  key: 0,\n  class: \"current-file\"\n};\nconst _hoisted_58 = {\n  key: 1,\n  class: \"no-file\"\n};\nconst _hoisted_59 = {\n  class: \"file-item\"\n};\nconst _hoisted_60 = {\n  class: \"file-controls\"\n};\nconst _hoisted_61 = {\n  key: 0,\n  class: \"current-file\"\n};\nconst _hoisted_62 = {\n  key: 1,\n  class: \"no-file\"\n};\nconst _hoisted_63 = {\n  class: \"file-item\"\n};\nconst _hoisted_64 = {\n  class: \"file-controls\"\n};\nconst _hoisted_65 = {\n  key: 0,\n  class: \"current-file\"\n};\nconst _hoisted_66 = {\n  key: 1,\n  class: \"no-file\"\n};\nconst _hoisted_67 = {\n  class: \"file-item\"\n};\nconst _hoisted_68 = {\n  class: \"file-controls\"\n};\nconst _hoisted_69 = {\n  key: 0,\n  class: \"current-file\"\n};\nconst _hoisted_70 = {\n  key: 1,\n  class: \"no-file\"\n};\nconst _hoisted_71 = {\n  class: \"file-item\"\n};\nconst _hoisted_72 = {\n  class: \"file-controls\"\n};\nconst _hoisted_73 = {\n  key: 0,\n  class: \"current-file\"\n};\nconst _hoisted_74 = {\n  key: 1,\n  class: \"no-file\"\n};\nconst _hoisted_75 = {\n  class: \"file-item\"\n};\nconst _hoisted_76 = {\n  class: \"file-controls\"\n};\nconst _hoisted_77 = {\n  key: 0,\n  class: \"current-file\"\n};\nconst _hoisted_78 = {\n  key: 1,\n  class: \"no-file\"\n};\nconst _hoisted_79 = {\n  class: \"file-item\"\n};\nconst _hoisted_80 = {\n  class: \"file-controls\"\n};\nconst _hoisted_81 = {\n  key: 0,\n  class: \"current-file\"\n};\nconst _hoisted_82 = {\n  key: 1,\n  class: \"no-file\"\n};\nconst _hoisted_83 = {\n  class: \"file-item\"\n};\nconst _hoisted_84 = {\n  class: \"file-controls\"\n};\nconst _hoisted_85 = {\n  key: 0,\n  class: \"current-file\"\n};\nconst _hoisted_86 = {\n  key: 1,\n  class: \"no-file\"\n};\nconst _hoisted_87 = {\n  class: \"file-item\"\n};\nconst _hoisted_88 = {\n  class: \"file-controls\"\n};\nconst _hoisted_89 = {\n  key: 0,\n  class: \"current-file\"\n};\nconst _hoisted_90 = {\n  key: 1,\n  class: \"no-file\"\n};\nconst _hoisted_91 = {\n  class: \"file-item\"\n};\nconst _hoisted_92 = {\n  class: \"file-controls\"\n};\nconst _hoisted_93 = {\n  key: 0,\n  class: \"current-file\"\n};\nconst _hoisted_94 = {\n  key: 1,\n  class: \"no-file\"\n};\nconst _hoisted_95 = {\n  class: \"file-item\"\n};\nconst _hoisted_96 = {\n  class: \"file-controls\"\n};\nconst _hoisted_97 = {\n  key: 0,\n  class: \"current-file\"\n};\nconst _hoisted_98 = {\n  key: 1,\n  class: \"no-file\"\n};\nconst _hoisted_99 = {\n  class: \"file-item\"\n};\nconst _hoisted_100 = {\n  class: \"file-controls\"\n};\nconst _hoisted_101 = {\n  key: 0,\n  class: \"current-file\"\n};\nconst _hoisted_102 = {\n  key: 1,\n  class: \"no-file\"\n};\nconst _hoisted_103 = {\n  class: \"file-item\"\n};\nconst _hoisted_104 = {\n  class: \"file-controls\"\n};\nconst _hoisted_105 = {\n  key: 0,\n  class: \"current-file\"\n};\nconst _hoisted_106 = {\n  key: 1,\n  class: \"no-file\"\n};\nconst _hoisted_107 = {\n  class: \"dialog-footer\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_Delete = _resolveComponent(\"Delete\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_Plus = _resolveComponent(\"Plus\");\n  const _component_Refresh = _resolveComponent(\"Refresh\");\n  const _component_el_option = _resolveComponent(\"el-option\");\n  const _component_el_select = _resolveComponent(\"el-select\");\n  const _component_el_table_column = _resolveComponent(\"el-table-column\");\n  const _component_el_table = _resolveComponent(\"el-table\");\n  const _component_el_pagination = _resolveComponent(\"el-pagination\");\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_input_number = _resolveComponent(\"el-input-number\");\n  const _component_el_divider = _resolveComponent(\"el-divider\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  const _component_el_dialog = _resolveComponent(\"el-dialog\");\n  const _directive_loading = _resolveDirective(\"loading\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_cache[45] || (_cache[45] = _createElementVNode(\"h2\", null, \"数据表格管理\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"span\", null, \"总计: \" + _toDisplayString($setup.totalCount) + \" 条记录\", 1 /* TEXT */), $setup.tableData.length > 0 ? (_openBlock(), _createElementBlock(\"span\", _hoisted_5, \" (当前页: \" + _toDisplayString($setup.tableData.length) + \" 条) \", 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), _ctx.selectedRows.length > 0 ? (_openBlock(), _createElementBlock(\"span\", _hoisted_6, \" 已选择: \" + _toDisplayString(_ctx.selectedRows.length) + \" 条 \", 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_el_button, {\n    type: \"danger\",\n    disabled: _ctx.selectedRows.length === 0,\n    onClick: _ctx.handleBatchDelete\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Delete)]),\n      _: 1 /* STABLE */\n    }), _createTextVNode(\" 批量删除 (\" + _toDisplayString(_ctx.selectedRows.length) + \") \", 1 /* TEXT */)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"disabled\", \"onClick\"]), _createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: _cache[0] || (_cache[0] = $event => $setup.showAddDialog = true)\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Plus)]),\n      _: 1 /* STABLE */\n    }), _cache[42] || (_cache[42] = _createTextVNode(\" 新增 Accession \"))]),\n    _: 1 /* STABLE */,\n    __: [42]\n  }), _createVNode(_component_el_button, {\n    onClick: $setup.refreshData\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Refresh)]),\n      _: 1 /* STABLE */\n    }), _cache[43] || (_cache[43] = _createTextVNode(\" 刷新数据 \"))]),\n    _: 1 /* STABLE */,\n    __: [43]\n  }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_button, {\n    type: \"success\",\n    onClick: $setup.handleUpdateData\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Refresh)]),\n      _: 1 /* STABLE */\n    }), _cache[44] || (_cache[44] = _createTextVNode(\" 更新数据 \"))]),\n    _: 1 /* STABLE */,\n    __: [44]\n  }, 8 /* PROPS */, [\"onClick\"])])])]), _createCommentVNode(\" 搜索和筛选区域 \"), _createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"div\", _hoisted_9, [_createCommentVNode(\" Accession搜索框 \"), _createElementVNode(\"div\", _hoisted_10, [_cache[46] || (_cache[46] = _createElementVNode(\"label\", null, \"Accession搜索:\", -1 /* CACHED */)), _createVNode(_component_el_select, {\n    modelValue: $setup.searchAccession,\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.searchAccession = $event),\n    placeholder: \"请选择Accession\",\n    filterable: \"\",\n    clearable: \"\",\n    style: {\n      \"width\": \"250px\"\n    },\n    onChange: $setup.handleAccessionChange,\n    onClear: $setup.handleAccessionClear\n  }, {\n    default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.allAccessionOptions, accession => {\n      return _openBlock(), _createBlock(_component_el_option, {\n        key: accession,\n        label: accession,\n        value: accession\n      }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n    }), 128 /* KEYED_FRAGMENT */))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onChange\", \"onClear\"])]), _createCommentVNode(\" SubPopulation筛选 \"), _createElementVNode(\"div\", _hoisted_11, [_cache[47] || (_cache[47] = _createElementVNode(\"label\", null, \"SubPopulation筛选:\", -1 /* CACHED */)), _createVNode(_component_el_select, {\n    modelValue: $setup.selectedSubPopulations,\n    \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.selectedSubPopulations = $event),\n    placeholder: \"请选择亚群\",\n    multiple: \"\",\n    \"collapse-tags\": \"\",\n    \"collapse-tags-tooltip\": \"\",\n    clearable: \"\",\n    style: {\n      \"width\": \"300px\"\n    },\n    onChange: $setup.handleSubPopulationFilterChange\n  }, {\n    default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.subPopulationOptions, option => {\n      return _openBlock(), _createBlock(_component_el_option, {\n        key: option.value,\n        label: option.label,\n        value: option.value\n      }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n    }), 128 /* KEYED_FRAGMENT */))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onChange\"])]), _createCommentVNode(\" 重置按钮 \"), _createElementVNode(\"div\", _hoisted_12, [_createVNode(_component_el_button, {\n    onClick: $setup.resetFilters\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Refresh)]),\n      _: 1 /* STABLE */\n    }), _cache[48] || (_cache[48] = _createTextVNode(\" 重置筛选 \"))]),\n    _: 1 /* STABLE */,\n    __: [48]\n  }, 8 /* PROPS */, [\"onClick\"])])])]), _createCommentVNode(\" 数据表格 \"), _withDirectives((_openBlock(), _createBlock(_component_el_table, {\n    data: $setup.tableData,\n    border: \"\",\n    stripe: \"\",\n    style: {\n      \"width\": \"100%\"\n    },\n    \"header-cell-style\": {\n      background: '#f0f5ff',\n      color: '#1a56db',\n      fontWeight: 'bold'\n    },\n    onSelectionChange: _ctx.handleSelectionChange\n  }, {\n    default: _withCtx(() => [_createCommentVNode(\" 多选框列 \"), _createVNode(_component_el_table_column, {\n      type: \"selection\",\n      width: \"55\",\n      fixed: \"left\"\n    }), _createVNode(_component_el_table_column, {\n      prop: \"accession\",\n      label: \"Accession\",\n      width: \"150\",\n      fixed: \"left\"\n    }), _createVNode(_component_el_table_column, {\n      prop: \"subPopulation\",\n      label: \"SubPopulation\",\n      width: \"120\"\n    }), _createVNode(_component_el_table_column, {\n      prop: \"seqData\",\n      label: \"SeqData\",\n      width: \"200\"\n    }, {\n      default: _withCtx(scope => [scope.row.seqData && scope.row.seqData !== '-' ? (_openBlock(), _createElementBlock(\"a\", {\n        key: 0,\n        href: scope.row.seqData,\n        target: \"_blank\",\n        class: \"data-link\"\n      }, _toDisplayString(scope.row.seqData), 9 /* TEXT, PROPS */, _hoisted_13)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_14, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      prop: \"longitude\",\n      label: \"Longitude\",\n      width: \"100\"\n    }), _createVNode(_component_el_table_column, {\n      prop: \"latitude\",\n      label: \"Latitude\",\n      width: \"100\"\n    }), _createCommentVNode(\" 文件显示列 \"), _createVNode(_component_el_table_column, {\n      label: \"Genome\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [scope.row.genomeFile ? (_openBlock(), _createElementBlock(\"span\", {\n        key: 0,\n        class: \"file-link\",\n        onClick: $event => $setup.downloadFile(scope.row.accession, 'genome'),\n        title: scope.row.genomeFile\n      }, _toDisplayString(scope.row.genomeFile), 9 /* TEXT, PROPS */, _hoisted_15)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_16, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"Annotation\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [scope.row.annotationFile ? (_openBlock(), _createElementBlock(\"span\", {\n        key: 0,\n        class: \"file-link\",\n        onClick: $event => $setup.downloadFile(scope.row.accession, 'annotation'),\n        title: scope.row.annotationFile\n      }, _toDisplayString(scope.row.annotationFile), 9 /* TEXT, PROPS */, _hoisted_17)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_18, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"Transcriptome.all\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [scope.row.transcriptomeAllFile ? (_openBlock(), _createElementBlock(\"span\", {\n        key: 0,\n        class: \"file-link\",\n        onClick: $event => $setup.downloadFile(scope.row.accession, 'transcriptome.all'),\n        title: scope.row.transcriptomeAllFile\n      }, _toDisplayString(scope.row.transcriptomeAllFile), 9 /* TEXT, PROPS */, _hoisted_19)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_20, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"Transcriptome.leaf\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [scope.row.transcriptomeLeafFile ? (_openBlock(), _createElementBlock(\"span\", {\n        key: 0,\n        class: \"file-link\",\n        onClick: $event => $setup.downloadFile(scope.row.accession, 'transcriptome.leaf'),\n        title: scope.row.transcriptomeLeafFile\n      }, _toDisplayString(scope.row.transcriptomeLeafFile), 9 /* TEXT, PROPS */, _hoisted_21)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_22, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"Transcriptome.panicles\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [scope.row.transcriptomePaniclesFile ? (_openBlock(), _createElementBlock(\"span\", {\n        key: 0,\n        class: \"file-link\",\n        onClick: $event => $setup.downloadFile(scope.row.accession, 'transcriptome.panicles'),\n        title: scope.row.transcriptomePaniclesFile\n      }, _toDisplayString(scope.row.transcriptomePaniclesFile), 9 /* TEXT, PROPS */, _hoisted_23)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_24, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"Transcriptome.shoot\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [scope.row.transcriptomeShootFile ? (_openBlock(), _createElementBlock(\"span\", {\n        key: 0,\n        class: \"file-link\",\n        onClick: $event => $setup.downloadFile(scope.row.accession, 'transcriptome.shoot'),\n        title: scope.row.transcriptomeShootFile\n      }, _toDisplayString(scope.row.transcriptomeShootFile), 9 /* TEXT, PROPS */, _hoisted_25)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_26, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"Transcriptome.stem\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [scope.row.transcriptomeStemFile ? (_openBlock(), _createElementBlock(\"span\", {\n        key: 0,\n        class: \"file-link\",\n        onClick: $event => $setup.downloadFile(scope.row.accession, 'transcriptome.stem'),\n        title: scope.row.transcriptomeStemFile\n      }, _toDisplayString(scope.row.transcriptomeStemFile), 9 /* TEXT, PROPS */, _hoisted_27)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_28, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"Transcriptome.root\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [scope.row.transcriptomeRootFile ? (_openBlock(), _createElementBlock(\"span\", {\n        key: 0,\n        class: \"file-link\",\n        onClick: $event => $setup.downloadFile(scope.row.accession, 'transcriptome.root'),\n        title: scope.row.transcriptomeRootFile\n      }, _toDisplayString(scope.row.transcriptomeRootFile), 9 /* TEXT, PROPS */, _hoisted_29)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_30, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"Codon\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [scope.row.codonFile ? (_openBlock(), _createElementBlock(\"span\", {\n        key: 0,\n        class: \"file-link\",\n        onClick: $event => $setup.downloadFile(scope.row.accession, 'codon'),\n        title: scope.row.codonFile\n      }, _toDisplayString(scope.row.codonFile), 9 /* TEXT, PROPS */, _hoisted_31)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_32, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"Centromere\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [scope.row.centromereFile ? (_openBlock(), _createElementBlock(\"span\", {\n        key: 0,\n        class: \"file-link\",\n        onClick: $event => $setup.downloadFile(scope.row.accession, 'centromere'),\n        title: scope.row.centromereFile\n      }, _toDisplayString(scope.row.centromereFile), 9 /* TEXT, PROPS */, _hoisted_33)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_34, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"TEs\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [scope.row.tesFile ? (_openBlock(), _createElementBlock(\"span\", {\n        key: 0,\n        class: \"file-link\",\n        onClick: $event => $setup.downloadFile(scope.row.accession, 'TEs'),\n        title: scope.row.tesFile\n      }, _toDisplayString(scope.row.tesFile), 9 /* TEXT, PROPS */, _hoisted_35)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_36, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"CoreBlocks\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [scope.row.coreBlocksFile ? (_openBlock(), _createElementBlock(\"span\", {\n        key: 0,\n        class: \"file-link\",\n        onClick: $event => $setup.downloadFile(scope.row.accession, 'coreBlocks'),\n        title: scope.row.coreBlocksFile\n      }, _toDisplayString(scope.row.coreBlocksFile), 9 /* TEXT, PROPS */, _hoisted_37)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_38, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"miRNA\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [scope.row.miRNAFile ? (_openBlock(), _createElementBlock(\"span\", {\n        key: 0,\n        class: \"file-link\",\n        onClick: $event => $setup.downloadFile(scope.row.accession, 'miRNA'),\n        title: scope.row.miRNAFile\n      }, _toDisplayString(scope.row.miRNAFile), 9 /* TEXT, PROPS */, _hoisted_39)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_40, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"tRNA\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [scope.row.tRNAFile ? (_openBlock(), _createElementBlock(\"span\", {\n        key: 0,\n        class: \"file-link\",\n        onClick: $event => $setup.downloadFile(scope.row.accession, 'tRNA'),\n        title: scope.row.tRNAFile\n      }, _toDisplayString(scope.row.tRNAFile), 9 /* TEXT, PROPS */, _hoisted_41)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_42, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"rRNA\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [scope.row.rRNAFile ? (_openBlock(), _createElementBlock(\"span\", {\n        key: 0,\n        class: \"file-link\",\n        onClick: $event => $setup.downloadFile(scope.row.accession, 'rRNA'),\n        title: scope.row.rRNAFile\n      }, _toDisplayString(scope.row.rRNAFile), 9 /* TEXT, PROPS */, _hoisted_43)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_44, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"操作\",\n      width: \"150\",\n      fixed: \"right\"\n    }, {\n      default: _withCtx(scope => [_createVNode(_component_el_button, {\n        type: \"primary\",\n        size: \"small\",\n        onClick: $event => $setup.editRow(scope.row)\n      }, {\n        default: _withCtx(() => _cache[49] || (_cache[49] = [_createTextVNode(\"编辑\")])),\n        _: 2 /* DYNAMIC */,\n        __: [49]\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"]), _createVNode(_component_el_button, {\n        type: \"danger\",\n        size: \"small\",\n        onClick: $event => $setup.deleteRow(scope.row)\n      }, {\n        default: _withCtx(() => _cache[50] || (_cache[50] = [_createTextVNode(\"删除\")])),\n        _: 2 /* DYNAMIC */,\n        __: [50]\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"data\", \"onSelectionChange\"])), [[_directive_loading, $setup.loading]]), _createCommentVNode(\" 分页 \"), _createElementVNode(\"div\", _hoisted_45, [_createVNode(_component_el_pagination, {\n    \"current-page\": $setup.currentPage,\n    \"onUpdate:currentPage\": _cache[3] || (_cache[3] = $event => $setup.currentPage = $event),\n    \"page-size\": $setup.pageSize,\n    \"onUpdate:pageSize\": _cache[4] || (_cache[4] = $event => $setup.pageSize = $event),\n    \"page-sizes\": [20, 50, 100],\n    total: $setup.totalCount,\n    layout: \"total, sizes, prev, pager, next, jumper\",\n    onSizeChange: $setup.handleSizeChange,\n    onCurrentChange: $setup.handleCurrentChange\n  }, null, 8 /* PROPS */, [\"current-page\", \"page-size\", \"total\", \"onSizeChange\", \"onCurrentChange\"])]), _createCommentVNode(\" 新增/编辑对话框 \"), _createVNode(_component_el_dialog, {\n    modelValue: $setup.showAddDialog,\n    \"onUpdate:modelValue\": _cache[41] || (_cache[41] = $event => $setup.showAddDialog = $event),\n    title: $setup.editingRow ? '编辑 Accession' : '新增 Accession',\n    width: \"600px\",\n    onClose: $setup.resetForm\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"span\", _hoisted_107, [_createVNode(_component_el_button, {\n      onClick: _cache[40] || (_cache[40] = $event => $setup.showAddDialog = false)\n    }, {\n      default: _withCtx(() => _cache[97] || (_cache[97] = [_createTextVNode(\"取消\")])),\n      _: 1 /* STABLE */,\n      __: [97]\n    }), _createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $setup.saveData,\n      loading: $setup.saving\n    }, {\n      default: _withCtx(() => _cache[98] || (_cache[98] = [_createTextVNode(\"保存\")])),\n      _: 1 /* STABLE */,\n      __: [98]\n    }, 8 /* PROPS */, [\"onClick\", \"loading\"])])]),\n    default: _withCtx(() => [_createVNode(_component_el_form, {\n      model: $setup.formData,\n      rules: $setup.formRules,\n      ref: \"formRef\",\n      \"label-width\": \"120px\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_form_item, {\n        label: \"Accession\",\n        prop: \"accession\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.formData.accession,\n          \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $setup.formData.accession = $event),\n          disabled: $setup.editingRow\n        }, null, 8 /* PROPS */, [\"modelValue\", \"disabled\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"SubPopulation\",\n        prop: \"subPopulation\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_select, {\n          modelValue: $setup.formData.subPopulation,\n          \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $setup.formData.subPopulation = $event),\n          placeholder: \"请选择或输入亚群\",\n          filterable: \"\",\n          \"allow-create\": \"\",\n          \"default-first-option\": \"\",\n          \"reserve-keyword\": false,\n          onChange: _ctx.handleSubPopulationChange\n        }, {\n          default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.subPopulationOptions, option => {\n            return _openBlock(), _createBlock(_component_el_option, {\n              key: option.value,\n              label: option.label,\n              value: option.value\n            }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n          }), 128 /* KEYED_FRAGMENT */))]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"modelValue\", \"onChange\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"SeqData URL\",\n        prop: \"seqData\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.formData.seqData,\n          \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $setup.formData.seqData = $event),\n          placeholder: \"请输入SeqData链接，留空则为 -\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"经度\",\n        prop: \"longitude\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input_number, {\n          modelValue: $setup.formData.longitude,\n          \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $setup.formData.longitude = $event),\n          precision: 2,\n          placeholder: \"经度\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"纬度\",\n        prop: \"latitude\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input_number, {\n          modelValue: $setup.formData.latitude,\n          \"onUpdate:modelValue\": _cache[9] || (_cache[9] = $event => $setup.formData.latitude = $event),\n          precision: 2,\n          placeholder: \"纬度\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createCommentVNode(\" 文件管理部分 \"), _createVNode(_component_el_divider, {\n        \"content-position\": \"left\"\n      }, {\n        default: _withCtx(() => _cache[51] || (_cache[51] = [_createTextVNode(\"文件管理\")])),\n        _: 1 /* STABLE */,\n        __: [51]\n      }), _createElementVNode(\"div\", _hoisted_46, [_createCommentVNode(\" Genome \"), _createElementVNode(\"div\", _hoisted_47, [_cache[54] || (_cache[54] = _createElementVNode(\"label\", null, \"Genome:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_48, [$setup.formData.files.genome ? (_openBlock(), _createElementBlock(\"span\", _hoisted_49, _toDisplayString($setup.formData.files.genome), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_50, \"无文件\")), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[10] || (_cache[10] = $event => $setup.selectFile('genome'))\n      }, {\n        default: _withCtx(() => _cache[52] || (_cache[52] = [_createTextVNode(\"选择文件\")])),\n        _: 1 /* STABLE */,\n        __: [52]\n      }), $setup.formData.files.genome ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 2,\n        size: \"small\",\n        type: \"danger\",\n        onClick: _cache[11] || (_cache[11] = $event => $setup.removeFile('genome'))\n      }, {\n        default: _withCtx(() => _cache[53] || (_cache[53] = [_createTextVNode(\"删除\")])),\n        _: 1 /* STABLE */,\n        __: [53]\n      })) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" Annotation \"), _createElementVNode(\"div\", _hoisted_51, [_cache[57] || (_cache[57] = _createElementVNode(\"label\", null, \"Annotation:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_52, [$setup.formData.files.annotation ? (_openBlock(), _createElementBlock(\"span\", _hoisted_53, _toDisplayString($setup.formData.files.annotation), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_54, \"无文件\")), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[12] || (_cache[12] = $event => $setup.selectFile('annotation'))\n      }, {\n        default: _withCtx(() => _cache[55] || (_cache[55] = [_createTextVNode(\"选择文件\")])),\n        _: 1 /* STABLE */,\n        __: [55]\n      }), $setup.formData.files.annotation ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 2,\n        size: \"small\",\n        type: \"danger\",\n        onClick: _cache[13] || (_cache[13] = $event => $setup.removeFile('annotation'))\n      }, {\n        default: _withCtx(() => _cache[56] || (_cache[56] = [_createTextVNode(\"删除\")])),\n        _: 1 /* STABLE */,\n        __: [56]\n      })) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" Transcriptome.all \"), _createElementVNode(\"div\", _hoisted_55, [_cache[60] || (_cache[60] = _createElementVNode(\"label\", null, \"Transcriptome.all:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_56, [$setup.formData.files.transcriptomeAll ? (_openBlock(), _createElementBlock(\"span\", _hoisted_57, _toDisplayString($setup.formData.files.transcriptomeAll), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_58, \"无文件\")), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[14] || (_cache[14] = $event => $setup.selectFile('transcriptomeAll'))\n      }, {\n        default: _withCtx(() => _cache[58] || (_cache[58] = [_createTextVNode(\"选择文件\")])),\n        _: 1 /* STABLE */,\n        __: [58]\n      }), $setup.formData.files.transcriptomeAll ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 2,\n        size: \"small\",\n        type: \"danger\",\n        onClick: _cache[15] || (_cache[15] = $event => $setup.removeFile('transcriptomeAll'))\n      }, {\n        default: _withCtx(() => _cache[59] || (_cache[59] = [_createTextVNode(\"删除\")])),\n        _: 1 /* STABLE */,\n        __: [59]\n      })) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" Transcriptome.leaf \"), _createElementVNode(\"div\", _hoisted_59, [_cache[63] || (_cache[63] = _createElementVNode(\"label\", null, \"Transcriptome.leaf:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_60, [$setup.formData.files.transcriptomeLeaf ? (_openBlock(), _createElementBlock(\"span\", _hoisted_61, _toDisplayString($setup.formData.files.transcriptomeLeaf), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_62, \"无文件\")), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[16] || (_cache[16] = $event => $setup.selectFile('transcriptomeLeaf'))\n      }, {\n        default: _withCtx(() => _cache[61] || (_cache[61] = [_createTextVNode(\"选择文件\")])),\n        _: 1 /* STABLE */,\n        __: [61]\n      }), $setup.formData.files.transcriptomeLeaf ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 2,\n        size: \"small\",\n        type: \"danger\",\n        onClick: _cache[17] || (_cache[17] = $event => $setup.removeFile('transcriptomeLeaf'))\n      }, {\n        default: _withCtx(() => _cache[62] || (_cache[62] = [_createTextVNode(\"删除\")])),\n        _: 1 /* STABLE */,\n        __: [62]\n      })) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" Transcriptome.panicles \"), _createElementVNode(\"div\", _hoisted_63, [_cache[66] || (_cache[66] = _createElementVNode(\"label\", null, \"Transcriptome.panicles:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_64, [$setup.formData.files.transcriptomePanicles ? (_openBlock(), _createElementBlock(\"span\", _hoisted_65, _toDisplayString($setup.formData.files.transcriptomePanicles), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_66, \"无文件\")), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[18] || (_cache[18] = $event => $setup.selectFile('transcriptomePanicles'))\n      }, {\n        default: _withCtx(() => _cache[64] || (_cache[64] = [_createTextVNode(\"选择文件\")])),\n        _: 1 /* STABLE */,\n        __: [64]\n      }), $setup.formData.files.transcriptomePanicles ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 2,\n        size: \"small\",\n        type: \"danger\",\n        onClick: _cache[19] || (_cache[19] = $event => $setup.removeFile('transcriptomePanicles'))\n      }, {\n        default: _withCtx(() => _cache[65] || (_cache[65] = [_createTextVNode(\"删除\")])),\n        _: 1 /* STABLE */,\n        __: [65]\n      })) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" Transcriptome.shoot \"), _createElementVNode(\"div\", _hoisted_67, [_cache[69] || (_cache[69] = _createElementVNode(\"label\", null, \"Transcriptome.shoot:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_68, [$setup.formData.files.transcriptomeShoot ? (_openBlock(), _createElementBlock(\"span\", _hoisted_69, _toDisplayString($setup.formData.files.transcriptomeShoot), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_70, \"无文件\")), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[20] || (_cache[20] = $event => $setup.selectFile('transcriptomeShoot'))\n      }, {\n        default: _withCtx(() => _cache[67] || (_cache[67] = [_createTextVNode(\"选择文件\")])),\n        _: 1 /* STABLE */,\n        __: [67]\n      }), $setup.formData.files.transcriptomeShoot ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 2,\n        size: \"small\",\n        type: \"danger\",\n        onClick: _cache[21] || (_cache[21] = $event => $setup.removeFile('transcriptomeShoot'))\n      }, {\n        default: _withCtx(() => _cache[68] || (_cache[68] = [_createTextVNode(\"删除\")])),\n        _: 1 /* STABLE */,\n        __: [68]\n      })) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" Transcriptome.stem \"), _createElementVNode(\"div\", _hoisted_71, [_cache[72] || (_cache[72] = _createElementVNode(\"label\", null, \"Transcriptome.stem:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_72, [$setup.formData.files.transcriptomeStem ? (_openBlock(), _createElementBlock(\"span\", _hoisted_73, _toDisplayString($setup.formData.files.transcriptomeStem), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_74, \"无文件\")), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[22] || (_cache[22] = $event => $setup.selectFile('transcriptomeStem'))\n      }, {\n        default: _withCtx(() => _cache[70] || (_cache[70] = [_createTextVNode(\"选择文件\")])),\n        _: 1 /* STABLE */,\n        __: [70]\n      }), $setup.formData.files.transcriptomeStem ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 2,\n        size: \"small\",\n        type: \"danger\",\n        onClick: _cache[23] || (_cache[23] = $event => $setup.removeFile('transcriptomeStem'))\n      }, {\n        default: _withCtx(() => _cache[71] || (_cache[71] = [_createTextVNode(\"删除\")])),\n        _: 1 /* STABLE */,\n        __: [71]\n      })) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" Transcriptome.root \"), _createElementVNode(\"div\", _hoisted_75, [_cache[75] || (_cache[75] = _createElementVNode(\"label\", null, \"Transcriptome.root:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_76, [$setup.formData.files.transcriptomeRoot ? (_openBlock(), _createElementBlock(\"span\", _hoisted_77, _toDisplayString($setup.formData.files.transcriptomeRoot), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_78, \"无文件\")), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[24] || (_cache[24] = $event => $setup.selectFile('transcriptomeRoot'))\n      }, {\n        default: _withCtx(() => _cache[73] || (_cache[73] = [_createTextVNode(\"选择文件\")])),\n        _: 1 /* STABLE */,\n        __: [73]\n      }), $setup.formData.files.transcriptomeRoot ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 2,\n        size: \"small\",\n        type: \"danger\",\n        onClick: _cache[25] || (_cache[25] = $event => $setup.removeFile('transcriptomeRoot'))\n      }, {\n        default: _withCtx(() => _cache[74] || (_cache[74] = [_createTextVNode(\"删除\")])),\n        _: 1 /* STABLE */,\n        __: [74]\n      })) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" Codon \"), _createElementVNode(\"div\", _hoisted_79, [_cache[78] || (_cache[78] = _createElementVNode(\"label\", null, \"Codon:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_80, [$setup.formData.files.codon ? (_openBlock(), _createElementBlock(\"span\", _hoisted_81, _toDisplayString($setup.formData.files.codon), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_82, \"无文件\")), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[26] || (_cache[26] = $event => $setup.selectFile('codon'))\n      }, {\n        default: _withCtx(() => _cache[76] || (_cache[76] = [_createTextVNode(\"选择文件\")])),\n        _: 1 /* STABLE */,\n        __: [76]\n      }), $setup.formData.files.codon ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 2,\n        size: \"small\",\n        type: \"danger\",\n        onClick: _cache[27] || (_cache[27] = $event => $setup.removeFile('codon'))\n      }, {\n        default: _withCtx(() => _cache[77] || (_cache[77] = [_createTextVNode(\"删除\")])),\n        _: 1 /* STABLE */,\n        __: [77]\n      })) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" Centromere \"), _createElementVNode(\"div\", _hoisted_83, [_cache[81] || (_cache[81] = _createElementVNode(\"label\", null, \"Centromere:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_84, [$setup.formData.files.centromere ? (_openBlock(), _createElementBlock(\"span\", _hoisted_85, _toDisplayString($setup.formData.files.centromere), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_86, \"无文件\")), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[28] || (_cache[28] = $event => $setup.selectFile('centromere'))\n      }, {\n        default: _withCtx(() => _cache[79] || (_cache[79] = [_createTextVNode(\"选择文件\")])),\n        _: 1 /* STABLE */,\n        __: [79]\n      }), $setup.formData.files.centromere ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 2,\n        size: \"small\",\n        type: \"danger\",\n        onClick: _cache[29] || (_cache[29] = $event => $setup.removeFile('centromere'))\n      }, {\n        default: _withCtx(() => _cache[80] || (_cache[80] = [_createTextVNode(\"删除\")])),\n        _: 1 /* STABLE */,\n        __: [80]\n      })) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" TEs \"), _createElementVNode(\"div\", _hoisted_87, [_cache[84] || (_cache[84] = _createElementVNode(\"label\", null, \"TEs:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_88, [$setup.formData.files.TEs ? (_openBlock(), _createElementBlock(\"span\", _hoisted_89, _toDisplayString($setup.formData.files.TEs), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_90, \"无文件\")), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[30] || (_cache[30] = $event => $setup.selectFile('TEs'))\n      }, {\n        default: _withCtx(() => _cache[82] || (_cache[82] = [_createTextVNode(\"选择文件\")])),\n        _: 1 /* STABLE */,\n        __: [82]\n      }), $setup.formData.files.TEs ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 2,\n        size: \"small\",\n        type: \"danger\",\n        onClick: _cache[31] || (_cache[31] = $event => $setup.removeFile('TEs'))\n      }, {\n        default: _withCtx(() => _cache[83] || (_cache[83] = [_createTextVNode(\"删除\")])),\n        _: 1 /* STABLE */,\n        __: [83]\n      })) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" CoreBlocks \"), _createElementVNode(\"div\", _hoisted_91, [_cache[87] || (_cache[87] = _createElementVNode(\"label\", null, \"CoreBlocks:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_92, [$setup.formData.files.coreBlocks ? (_openBlock(), _createElementBlock(\"span\", _hoisted_93, _toDisplayString($setup.formData.files.coreBlocks), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_94, \"无文件\")), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[32] || (_cache[32] = $event => $setup.selectFile('coreBlocks'))\n      }, {\n        default: _withCtx(() => _cache[85] || (_cache[85] = [_createTextVNode(\"选择文件\")])),\n        _: 1 /* STABLE */,\n        __: [85]\n      }), $setup.formData.files.coreBlocks ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 2,\n        size: \"small\",\n        type: \"danger\",\n        onClick: _cache[33] || (_cache[33] = $event => $setup.removeFile('coreBlocks'))\n      }, {\n        default: _withCtx(() => _cache[86] || (_cache[86] = [_createTextVNode(\"删除\")])),\n        _: 1 /* STABLE */,\n        __: [86]\n      })) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" miRNA \"), _createElementVNode(\"div\", _hoisted_95, [_cache[90] || (_cache[90] = _createElementVNode(\"label\", null, \"miRNA:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_96, [$setup.formData.files.miRNA ? (_openBlock(), _createElementBlock(\"span\", _hoisted_97, _toDisplayString($setup.formData.files.miRNA), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_98, \"无文件\")), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[34] || (_cache[34] = $event => $setup.selectFile('miRNA'))\n      }, {\n        default: _withCtx(() => _cache[88] || (_cache[88] = [_createTextVNode(\"选择文件\")])),\n        _: 1 /* STABLE */,\n        __: [88]\n      }), $setup.formData.files.miRNA ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 2,\n        size: \"small\",\n        type: \"danger\",\n        onClick: _cache[35] || (_cache[35] = $event => $setup.removeFile('miRNA'))\n      }, {\n        default: _withCtx(() => _cache[89] || (_cache[89] = [_createTextVNode(\"删除\")])),\n        _: 1 /* STABLE */,\n        __: [89]\n      })) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" tRNA \"), _createElementVNode(\"div\", _hoisted_99, [_cache[93] || (_cache[93] = _createElementVNode(\"label\", null, \"tRNA:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_100, [$setup.formData.files.tRNA ? (_openBlock(), _createElementBlock(\"span\", _hoisted_101, _toDisplayString($setup.formData.files.tRNA), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_102, \"无文件\")), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[36] || (_cache[36] = $event => $setup.selectFile('tRNA'))\n      }, {\n        default: _withCtx(() => _cache[91] || (_cache[91] = [_createTextVNode(\"选择文件\")])),\n        _: 1 /* STABLE */,\n        __: [91]\n      }), $setup.formData.files.tRNA ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 2,\n        size: \"small\",\n        type: \"danger\",\n        onClick: _cache[37] || (_cache[37] = $event => $setup.removeFile('tRNA'))\n      }, {\n        default: _withCtx(() => _cache[92] || (_cache[92] = [_createTextVNode(\"删除\")])),\n        _: 1 /* STABLE */,\n        __: [92]\n      })) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" rRNA \"), _createElementVNode(\"div\", _hoisted_103, [_cache[96] || (_cache[96] = _createElementVNode(\"label\", null, \"rRNA:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_104, [$setup.formData.files.rRNA ? (_openBlock(), _createElementBlock(\"span\", _hoisted_105, _toDisplayString($setup.formData.files.rRNA), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_106, \"无文件\")), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[38] || (_cache[38] = $event => $setup.selectFile('rRNA'))\n      }, {\n        default: _withCtx(() => _cache[94] || (_cache[94] = [_createTextVNode(\"选择文件\")])),\n        _: 1 /* STABLE */,\n        __: [94]\n      }), $setup.formData.files.rRNA ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 2,\n        size: \"small\",\n        type: \"danger\",\n        onClick: _cache[39] || (_cache[39] = $event => $setup.removeFile('rRNA'))\n      }, {\n        default: _withCtx(() => _cache[95] || (_cache[95] = [_createTextVNode(\"删除\")])),\n        _: 1 /* STABLE */,\n        __: [95]\n      })) : _createCommentVNode(\"v-if\", true)])])])]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"model\", \"rules\"])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"title\", \"onClose\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_toDisplayString", "$setup", "totalCount", "tableData", "length", "_hoisted_5", "_ctx", "selectedRows", "_hoisted_6", "_hoisted_7", "_createVNode", "_component_el_button", "type", "disabled", "onClick", "handleBatchDelete", "_component_el_icon", "_component_Delete", "_cache", "$event", "showAddDialog", "_component_Plus", "refreshData", "_component_Refresh", "handleUpdateData", "_createCommentVNode", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_component_el_select", "searchAccession", "placeholder", "filterable", "clearable", "style", "onChange", "handleAccessionChange", "onClear", "handleAccessionClear", "_Fragment", "_renderList", "allAccessionOptions", "accession", "_createBlock", "_component_el_option", "key", "label", "value", "_hoisted_11", "selectedSubPopulations", "multiple", "handleSubPopulationFilterChange", "subPopulationOptions", "option", "_hoisted_12", "resetFilters", "_component_el_table", "data", "border", "stripe", "background", "color", "fontWeight", "onSelectionChange", "handleSelectionChange", "_component_el_table_column", "width", "fixed", "prop", "default", "_withCtx", "scope", "row", "seqData", "href", "target", "_hoisted_13", "_hoisted_14", "genomeFile", "downloadFile", "title", "_hoisted_15", "_hoisted_16", "annotationFile", "_hoisted_17", "_hoisted_18", "transcriptomeAllFile", "_hoisted_19", "_hoisted_20", "transcriptomeLeafFile", "_hoisted_21", "_hoisted_22", "transcriptomePaniclesFile", "_hoisted_23", "_hoisted_24", "transcriptomeShootFile", "_hoisted_25", "_hoisted_26", "transcriptomeStemFile", "_hoisted_27", "_hoisted_28", "transcriptomeRootFile", "_hoisted_29", "_hoisted_30", "codonFile", "_hoisted_31", "_hoisted_32", "centromereFile", "_hoisted_33", "_hoisted_34", "tesFile", "_hoisted_35", "_hoisted_36", "coreBlocksFile", "_hoisted_37", "_hoisted_38", "miRNAFile", "_hoisted_39", "_hoisted_40", "tRNAFile", "_hoisted_41", "_hoisted_42", "rRNAFile", "_hoisted_43", "_hoisted_44", "size", "editRow", "deleteRow", "loading", "_hoisted_45", "_component_el_pagination", "currentPage", "pageSize", "total", "layout", "onSizeChange", "handleSizeChange", "onCurrentChange", "handleCurrentChange", "_component_el_dialog", "editingRow", "onClose", "resetForm", "footer", "_hoisted_107", "saveData", "saving", "_component_el_form", "model", "formData", "rules", "formRules", "ref", "_component_el_form_item", "_component_el_input", "subPopulation", "handleSubPopulationChange", "_component_el_input_number", "longitude", "precision", "latitude", "_component_el_divider", "_hoisted_46", "_hoisted_47", "_hoisted_48", "files", "genome", "_hoisted_49", "_hoisted_50", "selectFile", "removeFile", "_hoisted_51", "_hoisted_52", "annotation", "_hoisted_53", "_hoisted_54", "_hoisted_55", "_hoisted_56", "transcriptomeAll", "_hoisted_57", "_hoisted_58", "_hoisted_59", "_hoisted_60", "transcriptomeLeaf", "_hoisted_61", "_hoisted_62", "_hoisted_63", "_hoisted_64", "transcriptomePanicles", "_hoisted_65", "_hoisted_66", "_hoisted_67", "_hoisted_68", "transcriptomeShoot", "_hoisted_69", "_hoisted_70", "_hoisted_71", "_hoisted_72", "transcriptomeStem", "_hoisted_73", "_hoisted_74", "_hoisted_75", "_hoisted_76", "transcriptomeRoot", "_hoisted_77", "_hoisted_78", "_hoisted_79", "_hoisted_80", "codon", "_hoisted_81", "_hoisted_82", "_hoisted_83", "_hoisted_84", "centromere", "_hoisted_85", "_hoisted_86", "_hoisted_87", "_hoisted_88", "TEs", "_hoisted_89", "_hoisted_90", "_hoisted_91", "_hoisted_92", "coreBlocks", "_hoisted_93", "_hoisted_94", "_hoisted_95", "_hoisted_96", "miRNA", "_hoisted_97", "_hoisted_98", "_hoisted_99", "_hoisted_100", "tRNA", "_hoisted_101", "_hoisted_102", "_hoisted_103", "_hoisted_104", "rRNA", "_hoisted_105", "_hoisted_106"], "sources": ["D:\\gene_manage_system\\vue_project\\src\\views\\AdminDataManager.vue"], "sourcesContent": ["<template>\n  <div class=\"admin-data-manager\">\n    <div class=\"page-header\">\n      <h2>数据表格管理</h2>\n      <div class=\"header-actions\">\n        <div class=\"stats-info\">\n          <span>总计: {{ totalCount }} 条记录</span>\n          <span v-if=\"tableData.length > 0\">\n            (当前页: {{ tableData.length }} 条)\n          </span>\n          <span v-if=\"selectedRows.length > 0\" class=\"selected-info\">\n            已选择: {{ selectedRows.length }} 条\n          </span>\n        </div>\n        <div class=\"action-buttons\">\n          <el-button\n            type=\"danger\"\n            :disabled=\"selectedRows.length === 0\"\n            @click=\"handleBatchDelete\">\n            <el-icon><Delete /></el-icon>\n            批量删除 ({{ selectedRows.length }})\n          </el-button>\n          <el-button type=\"primary\" @click=\"showAddDialog = true\">\n            <el-icon><Plus /></el-icon>\n            新增 Accession\n          </el-button>\n          <el-button @click=\"refreshData\">\n            <el-icon><Refresh /></el-icon>\n            刷新数据\n          </el-button>\n          <el-button type=\"success\" @click=\"handleUpdateData\">\n            <el-icon><Refresh /></el-icon>\n            更新数据\n          </el-button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 搜索和筛选区域 -->\n    <div class=\"filter-section\">\n      <div class=\"filter-row\">\n        <!-- Accession搜索框 -->\n        <div class=\"filter-item\">\n          <label>Accession搜索:</label>\n          <el-select\n            v-model=\"searchAccession\"\n            placeholder=\"请选择Accession\"\n            filterable\n            clearable\n            style=\"width: 250px;\"\n            @change=\"handleAccessionChange\"\n            @clear=\"handleAccessionClear\">\n            <el-option\n              v-for=\"accession in allAccessionOptions\"\n              :key=\"accession\"\n              :label=\"accession\"\n              :value=\"accession\" />\n          </el-select>\n        </div>\n\n        <!-- SubPopulation筛选 -->\n        <div class=\"filter-item\">\n          <label>SubPopulation筛选:</label>\n          <el-select\n            v-model=\"selectedSubPopulations\"\n            placeholder=\"请选择亚群\"\n            multiple\n            collapse-tags\n            collapse-tags-tooltip\n            clearable\n            style=\"width: 300px;\"\n            @change=\"handleSubPopulationFilterChange\">\n            <el-option\n              v-for=\"option in subPopulationOptions\"\n              :key=\"option.value\"\n              :label=\"option.label\"\n              :value=\"option.value\" />\n          </el-select>\n        </div>\n\n        <!-- 重置按钮 -->\n        <div class=\"filter-item\">\n          <el-button @click=\"resetFilters\">\n            <el-icon><Refresh /></el-icon>\n            重置筛选\n          </el-button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 数据表格 -->\n    <el-table\n      :data=\"tableData\"\n      v-loading=\"loading\"\n      border\n      stripe\n      style=\"width: 100%\"\n      :header-cell-style=\"{ background: '#f0f5ff', color: '#1a56db', fontWeight: 'bold' }\"\n      @selection-change=\"handleSelectionChange\">\n\n      <!-- 多选框列 -->\n      <el-table-column type=\"selection\" width=\"55\" fixed=\"left\" />\n\n      <el-table-column prop=\"accession\" label=\"Accession\" width=\"150\" fixed=\"left\" />\n      <el-table-column prop=\"subPopulation\" label=\"SubPopulation\" width=\"120\" />\n      <el-table-column prop=\"seqData\" label=\"SeqData\" width=\"200\">\n        <template #default=\"scope\">\n          <a v-if=\"scope.row.seqData && scope.row.seqData !== '-'\" \n             :href=\"scope.row.seqData\" \n             target=\"_blank\" \n             class=\"data-link\">\n            {{ scope.row.seqData }}\n          </a>\n          <span v-else class=\"data-empty\">-</span>\n        </template>\n      </el-table-column>\n      <el-table-column prop=\"longitude\" label=\"Longitude\" width=\"100\" />\n      <el-table-column prop=\"latitude\" label=\"Latitude\" width=\"100\" />\n\n      <!-- 文件显示列 -->\n      <el-table-column label=\"Genome\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.genomeFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'genome')\"\n                :title=\"scope.row.genomeFile\">\n            {{ scope.row.genomeFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"Annotation\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.annotationFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'annotation')\"\n                :title=\"scope.row.annotationFile\">\n            {{ scope.row.annotationFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"Transcriptome.all\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.transcriptomeAllFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'transcriptome.all')\"\n                :title=\"scope.row.transcriptomeAllFile\">\n            {{ scope.row.transcriptomeAllFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"Transcriptome.leaf\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.transcriptomeLeafFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'transcriptome.leaf')\"\n                :title=\"scope.row.transcriptomeLeafFile\">\n            {{ scope.row.transcriptomeLeafFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"Transcriptome.panicles\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.transcriptomePaniclesFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'transcriptome.panicles')\"\n                :title=\"scope.row.transcriptomePaniclesFile\">\n            {{ scope.row.transcriptomePaniclesFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"Transcriptome.shoot\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.transcriptomeShootFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'transcriptome.shoot')\"\n                :title=\"scope.row.transcriptomeShootFile\">\n            {{ scope.row.transcriptomeShootFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"Transcriptome.stem\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.transcriptomeStemFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'transcriptome.stem')\"\n                :title=\"scope.row.transcriptomeStemFile\">\n            {{ scope.row.transcriptomeStemFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"Transcriptome.root\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.transcriptomeRootFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'transcriptome.root')\"\n                :title=\"scope.row.transcriptomeRootFile\">\n            {{ scope.row.transcriptomeRootFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"Codon\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.codonFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'codon')\"\n                :title=\"scope.row.codonFile\">\n            {{ scope.row.codonFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"Centromere\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.centromereFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'centromere')\"\n                :title=\"scope.row.centromereFile\">\n            {{ scope.row.centromereFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"TEs\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.tesFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'TEs')\"\n                :title=\"scope.row.tesFile\">\n            {{ scope.row.tesFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"CoreBlocks\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.coreBlocksFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'coreBlocks')\"\n                :title=\"scope.row.coreBlocksFile\">\n            {{ scope.row.coreBlocksFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"miRNA\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.miRNAFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'miRNA')\"\n                :title=\"scope.row.miRNAFile\">\n            {{ scope.row.miRNAFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"tRNA\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.tRNAFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'tRNA')\"\n                :title=\"scope.row.tRNAFile\">\n            {{ scope.row.tRNAFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"rRNA\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.rRNAFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'rRNA')\"\n                :title=\"scope.row.rRNAFile\">\n            {{ scope.row.rRNAFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"操作\" width=\"150\" fixed=\"right\">\n        <template #default=\"scope\">\n          <el-button type=\"primary\" size=\"small\" @click=\"editRow(scope.row)\">编辑</el-button>\n          <el-button type=\"danger\" size=\"small\" @click=\"deleteRow(scope.row)\">删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- 分页 -->\n    <div class=\"pagination-container\">\n      <el-pagination\n        v-model:current-page=\"currentPage\"\n        v-model:page-size=\"pageSize\"\n        :page-sizes=\"[20, 50, 100]\"\n        :total=\"totalCount\"\n        layout=\"total, sizes, prev, pager, next, jumper\"\n        @size-change=\"handleSizeChange\"\n        @current-change=\"handleCurrentChange\"\n      />\n    </div>\n\n    <!-- 新增/编辑对话框 -->\n    <el-dialog\n      v-model=\"showAddDialog\"\n      :title=\"editingRow ? '编辑 Accession' : '新增 Accession'\"\n      width=\"600px\"\n      @close=\"resetForm\">\n      \n      <el-form :model=\"formData\" :rules=\"formRules\" ref=\"formRef\" label-width=\"120px\">\n        <el-form-item label=\"Accession\" prop=\"accession\">\n          <el-input v-model=\"formData.accession\" :disabled=\"editingRow\" />\n        </el-form-item>\n        \n        <el-form-item label=\"SubPopulation\" prop=\"subPopulation\">\n          <el-select\n            v-model=\"formData.subPopulation\"\n            placeholder=\"请选择或输入亚群\"\n            filterable\n            allow-create\n            default-first-option\n            :reserve-keyword=\"false\"\n            @change=\"handleSubPopulationChange\">\n            <el-option\n              v-for=\"option in subPopulationOptions\"\n              :key=\"option.value\"\n              :label=\"option.label\"\n              :value=\"option.value\" />\n          </el-select>\n        </el-form-item>\n        \n        <el-form-item label=\"SeqData URL\" prop=\"seqData\">\n          <el-input v-model=\"formData.seqData\" placeholder=\"请输入SeqData链接，留空则为 -\" />\n        </el-form-item>\n        \n        <el-form-item label=\"经度\" prop=\"longitude\">\n          <el-input-number v-model=\"formData.longitude\" :precision=\"2\" placeholder=\"经度\" />\n        </el-form-item>\n        \n        <el-form-item label=\"纬度\" prop=\"latitude\">\n          <el-input-number v-model=\"formData.latitude\" :precision=\"2\" placeholder=\"纬度\" />\n        </el-form-item>\n\n        <!-- 文件管理部分 -->\n        <el-divider content-position=\"left\">文件管理</el-divider>\n\n        <div class=\"file-management-grid\">\n          <!-- Genome -->\n          <div class=\"file-item\">\n            <label>Genome:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.genome\" class=\"current-file\">{{ formData.files.genome }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('genome')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.genome\" size=\"small\" type=\"danger\" @click=\"removeFile('genome')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- Annotation -->\n          <div class=\"file-item\">\n            <label>Annotation:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.annotation\" class=\"current-file\">{{ formData.files.annotation }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('annotation')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.annotation\" size=\"small\" type=\"danger\" @click=\"removeFile('annotation')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- Transcriptome.all -->\n          <div class=\"file-item\">\n            <label>Transcriptome.all:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.transcriptomeAll\" class=\"current-file\">{{ formData.files.transcriptomeAll }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('transcriptomeAll')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.transcriptomeAll\" size=\"small\" type=\"danger\" @click=\"removeFile('transcriptomeAll')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- Transcriptome.leaf -->\n          <div class=\"file-item\">\n            <label>Transcriptome.leaf:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.transcriptomeLeaf\" class=\"current-file\">{{ formData.files.transcriptomeLeaf }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('transcriptomeLeaf')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.transcriptomeLeaf\" size=\"small\" type=\"danger\" @click=\"removeFile('transcriptomeLeaf')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- Transcriptome.panicles -->\n          <div class=\"file-item\">\n            <label>Transcriptome.panicles:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.transcriptomePanicles\" class=\"current-file\">{{ formData.files.transcriptomePanicles }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('transcriptomePanicles')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.transcriptomePanicles\" size=\"small\" type=\"danger\" @click=\"removeFile('transcriptomePanicles')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- Transcriptome.shoot -->\n          <div class=\"file-item\">\n            <label>Transcriptome.shoot:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.transcriptomeShoot\" class=\"current-file\">{{ formData.files.transcriptomeShoot }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('transcriptomeShoot')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.transcriptomeShoot\" size=\"small\" type=\"danger\" @click=\"removeFile('transcriptomeShoot')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- Transcriptome.stem -->\n          <div class=\"file-item\">\n            <label>Transcriptome.stem:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.transcriptomeStem\" class=\"current-file\">{{ formData.files.transcriptomeStem }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('transcriptomeStem')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.transcriptomeStem\" size=\"small\" type=\"danger\" @click=\"removeFile('transcriptomeStem')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- Transcriptome.root -->\n          <div class=\"file-item\">\n            <label>Transcriptome.root:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.transcriptomeRoot\" class=\"current-file\">{{ formData.files.transcriptomeRoot }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('transcriptomeRoot')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.transcriptomeRoot\" size=\"small\" type=\"danger\" @click=\"removeFile('transcriptomeRoot')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- Codon -->\n          <div class=\"file-item\">\n            <label>Codon:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.codon\" class=\"current-file\">{{ formData.files.codon }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('codon')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.codon\" size=\"small\" type=\"danger\" @click=\"removeFile('codon')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- Centromere -->\n          <div class=\"file-item\">\n            <label>Centromere:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.centromere\" class=\"current-file\">{{ formData.files.centromere }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('centromere')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.centromere\" size=\"small\" type=\"danger\" @click=\"removeFile('centromere')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- TEs -->\n          <div class=\"file-item\">\n            <label>TEs:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.TEs\" class=\"current-file\">{{ formData.files.TEs }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('TEs')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.TEs\" size=\"small\" type=\"danger\" @click=\"removeFile('TEs')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- CoreBlocks -->\n          <div class=\"file-item\">\n            <label>CoreBlocks:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.coreBlocks\" class=\"current-file\">{{ formData.files.coreBlocks }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('coreBlocks')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.coreBlocks\" size=\"small\" type=\"danger\" @click=\"removeFile('coreBlocks')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- miRNA -->\n          <div class=\"file-item\">\n            <label>miRNA:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.miRNA\" class=\"current-file\">{{ formData.files.miRNA }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('miRNA')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.miRNA\" size=\"small\" type=\"danger\" @click=\"removeFile('miRNA')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- tRNA -->\n          <div class=\"file-item\">\n            <label>tRNA:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.tRNA\" class=\"current-file\">{{ formData.files.tRNA }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('tRNA')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.tRNA\" size=\"small\" type=\"danger\" @click=\"removeFile('tRNA')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- rRNA -->\n          <div class=\"file-item\">\n            <label>rRNA:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.rRNA\" class=\"current-file\">{{ formData.files.rRNA }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('rRNA')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.rRNA\" size=\"small\" type=\"danger\" @click=\"removeFile('rRNA')\">删除</el-button>\n            </div>\n          </div>\n        </div>\n      </el-form>\n      \n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"showAddDialog = false\">取消</el-button>\n          <el-button type=\"primary\" @click=\"saveData\" :loading=\"saving\">保存</el-button>\n        </span>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, onMounted } from 'vue'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport { Plus, Refresh, Download, Upload, Delete, Document } from '@element-plus/icons-vue'\nimport axios from 'axios'\n\nexport default {\n  name: 'AdminDataManager',\n  components: {\n    Plus,\n    Refresh,\n    Download,\n    Upload,\n    Delete,\n    Document\n  },\n  setup() {\n    const loading = ref(false)\n    const saving = ref(false)\n    const tableData = ref([])\n    const currentPage = ref(1)\n    const pageSize = ref(20)\n    const totalCount = ref(0)\n\n    const showAddDialog = ref(false)\n    const editingRow = ref(null)\n    const formRef = ref(null)\n\n    // 搜索和筛选相关\n    const searchAccession = ref('')\n    const selectedSubPopulations = ref([])\n    const allAccessionOptions = ref([])\n    const loadingAccessions = ref(false)\n\n    // 多选相关\n    const selectedRows = ref([])\n\n    const subPopulationOptions = ref([\n      { label: 'cA', value: 'cA' },\n      { label: 'cB', value: 'cB' },\n      { label: 'GJ', value: 'GJ' },\n      { label: 'XI', value: 'XI' },\n      { label: 'WILD', value: 'WILD' },\n      { label: 'O.glaberrima', value: 'O.glaberrima' },\n      { label: '未知', value: '-' }\n    ])\n    \n    const formData = reactive({\n      accession: '',\n      subPopulation: '',\n      seqData: '',\n      longitude: null,\n      latitude: null,\n      files: {\n        genome: '',\n        annotation: '',\n        transcriptomeAll: '',\n        transcriptomeLeaf: '',\n        transcriptomePanicles: '',\n        transcriptomeShoot: '',\n        transcriptomeStem: '',\n        transcriptomeRoot: '',\n        codon: '',\n        centromere: '',\n        TEs: '',\n        coreBlocks: '',\n        miRNA: '',\n        tRNA: '',\n        rRNA: ''\n      },\n      filesToUpload: {} // 存储待上传的文件\n    })\n    \n    const formRules = {\n      accession: [\n        { required: true, message: '请输入Accession', trigger: 'blur' }\n      ]\n    }\n\n    // 加载亚群选项\n    const loadSubPopulationOptions = async () => {\n      try {\n        const response = await axios.get('/admin/data-management/list/', {\n          params: { page: 1, page_size: 1000 } // 获取所有数据来提取亚群\n        })\n\n        if (response.data.success && response.data.data) {\n          // 提取所有唯一的亚群\n          const existingSubPopulations = new Set()\n          response.data.data.forEach(item => {\n            if (item.subPopulation && item.subPopulation !== '-') {\n              existingSubPopulations.add(item.subPopulation)\n            }\n          })\n\n          // 合并默认选项和已存在的亚群\n          const defaultOptions = [\n            { label: 'cA', value: 'cA' },\n            { label: 'cB', value: 'cB' },\n            { label: 'GJ', value: 'GJ' },\n            { label: 'XI', value: 'XI' },\n            { label: 'WILD', value: 'WILD' },\n            { label: 'O.glaberrima', value: 'O.glaberrima' },\n            { label: '未知', value: '-' }\n          ]\n\n          const defaultValues = new Set(defaultOptions.map(opt => opt.value))\n          const additionalOptions = Array.from(existingSubPopulations)\n            .filter(value => !defaultValues.has(value))\n            .map(value => ({ label: value, value: value }))\n\n          subPopulationOptions.value = [...defaultOptions, ...additionalOptions]\n        }\n      } catch (error) {\n        console.error('加载亚群选项失败:', error)\n      }\n    }\n\n    // 加载数据\n    const loadData = async () => {\n      try {\n        loading.value = true\n        const params = {\n          page: currentPage.value,\n          page_size: pageSize.value\n        }\n\n        // 添加搜索参数\n        if (searchAccession.value) {\n          params.search = searchAccession.value\n        }\n\n        // 添加亚群筛选参数\n        if (selectedSubPopulations.value.length > 0) {\n          params.sub_populations = selectedSubPopulations.value.join(',')\n        }\n\n        const response = await axios.get('/admin/data-management/list/', { params })\n        const data = response.data\n\n        if (data.success) {\n          tableData.value = data.data || []\n          totalCount.value = data.total || 0\n\n          // 调试信息：检查第一条记录的文件状态\n          if (data.data && data.data.length > 0) {\n            console.log('第一条记录的文件状态:', data.data[0])\n          }\n        } else {\n          throw new Error(data.message || '获取数据失败')\n        }\n\n      } catch (error) {\n        console.error('加载数据失败:', error)\n        ElMessage.error('加载数据失败')\n      } finally {\n        loading.value = false\n      }\n    }\n\n    // 刷新数据\n    const refreshData = () => {\n      loadData()\n    }\n\n    // 处理亚群变化（表单中的）\n    const handleSubPopulationChange = (value) => {\n      // 如果是新输入的亚群，添加到选项列表中\n      if (value && !subPopulationOptions.value.find(opt => opt.value === value)) {\n        subPopulationOptions.value.push({\n          label: value,\n          value: value\n        })\n      }\n    }\n\n    // 加载所有Accession选项\n    const loadAllAccessions = async () => {\n      try {\n        loadingAccessions.value = true\n        const response = await axios.get('/admin/data-management/list/', {\n          params: {\n            page: 1,\n            page_size: 1000  // 获取所有数据\n          }\n        })\n\n        if (response.data.success) {\n          allAccessionOptions.value = response.data.data.map(item => item.accession).sort()\n        }\n      } catch (error) {\n        console.error('加载Accession选项失败:', error)\n      } finally {\n        loadingAccessions.value = false\n      }\n    }\n\n    // 处理Accession选择变化\n    const handleAccessionChange = (value) => {\n      searchAccession.value = value\n      currentPage.value = 1\n      loadData()\n    }\n\n    // 清除Accession搜索\n    const handleAccessionClear = () => {\n      searchAccession.value = ''\n      currentPage.value = 1\n      loadData()\n    }\n\n    // 处理SubPopulation筛选变化\n    const handleSubPopulationFilterChange = () => {\n      currentPage.value = 1\n      loadData()\n    }\n\n    // 重置筛选\n    const resetFilters = () => {\n      searchAccession.value = ''\n      selectedSubPopulations.value = []\n      currentPage.value = 1\n      loadData()\n    }\n\n    // 处理表格选择变化\n    const handleSelectionChange = (selection) => {\n      selectedRows.value = selection\n    }\n\n    // 批量删除\n    const handleBatchDelete = async () => {\n      if (selectedRows.value.length === 0) {\n        ElMessage.warning('请先选择要删除的记录')\n        return\n      }\n\n      try {\n        const accessions = selectedRows.value.map(row => row.accession)\n        const message = `确定要删除选中的 ${accessions.length} 个 Accession 吗？\\n\\n${accessions.join(', ')}\\n\\n此操作将同时删除相关的所有数据文件，且无法恢复！`\n\n        await ElMessageBox.confirm(message, '批量删除确认', {\n          confirmButtonText: '确定删除',\n          cancelButtonText: '取消',\n          type: 'warning',\n          dangerouslyUseHTMLString: false\n        })\n\n        // 调用批量删除API\n        const response = await axios.post('/admin/data-management/batch-delete/', {\n          accessions: accessions\n        })\n\n        if (response.data.success) {\n          ElMessage.success(`成功删除 ${accessions.length} 个 Accession`)\n          selectedRows.value = [] // 清空选择\n          loadData()\n          loadAllAccessions() // 重新加载Accession选项\n        } else {\n          ElMessage.error(response.data.message || '批量删除失败')\n        }\n\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('批量删除失败:', error)\n          ElMessage.error('批量删除失败')\n        }\n      }\n    }\n\n    // 更新数据（重新扫描文件）\n    const handleUpdateData = async () => {\n      try {\n        loading.value = true\n        const response = await axios.post('/admin/rescan/')\n\n        if (response.data.success) {\n          ElMessage.success(response.data.message)\n          loadData()\n        } else {\n          ElMessage.error(response.data.message)\n        }\n      } catch (error) {\n        console.error('更新数据失败:', error)\n        ElMessage.error('更新数据失败')\n      } finally {\n        loading.value = false\n      }\n    }\n\n    // 分页处理\n    const handleSizeChange = (size) => {\n      pageSize.value = size\n      currentPage.value = 1\n      loadData()\n    }\n\n    const handleCurrentChange = (page) => {\n      currentPage.value = page\n      loadData()\n    }\n\n    // 编辑行\n    const editRow = (row) => {\n      editingRow.value = row\n      formData.accession = row.accession\n      formData.subPopulation = row.subPopulation || ''\n      formData.seqData = row.seqData === '-' ? '' : (row.seqData || '')\n      formData.longitude = row.longitude\n      formData.latitude = row.latitude\n\n      // 加载文件信息\n      formData.files.genome = row.genomeFile || ''\n      formData.files.annotation = row.annotationFile || ''\n      formData.files.transcriptomeAll = row.transcriptomeAllFile || ''\n      formData.files.transcriptomeLeaf = row.transcriptomeLeafFile || ''\n      formData.files.transcriptomePanicles = row.transcriptomePaniclesFile || ''\n      formData.files.transcriptomeShoot = row.transcriptomeShootFile || ''\n      formData.files.transcriptomeStem = row.transcriptomeStemFile || ''\n      formData.files.transcriptomeRoot = row.transcriptomeRootFile || ''\n      formData.files.codon = row.codonFile || ''\n      formData.files.centromere = row.centromereFile || ''\n      formData.files.TEs = row.tesFile || ''\n      formData.files.coreBlocks = row.coreBlocksFile || ''\n      formData.files.miRNA = row.miRNAFile || ''\n      formData.files.tRNA = row.tRNAFile || ''\n      formData.files.rRNA = row.rRNAFile || ''\n\n      formData.filesToUpload = {} // 清空待上传文件\n      showAddDialog.value = true\n    }\n\n    // 删除行\n    const deleteRow = async (row) => {\n      try {\n        await ElMessageBox.confirm(\n          `确定要删除 Accession \"${row.accession}\" 吗？这将删除该条目的所有相关数据。`,\n          '确认删除',\n          {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }\n        )\n        \n        // 调用删除API\n        await axios.delete(`/admin/data-management/accession/${row.accession}/delete/`)\n        \n        ElMessage.success('删除成功')\n        loadData()\n        loadAllAccessions() // 重新加载Accession选项\n        \n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('删除失败:', error)\n          ElMessage.error('删除失败')\n        }\n      }\n    }\n\n    // 保存数据\n    const saveData = async () => {\n      try {\n        await formRef.value.validate()\n\n        saving.value = true\n\n        const data = {\n          accession: formData.accession,\n          subPopulation: formData.subPopulation || '-',\n          seqData: formData.seqData || '-',\n          longitude: formData.longitude,\n          latitude: formData.latitude\n        }\n\n        // 先保存基本信息\n        if (editingRow.value) {\n          // 编辑\n          await axios.put(`/admin/data-management/accession/${editingRow.value.accession}/update/`, data)\n        } else {\n          // 新增\n          await axios.post('/admin/data-management/accession/', data)\n        }\n\n        // 处理文件上传\n        const accession = formData.accession\n\n        // 文件类型映射：前端字段名 -> API参数\n        const fileTypeMapping = {\n          'genome': 'genome',\n          'annotation': 'annotation',\n          'transcriptomeAll': 'transcriptome.all',\n          'transcriptomeLeaf': 'transcriptome.leaf',\n          'transcriptomePanicles': 'transcriptome.panicles',\n          'transcriptomeShoot': 'transcriptome.shoot',\n          'transcriptomeStem': 'transcriptome.stem',\n          'transcriptomeRoot': 'transcriptome.root',\n          'codon': 'codon',\n          'centromere': 'centromere',\n          'TEs': 'TEs',\n          'coreBlocks': 'coreBlocks',\n          'miRNA': 'miRNA',\n          'tRNA': 'tRNA',\n          'rRNA': 'rRNA'\n        }\n\n        for (const [frontendFileType, file] of Object.entries(formData.filesToUpload)) {\n          if (file) {\n            try {\n              const apiFileType = fileTypeMapping[frontendFileType] || frontendFileType\n              const fileFormData = new FormData()\n              fileFormData.append('file', file)\n              fileFormData.append('accession', accession)\n              fileFormData.append('fileType', apiFileType)\n\n              await axios.post('/admin/data-management/upload-file/', fileFormData, {\n                headers: {\n                  'Content-Type': 'multipart/form-data'\n                }\n              })\n            } catch (fileError) {\n              console.error(`文件 ${frontendFileType} 上传失败:`, fileError)\n              ElMessage.warning(`文件 ${frontendFileType} 上传失败`)\n            }\n          }\n        }\n\n        // 处理文件删除（如果文件名被清空但原来有文件）\n        if (editingRow.value) {\n          const fileTypeMappings = [\n            { formKey: 'genome', apiKey: 'genome', originalKey: 'genomeFile' },\n            { formKey: 'annotation', apiKey: 'annotation', originalKey: 'annotationFile' },\n            { formKey: 'transcriptomeAll', apiKey: 'transcriptome.all', originalKey: 'transcriptomeAllFile' },\n            { formKey: 'transcriptomeLeaf', apiKey: 'transcriptome.leaf', originalKey: 'transcriptomeLeafFile' },\n            { formKey: 'transcriptomePanicles', apiKey: 'transcriptome.panicles', originalKey: 'transcriptomePaniclesFile' },\n            { formKey: 'transcriptomeShoot', apiKey: 'transcriptome.shoot', originalKey: 'transcriptomeShootFile' },\n            { formKey: 'transcriptomeStem', apiKey: 'transcriptome.stem', originalKey: 'transcriptomeStemFile' },\n            { formKey: 'transcriptomeRoot', apiKey: 'transcriptome.root', originalKey: 'transcriptomeRootFile' },\n            { formKey: 'codon', apiKey: 'codon', originalKey: 'codonFile' },\n            { formKey: 'centromere', apiKey: 'centromere', originalKey: 'centromereFile' },\n            { formKey: 'TEs', apiKey: 'TEs', originalKey: 'tesFile' },\n            { formKey: 'coreBlocks', apiKey: 'coreBlocks', originalKey: 'coreBlocksFile' },\n            { formKey: 'miRNA', apiKey: 'miRNA', originalKey: 'miRNAFile' },\n            { formKey: 'tRNA', apiKey: 'tRNA', originalKey: 'tRNAFile' },\n            { formKey: 'rRNA', apiKey: 'rRNA', originalKey: 'rRNAFile' }\n          ]\n\n          for (const mapping of fileTypeMappings) {\n            const originalFile = editingRow.value[mapping.originalKey]\n            const currentFile = formData.files[mapping.formKey]\n\n            // 如果原来有文件，现在没有，且没有新上传的文件，则删除\n            if (originalFile && !currentFile && !formData.filesToUpload[mapping.formKey]) {\n              try {\n                await axios.delete(`/admin/data-management/delete-file/${accession}/${mapping.apiKey}/`)\n              } catch (deleteError) {\n                console.error(`文件 ${mapping.apiKey} 删除失败:`, deleteError)\n              }\n            }\n          }\n        }\n\n        ElMessage.success(editingRow.value ? '更新成功' : '新增成功')\n        showAddDialog.value = false\n        loadData()\n        loadSubPopulationOptions() // 重新加载亚群选项\n        loadAllAccessions() // 重新加载Accession选项\n\n      } catch (error) {\n        console.error('保存失败:', error)\n        ElMessage.error('保存失败')\n      } finally {\n        saving.value = false\n      }\n    }\n\n    // 重置表单\n    const resetForm = () => {\n      editingRow.value = null\n      formData.accession = ''\n      formData.subPopulation = ''\n      formData.seqData = ''\n      formData.longitude = null\n      formData.latitude = null\n\n      // 重置文件信息\n      formData.files = {\n        genome: '',\n        annotation: '',\n        transcriptomeAll: '',\n        transcriptomeLeaf: '',\n        transcriptomePanicles: '',\n        transcriptomeShoot: '',\n        transcriptomeStem: '',\n        transcriptomeRoot: '',\n        codon: '',\n        centromere: '',\n        TEs: '',\n        coreBlocks: '',\n        miRNA: '',\n        tRNA: '',\n        rRNA: ''\n      }\n      formData.filesToUpload = {}\n\n      if (formRef.value) {\n        formRef.value.clearValidate()\n      }\n    }\n\n    // 文件选择方法（用于编辑对话框）\n    const selectFile = (fileType) => {\n      const input = document.createElement('input')\n      input.type = 'file'\n\n      // 根据文件类型设置接受的文件格式\n      const fileExtensions = {\n        'genome': '.fasta,.fa,.fas',\n        'annotation': '.gff,.gff3',\n        'transcriptomeAll': '.tar.gz',\n        'transcriptomeLeaf': '.tar.gz',\n        'transcriptomePanicles': '.tar.gz',\n        'transcriptomeShoot': '.tar.gz',\n        'transcriptomeStem': '.tar.gz',\n        'transcriptomeRoot': '.tar.gz',\n        'codon': '.tar.gz',\n        'centromere': '.bed',\n        'TEs': '.tar.gz',\n        'coreBlocks': '.bed',\n        'miRNA': '.bed',\n        'tRNA': '.bed',\n        'rRNA': '.bed'\n      }\n\n      input.accept = fileExtensions[fileType] || '*'\n\n      input.onchange = (event) => {\n        const file = event.target.files[0]\n        if (file) {\n          formData.files[fileType] = file.name\n          formData.filesToUpload[fileType] = file\n        }\n      }\n\n      input.click()\n    }\n\n    // 移除文件方法（用于编辑对话框）\n    const removeFile = (fileType) => {\n      formData.files[fileType] = ''\n      delete formData.filesToUpload[fileType]\n    }\n\n    // 文件操作方法（用于表格中的下载）\n    const uploadFile = (accession, fileType) => {\n      // 创建文件输入元素\n      const input = document.createElement('input')\n      input.type = 'file'\n\n      // 根据文件类型设置接受的文件格式\n      const fileExtensions = {\n        'genome': '.fasta,.fa,.fas',\n        'annotation': '.gff,.gff3',\n        'transcriptome.all': '.tar.gz',\n        'transcriptome.leaf': '.tar.gz',\n        'transcriptome.panicles': '.tar.gz',\n        'transcriptome.shoot': '.tar.gz',\n        'transcriptome.stem': '.tar.gz',\n        'transcriptome.root': '.tar.gz',\n        'codon': '.tar.gz',\n        'centromere': '.bed',\n        'TEs': '.tar.gz',\n        'coreBlocks': '.bed',\n        'miRNA': '.bed',\n        'tRNA': '.bed',\n        'rRNA': '.bed'\n      }\n\n      input.accept = fileExtensions[fileType] || '*'\n\n      input.onchange = async (event) => {\n        const file = event.target.files[0]\n        if (!file) return\n\n        try {\n          const formData = new FormData()\n          formData.append('file', file)\n          formData.append('accession', accession)\n          formData.append('fileType', fileType)\n\n          const response = await axios.post('/admin/data-management/upload-file/', formData, {\n            headers: {\n              'Content-Type': 'multipart/form-data'\n            }\n          })\n\n          if (response.data.success) {\n            ElMessage.success(`${fileType} 文件上传成功`)\n            loadData() // 刷新数据\n          } else {\n            ElMessage.error(response.data.message || '上传失败')\n          }\n        } catch (error) {\n          console.error('文件上传失败:', error)\n          ElMessage.error('文件上传失败')\n        }\n      }\n\n      input.click()\n    }\n\n    const downloadFile = async (accession, fileType) => {\n      try {\n        const response = await axios.get(`/admin/data-management/download-file/${accession}/${fileType}/`, {\n          responseType: 'blob'\n        })\n\n        // 创建下载链接\n        const url = window.URL.createObjectURL(new Blob([response.data]))\n        const link = document.createElement('a')\n        link.href = url\n\n        // 根据文件类型设置文件名\n        const extensions = {\n          'genome': 'fasta',\n          'annotation': 'gff',\n          'transcriptome': 'tar.gz',\n          'codon': 'tar.gz',\n          'centromere': 'bed',\n          'TEs': 'tar.gz',\n          'coreBlocks': 'bed',\n          'miRNA': 'bed',\n          'tRNA': 'bed',\n          'rRNA': 'bed'\n        }\n\n        link.download = `${fileType}.${accession}.${extensions[fileType]}`\n        document.body.appendChild(link)\n        link.click()\n        document.body.removeChild(link)\n        window.URL.revokeObjectURL(url)\n\n      } catch (error) {\n        console.error('文件下载失败:', error)\n        ElMessage.error('文件下载失败')\n      }\n    }\n\n    const deleteFile = async (accession, fileType) => {\n      try {\n        await ElMessageBox.confirm(\n          `确定要删除 ${accession} 的 ${fileType} 文件吗？`,\n          '确认删除',\n          {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }\n        )\n\n        const response = await axios.delete(`/admin/data-management/delete-file/${accession}/${fileType}/`)\n\n        if (response.data.success) {\n          ElMessage.success(`${fileType} 文件删除成功`)\n          loadData() // 刷新数据\n        } else {\n          ElMessage.error(response.data.message || '删除失败')\n        }\n\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('文件删除失败:', error)\n          ElMessage.error('文件删除失败')\n        }\n      }\n    }\n\n    onMounted(() => {\n      loadData()\n      loadSubPopulationOptions()\n      loadAllAccessions()\n    })\n\n    return {\n      loading,\n      saving,\n      tableData,\n      currentPage,\n      pageSize,\n      totalCount,\n      showAddDialog,\n      editingRow,\n      formRef,\n      formData,\n      formRules,\n      subPopulationOptions,\n      // 搜索和筛选相关\n      searchAccession,\n      selectedSubPopulations,\n      allAccessionOptions,\n      loadingAccessions,\n      loadAllAccessions,\n      handleAccessionChange,\n      handleAccessionClear,\n      handleSubPopulationFilterChange,\n      resetFilters,\n      // 原有方法\n      loadData,\n      refreshData,\n      handleUpdateData,\n      handleSizeChange,\n      handleCurrentChange,\n      editRow,\n      deleteRow,\n      saveData,\n      resetForm,\n      selectFile,\n      removeFile,\n      uploadFile,\n      downloadFile,\n      deleteFile\n    }\n  }\n}\n</script>\n\n<style scoped>\n.admin-data-manager {\n  padding: 20px;\n}\n\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.page-header h2 {\n  margin: 0;\n  color: #1a56db;\n}\n\n.header-actions {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.stats-info {\n  color: #666;\n  font-size: 14px;\n}\n\n.stats-info span {\n  margin-right: 10px;\n}\n\n/* 筛选区域样式 */\n.filter-section {\n  background: #f8f9fa;\n  padding: 20px;\n  border-radius: 8px;\n  margin-bottom: 20px;\n  border: 1px solid #e9ecef;\n}\n\n.filter-row {\n  display: flex;\n  align-items: center;\n  gap: 20px;\n  flex-wrap: wrap;\n}\n\n.filter-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.filter-item label {\n  font-weight: 500;\n  color: #495057;\n  white-space: nowrap;\n  min-width: fit-content;\n}\n\n.file-link {\n  color: #1a56db;\n  cursor: pointer;\n  text-decoration: none;\n  word-break: break-all;\n  font-size: 12px;\n}\n\n.file-link:hover {\n  text-decoration: underline;\n}\n\n.file-management-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 15px;\n  margin-top: 10px;\n}\n\n.file-item {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.file-item label {\n  font-weight: bold;\n  color: #333;\n  font-size: 14px;\n}\n\n.file-controls {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  flex-wrap: wrap;\n}\n\n.current-file {\n  color: #67c23a;\n  font-size: 12px;\n  word-break: break-all;\n  flex: 1;\n  min-width: 0;\n}\n\n.no-file {\n  color: #999;\n  font-size: 12px;\n}\n\n.data-link {\n  color: #1a56db;\n  text-decoration: none;\n  word-break: break-all;\n}\n\n.data-link:hover {\n  text-decoration: underline;\n}\n\n.data-empty {\n  color: #999;\n}\n\n.pagination-container {\n  margin-top: 20px;\n  display: flex;\n  justify-content: center;\n}\n\n.dialog-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 10px;\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAa;;EAEjBA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAY;;;;;;EAKgBA,KAAK,EAAC;;;EAIxCA,KAAK,EAAC;AAAgB;;EAyB1BA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAa;;EAmBnBA,KAAK,EAAC;AAAa;;EAoBnBA,KAAK,EAAC;AAAa;;;;EAgCTA,KAAK,EAAC;;;;;EAeNA,KAAK,EAAC;;;;;EAYNA,KAAK,EAAC;;;;;EAYNA,KAAK,EAAC;;;;;EAYNA,KAAK,EAAC;;;;;EAYNA,KAAK,EAAC;;;;;EAYNA,KAAK,EAAC;;;;;EAYNA,KAAK,EAAC;;;;;EAYNA,KAAK,EAAC;;;;;EAYNA,KAAK,EAAC;;;;;EAYNA,KAAK,EAAC;;;;;EAYNA,KAAK,EAAC;;;;;EAYNA,KAAK,EAAC;;;;;EAYNA,KAAK,EAAC;;;;;EAYNA,KAAK,EAAC;;;;;EAYNA,KAAK,EAAC;;;EAapBA,KAAK,EAAC;AAAsB;;EAwDxBA,KAAK,EAAC;AAAsB;;EAE1BA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAe;;;EACWA,KAAK,EAAC;;;;EAC5BA,KAAK,EAAC;;;EAOlBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAe;;;EACeA,KAAK,EAAC;;;;EAChCA,KAAK,EAAC;;;EAOlBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAe;;;EACqBA,KAAK,EAAC;;;;EACtCA,KAAK,EAAC;;;EAOlBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAe;;;EACsBA,KAAK,EAAC;;;;EACvCA,KAAK,EAAC;;;EAOlBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAe;;;EAC0BA,KAAK,EAAC;;;;EAC3CA,KAAK,EAAC;;;EAOlBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAe;;;EACuBA,KAAK,EAAC;;;;EACxCA,KAAK,EAAC;;;EAOlBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAe;;;EACsBA,KAAK,EAAC;;;;EACvCA,KAAK,EAAC;;;EAOlBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAe;;;EACsBA,KAAK,EAAC;;;;EACvCA,KAAK,EAAC;;;EAOlBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAe;;;EACUA,KAAK,EAAC;;;;EAC3BA,KAAK,EAAC;;;EAOlBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAe;;;EACeA,KAAK,EAAC;;;;EAChCA,KAAK,EAAC;;;EAOlBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAe;;;EACQA,KAAK,EAAC;;;;EACzBA,KAAK,EAAC;;;EAOlBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAe;;;EACeA,KAAK,EAAC;;;;EAChCA,KAAK,EAAC;;;EAOlBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAe;;;EACUA,KAAK,EAAC;;;;EAC3BA,KAAK,EAAC;;;EAOlBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAe;;;EACSA,KAAK,EAAC;;;;EAC1BA,KAAK,EAAC;;;EAOlBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAe;;;EACSA,KAAK,EAAC;;;;EAC1BA,KAAK,EAAC;;;EASnBA,KAAK,EAAC;AAAe;;;;;;;;;;;;;;;;;;;uBArhBjCC,mBAAA,CA2hBM,OA3hBNC,UA2hBM,GA1hBJC,mBAAA,CAkCM,OAlCNC,UAkCM,G,4BAjCJD,mBAAA,CAAe,YAAX,QAAM,qBACVA,mBAAA,CA+BM,OA/BNE,UA+BM,GA9BJF,mBAAA,CAQM,OARNG,UAQM,GAPJH,mBAAA,CAAqC,cAA/B,MAAI,GAAAI,gBAAA,CAAGC,MAAA,CAAAC,UAAU,IAAG,MAAI,iBAClBD,MAAA,CAAAE,SAAS,CAACC,MAAM,Q,cAA5BV,mBAAA,CAEO,QAAAW,UAAA,EAF2B,SAC1B,GAAAL,gBAAA,CAAGC,MAAA,CAAAE,SAAS,CAACC,MAAM,IAAG,MAC9B,mB,mCACYE,IAAA,CAAAC,YAAY,CAACH,MAAM,Q,cAA/BV,mBAAA,CAEO,QAFPc,UAEO,EAFoD,QACpD,GAAAR,gBAAA,CAAGM,IAAA,CAAAC,YAAY,CAACH,MAAM,IAAG,KAChC,mB,qCAEFR,mBAAA,CAoBM,OApBNa,UAoBM,GAnBJC,YAAA,CAMYC,oBAAA;IALVC,IAAI,EAAC,QAAQ;IACZC,QAAQ,EAAEP,IAAA,CAAAC,YAAY,CAACH,MAAM;IAC7BU,OAAK,EAAER,IAAA,CAAAS;;sBACR,MAA6B,CAA7BL,YAAA,CAA6BM,kBAAA;wBAApB,MAAU,CAAVN,YAAA,CAAUO,iBAAA,E;;yBAAU,SACvB,GAAAjB,gBAAA,CAAGM,IAAA,CAAAC,YAAY,CAACH,MAAM,IAAG,IACjC,gB;;8CACAM,YAAA,CAGYC,oBAAA;IAHDC,IAAI,EAAC,SAAS;IAAEE,OAAK,EAAAI,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAElB,MAAA,CAAAmB,aAAa;;sBAC7C,MAA2B,CAA3BV,YAAA,CAA2BM,kBAAA;wBAAlB,MAAQ,CAARN,YAAA,CAAQW,eAAA,E;;qDAAU,gBAE7B,G;;;MACAX,YAAA,CAGYC,oBAAA;IAHAG,OAAK,EAAEb,MAAA,CAAAqB;EAAW;sBAC5B,MAA8B,CAA9BZ,YAAA,CAA8BM,kBAAA;wBAArB,MAAW,CAAXN,YAAA,CAAWa,kBAAA,E;;qDAAU,QAEhC,G;;;kCACAb,YAAA,CAGYC,oBAAA;IAHDC,IAAI,EAAC,SAAS;IAAEE,OAAK,EAAEb,MAAA,CAAAuB;;sBAChC,MAA8B,CAA9Bd,YAAA,CAA8BM,kBAAA;wBAArB,MAAW,CAAXN,YAAA,CAAWa,kBAAA,E;;qDAAU,QAEhC,G;;;wCAKNE,mBAAA,aAAgB,EAChB7B,mBAAA,CAiDM,OAjDN8B,UAiDM,GAhDJ9B,mBAAA,CA+CM,OA/CN+B,UA+CM,GA9CJF,mBAAA,kBAAqB,EACrB7B,mBAAA,CAgBM,OAhBNgC,WAgBM,G,4BAfJhC,mBAAA,CAA2B,eAApB,cAAY,qBACnBc,YAAA,CAaYmB,oBAAA;gBAZD5B,MAAA,CAAA6B,eAAe;+DAAf7B,MAAA,CAAA6B,eAAe,GAAAX,MAAA;IACxBY,WAAW,EAAC,cAAc;IAC1BC,UAAU,EAAV,EAAU;IACVC,SAAS,EAAT,EAAS;IACTC,KAAqB,EAArB;MAAA;IAAA,CAAqB;IACpBC,QAAM,EAAElC,MAAA,CAAAmC,qBAAqB;IAC7BC,OAAK,EAAEpC,MAAA,CAAAqC;;sBAEN,MAAwC,E,kBAD1C5C,mBAAA,CAIuB6C,SAAA,QAAAC,WAAA,CAHDvC,MAAA,CAAAwC,mBAAmB,EAAhCC,SAAS;2BADlBC,YAAA,CAIuBC,oBAAA;QAFpBC,GAAG,EAAEH,SAAS;QACdI,KAAK,EAAEJ,SAAS;QAChBK,KAAK,EAAEL;;;;8DAIdjB,mBAAA,qBAAwB,EACxB7B,mBAAA,CAiBM,OAjBNoD,WAiBM,G,4BAhBJpD,mBAAA,CAA+B,eAAxB,kBAAgB,qBACvBc,YAAA,CAcYmB,oBAAA;gBAbD5B,MAAA,CAAAgD,sBAAsB;+DAAtBhD,MAAA,CAAAgD,sBAAsB,GAAA9B,MAAA;IAC/BY,WAAW,EAAC,OAAO;IACnBmB,QAAQ,EAAR,EAAQ;IACR,eAAa,EAAb,EAAa;IACb,uBAAqB,EAArB,EAAqB;IACrBjB,SAAS,EAAT,EAAS;IACTC,KAAqB,EAArB;MAAA;IAAA,CAAqB;IACpBC,QAAM,EAAElC,MAAA,CAAAkD;;sBAEP,MAAsC,E,kBADxCzD,mBAAA,CAI0B6C,SAAA,QAAAC,WAAA,CAHPvC,MAAA,CAAAmD,oBAAoB,EAA9BC,MAAM;2BADfV,YAAA,CAI0BC,oBAAA;QAFvBC,GAAG,EAAEQ,MAAM,CAACN,KAAK;QACjBD,KAAK,EAAEO,MAAM,CAACP,KAAK;QACnBC,KAAK,EAAEM,MAAM,CAACN;;;;mDAIrBtB,mBAAA,UAAa,EACb7B,mBAAA,CAKM,OALN0D,WAKM,GAJJ5C,YAAA,CAGYC,oBAAA;IAHAG,OAAK,EAAEb,MAAA,CAAAsD;EAAY;sBAC7B,MAA8B,CAA9B7C,YAAA,CAA8BM,kBAAA;wBAArB,MAAW,CAAXN,YAAA,CAAWa,kBAAA,E;;qDAAU,QAEhC,G;;;wCAKNE,mBAAA,UAAa,E,+BACbkB,YAAA,CAuNWa,mBAAA;IAtNRC,IAAI,EAAExD,MAAA,CAAAE,SAAS;IAEhBuD,MAAM,EAAN,EAAM;IACNC,MAAM,EAAN,EAAM;IACNzB,KAAmB,EAAnB;MAAA;IAAA,CAAmB;IAClB,mBAAiB,EAAE;MAAA0B,UAAA;MAAAC,KAAA;MAAAC,UAAA;IAAA,CAA+D;IAClFC,iBAAgB,EAAEzD,IAAA,CAAA0D;;sBAEnB,MAAa,CAAbvC,mBAAA,UAAa,EACbf,YAAA,CAA4DuD,0BAAA;MAA3CrD,IAAI,EAAC,WAAW;MAACsD,KAAK,EAAC,IAAI;MAACC,KAAK,EAAC;QAEnDzD,YAAA,CAA+EuD,0BAAA;MAA9DG,IAAI,EAAC,WAAW;MAACtB,KAAK,EAAC,WAAW;MAACoB,KAAK,EAAC,KAAK;MAACC,KAAK,EAAC;QACtEzD,YAAA,CAA0EuD,0BAAA;MAAzDG,IAAI,EAAC,eAAe;MAACtB,KAAK,EAAC,eAAe;MAACoB,KAAK,EAAC;QAClExD,YAAA,CAUkBuD,0BAAA;MAVDG,IAAI,EAAC,SAAS;MAACtB,KAAK,EAAC,SAAS;MAACoB,KAAK,EAAC;;MACzCG,OAAO,EAAAC,QAAA,CAMZC,KANmB,KACdA,KAAK,CAACC,GAAG,CAACC,OAAO,IAAIF,KAAK,CAACC,GAAG,CAACC,OAAO,Y,cAA/C/E,mBAAA,CAKI;;QAJAgF,IAAI,EAAEH,KAAK,CAACC,GAAG,CAACC,OAAO;QACxBE,MAAM,EAAC,QAAQ;QACflF,KAAK,EAAC;0BACJ8E,KAAK,CAACC,GAAG,CAACC,OAAO,wBAAAG,WAAA,M,cAEtBlF,mBAAA,CAAwC,QAAxCmF,WAAwC,EAAR,GAAC,G;;QAGrCnE,YAAA,CAAkEuD,0BAAA;MAAjDG,IAAI,EAAC,WAAW;MAACtB,KAAK,EAAC,WAAW;MAACoB,KAAK,EAAC;QAC1DxD,YAAA,CAAgEuD,0BAAA;MAA/CG,IAAI,EAAC,UAAU;MAACtB,KAAK,EAAC,UAAU;MAACoB,KAAK,EAAC;QAExDzC,mBAAA,WAAc,EACdf,YAAA,CAUkBuD,0BAAA;MAVDnB,KAAK,EAAC,QAAQ;MAACoB,KAAK,EAAC;;MACzBG,OAAO,EAAAC,QAAA,CAMTC,KANgB,KACXA,KAAK,CAACC,GAAG,CAACM,UAAU,I,cAAhCpF,mBAAA,CAKO;;QAJDD,KAAK,EAAC,WAAW;QAChBqB,OAAK,EAAAK,MAAA,IAAElB,MAAA,CAAA8E,YAAY,CAACR,KAAK,CAACC,GAAG,CAAC9B,SAAS;QACvCsC,KAAK,EAAET,KAAK,CAACC,GAAG,CAACM;0BACnBP,KAAK,CAACC,GAAG,CAACM,UAAU,wBAAAG,WAAA,M,cAEzBvF,mBAAA,CAAwC,QAAxCwF,WAAwC,EAAR,GAAC,G;;QAIrCxE,YAAA,CAUkBuD,0BAAA;MAVDnB,KAAK,EAAC,YAAY;MAACoB,KAAK,EAAC;;MAC7BG,OAAO,EAAAC,QAAA,CAMTC,KANgB,KACXA,KAAK,CAACC,GAAG,CAACW,cAAc,I,cAApCzF,mBAAA,CAKO;;QAJDD,KAAK,EAAC,WAAW;QAChBqB,OAAK,EAAAK,MAAA,IAAElB,MAAA,CAAA8E,YAAY,CAACR,KAAK,CAACC,GAAG,CAAC9B,SAAS;QACvCsC,KAAK,EAAET,KAAK,CAACC,GAAG,CAACW;0BACnBZ,KAAK,CAACC,GAAG,CAACW,cAAc,wBAAAC,WAAA,M,cAE7B1F,mBAAA,CAAwC,QAAxC2F,WAAwC,EAAR,GAAC,G;;QAIrC3E,YAAA,CAUkBuD,0BAAA;MAVDnB,KAAK,EAAC,mBAAmB;MAACoB,KAAK,EAAC;;MACpCG,OAAO,EAAAC,QAAA,CAMTC,KANgB,KACXA,KAAK,CAACC,GAAG,CAACc,oBAAoB,I,cAA1C5F,mBAAA,CAKO;;QAJDD,KAAK,EAAC,WAAW;QAChBqB,OAAK,EAAAK,MAAA,IAAElB,MAAA,CAAA8E,YAAY,CAACR,KAAK,CAACC,GAAG,CAAC9B,SAAS;QACvCsC,KAAK,EAAET,KAAK,CAACC,GAAG,CAACc;0BACnBf,KAAK,CAACC,GAAG,CAACc,oBAAoB,wBAAAC,WAAA,M,cAEnC7F,mBAAA,CAAwC,QAAxC8F,WAAwC,EAAR,GAAC,G;;QAIrC9E,YAAA,CAUkBuD,0BAAA;MAVDnB,KAAK,EAAC,oBAAoB;MAACoB,KAAK,EAAC;;MACrCG,OAAO,EAAAC,QAAA,CAMTC,KANgB,KACXA,KAAK,CAACC,GAAG,CAACiB,qBAAqB,I,cAA3C/F,mBAAA,CAKO;;QAJDD,KAAK,EAAC,WAAW;QAChBqB,OAAK,EAAAK,MAAA,IAAElB,MAAA,CAAA8E,YAAY,CAACR,KAAK,CAACC,GAAG,CAAC9B,SAAS;QACvCsC,KAAK,EAAET,KAAK,CAACC,GAAG,CAACiB;0BACnBlB,KAAK,CAACC,GAAG,CAACiB,qBAAqB,wBAAAC,WAAA,M,cAEpChG,mBAAA,CAAwC,QAAxCiG,WAAwC,EAAR,GAAC,G;;QAIrCjF,YAAA,CAUkBuD,0BAAA;MAVDnB,KAAK,EAAC,wBAAwB;MAACoB,KAAK,EAAC;;MACzCG,OAAO,EAAAC,QAAA,CAMTC,KANgB,KACXA,KAAK,CAACC,GAAG,CAACoB,yBAAyB,I,cAA/ClG,mBAAA,CAKO;;QAJDD,KAAK,EAAC,WAAW;QAChBqB,OAAK,EAAAK,MAAA,IAAElB,MAAA,CAAA8E,YAAY,CAACR,KAAK,CAACC,GAAG,CAAC9B,SAAS;QACvCsC,KAAK,EAAET,KAAK,CAACC,GAAG,CAACoB;0BACnBrB,KAAK,CAACC,GAAG,CAACoB,yBAAyB,wBAAAC,WAAA,M,cAExCnG,mBAAA,CAAwC,QAAxCoG,WAAwC,EAAR,GAAC,G;;QAIrCpF,YAAA,CAUkBuD,0BAAA;MAVDnB,KAAK,EAAC,qBAAqB;MAACoB,KAAK,EAAC;;MACtCG,OAAO,EAAAC,QAAA,CAMTC,KANgB,KACXA,KAAK,CAACC,GAAG,CAACuB,sBAAsB,I,cAA5CrG,mBAAA,CAKO;;QAJDD,KAAK,EAAC,WAAW;QAChBqB,OAAK,EAAAK,MAAA,IAAElB,MAAA,CAAA8E,YAAY,CAACR,KAAK,CAACC,GAAG,CAAC9B,SAAS;QACvCsC,KAAK,EAAET,KAAK,CAACC,GAAG,CAACuB;0BACnBxB,KAAK,CAACC,GAAG,CAACuB,sBAAsB,wBAAAC,WAAA,M,cAErCtG,mBAAA,CAAwC,QAAxCuG,WAAwC,EAAR,GAAC,G;;QAIrCvF,YAAA,CAUkBuD,0BAAA;MAVDnB,KAAK,EAAC,oBAAoB;MAACoB,KAAK,EAAC;;MACrCG,OAAO,EAAAC,QAAA,CAMTC,KANgB,KACXA,KAAK,CAACC,GAAG,CAAC0B,qBAAqB,I,cAA3CxG,mBAAA,CAKO;;QAJDD,KAAK,EAAC,WAAW;QAChBqB,OAAK,EAAAK,MAAA,IAAElB,MAAA,CAAA8E,YAAY,CAACR,KAAK,CAACC,GAAG,CAAC9B,SAAS;QACvCsC,KAAK,EAAET,KAAK,CAACC,GAAG,CAAC0B;0BACnB3B,KAAK,CAACC,GAAG,CAAC0B,qBAAqB,wBAAAC,WAAA,M,cAEpCzG,mBAAA,CAAwC,QAAxC0G,WAAwC,EAAR,GAAC,G;;QAIrC1F,YAAA,CAUkBuD,0BAAA;MAVDnB,KAAK,EAAC,oBAAoB;MAACoB,KAAK,EAAC;;MACrCG,OAAO,EAAAC,QAAA,CAMTC,KANgB,KACXA,KAAK,CAACC,GAAG,CAAC6B,qBAAqB,I,cAA3C3G,mBAAA,CAKO;;QAJDD,KAAK,EAAC,WAAW;QAChBqB,OAAK,EAAAK,MAAA,IAAElB,MAAA,CAAA8E,YAAY,CAACR,KAAK,CAACC,GAAG,CAAC9B,SAAS;QACvCsC,KAAK,EAAET,KAAK,CAACC,GAAG,CAAC6B;0BACnB9B,KAAK,CAACC,GAAG,CAAC6B,qBAAqB,wBAAAC,WAAA,M,cAEpC5G,mBAAA,CAAwC,QAAxC6G,WAAwC,EAAR,GAAC,G;;QAIrC7F,YAAA,CAUkBuD,0BAAA;MAVDnB,KAAK,EAAC,OAAO;MAACoB,KAAK,EAAC;;MACxBG,OAAO,EAAAC,QAAA,CAMTC,KANgB,KACXA,KAAK,CAACC,GAAG,CAACgC,SAAS,I,cAA/B9G,mBAAA,CAKO;;QAJDD,KAAK,EAAC,WAAW;QAChBqB,OAAK,EAAAK,MAAA,IAAElB,MAAA,CAAA8E,YAAY,CAACR,KAAK,CAACC,GAAG,CAAC9B,SAAS;QACvCsC,KAAK,EAAET,KAAK,CAACC,GAAG,CAACgC;0BACnBjC,KAAK,CAACC,GAAG,CAACgC,SAAS,wBAAAC,WAAA,M,cAExB/G,mBAAA,CAAwC,QAAxCgH,WAAwC,EAAR,GAAC,G;;QAIrChG,YAAA,CAUkBuD,0BAAA;MAVDnB,KAAK,EAAC,YAAY;MAACoB,KAAK,EAAC;;MAC7BG,OAAO,EAAAC,QAAA,CAMTC,KANgB,KACXA,KAAK,CAACC,GAAG,CAACmC,cAAc,I,cAApCjH,mBAAA,CAKO;;QAJDD,KAAK,EAAC,WAAW;QAChBqB,OAAK,EAAAK,MAAA,IAAElB,MAAA,CAAA8E,YAAY,CAACR,KAAK,CAACC,GAAG,CAAC9B,SAAS;QACvCsC,KAAK,EAAET,KAAK,CAACC,GAAG,CAACmC;0BACnBpC,KAAK,CAACC,GAAG,CAACmC,cAAc,wBAAAC,WAAA,M,cAE7BlH,mBAAA,CAAwC,QAAxCmH,WAAwC,EAAR,GAAC,G;;QAIrCnG,YAAA,CAUkBuD,0BAAA;MAVDnB,KAAK,EAAC,KAAK;MAACoB,KAAK,EAAC;;MACtBG,OAAO,EAAAC,QAAA,CAMTC,KANgB,KACXA,KAAK,CAACC,GAAG,CAACsC,OAAO,I,cAA7BpH,mBAAA,CAKO;;QAJDD,KAAK,EAAC,WAAW;QAChBqB,OAAK,EAAAK,MAAA,IAAElB,MAAA,CAAA8E,YAAY,CAACR,KAAK,CAACC,GAAG,CAAC9B,SAAS;QACvCsC,KAAK,EAAET,KAAK,CAACC,GAAG,CAACsC;0BACnBvC,KAAK,CAACC,GAAG,CAACsC,OAAO,wBAAAC,WAAA,M,cAEtBrH,mBAAA,CAAwC,QAAxCsH,WAAwC,EAAR,GAAC,G;;QAIrCtG,YAAA,CAUkBuD,0BAAA;MAVDnB,KAAK,EAAC,YAAY;MAACoB,KAAK,EAAC;;MAC7BG,OAAO,EAAAC,QAAA,CAMTC,KANgB,KACXA,KAAK,CAACC,GAAG,CAACyC,cAAc,I,cAApCvH,mBAAA,CAKO;;QAJDD,KAAK,EAAC,WAAW;QAChBqB,OAAK,EAAAK,MAAA,IAAElB,MAAA,CAAA8E,YAAY,CAACR,KAAK,CAACC,GAAG,CAAC9B,SAAS;QACvCsC,KAAK,EAAET,KAAK,CAACC,GAAG,CAACyC;0BACnB1C,KAAK,CAACC,GAAG,CAACyC,cAAc,wBAAAC,WAAA,M,cAE7BxH,mBAAA,CAAwC,QAAxCyH,WAAwC,EAAR,GAAC,G;;QAIrCzG,YAAA,CAUkBuD,0BAAA;MAVDnB,KAAK,EAAC,OAAO;MAACoB,KAAK,EAAC;;MACxBG,OAAO,EAAAC,QAAA,CAMTC,KANgB,KACXA,KAAK,CAACC,GAAG,CAAC4C,SAAS,I,cAA/B1H,mBAAA,CAKO;;QAJDD,KAAK,EAAC,WAAW;QAChBqB,OAAK,EAAAK,MAAA,IAAElB,MAAA,CAAA8E,YAAY,CAACR,KAAK,CAACC,GAAG,CAAC9B,SAAS;QACvCsC,KAAK,EAAET,KAAK,CAACC,GAAG,CAAC4C;0BACnB7C,KAAK,CAACC,GAAG,CAAC4C,SAAS,wBAAAC,WAAA,M,cAExB3H,mBAAA,CAAwC,QAAxC4H,WAAwC,EAAR,GAAC,G;;QAIrC5G,YAAA,CAUkBuD,0BAAA;MAVDnB,KAAK,EAAC,MAAM;MAACoB,KAAK,EAAC;;MACvBG,OAAO,EAAAC,QAAA,CAMTC,KANgB,KACXA,KAAK,CAACC,GAAG,CAAC+C,QAAQ,I,cAA9B7H,mBAAA,CAKO;;QAJDD,KAAK,EAAC,WAAW;QAChBqB,OAAK,EAAAK,MAAA,IAAElB,MAAA,CAAA8E,YAAY,CAACR,KAAK,CAACC,GAAG,CAAC9B,SAAS;QACvCsC,KAAK,EAAET,KAAK,CAACC,GAAG,CAAC+C;0BACnBhD,KAAK,CAACC,GAAG,CAAC+C,QAAQ,wBAAAC,WAAA,M,cAEvB9H,mBAAA,CAAwC,QAAxC+H,WAAwC,EAAR,GAAC,G;;QAIrC/G,YAAA,CAUkBuD,0BAAA;MAVDnB,KAAK,EAAC,MAAM;MAACoB,KAAK,EAAC;;MACvBG,OAAO,EAAAC,QAAA,CAMTC,KANgB,KACXA,KAAK,CAACC,GAAG,CAACkD,QAAQ,I,cAA9BhI,mBAAA,CAKO;;QAJDD,KAAK,EAAC,WAAW;QAChBqB,OAAK,EAAAK,MAAA,IAAElB,MAAA,CAAA8E,YAAY,CAACR,KAAK,CAACC,GAAG,CAAC9B,SAAS;QACvCsC,KAAK,EAAET,KAAK,CAACC,GAAG,CAACkD;0BACnBnD,KAAK,CAACC,GAAG,CAACkD,QAAQ,wBAAAC,WAAA,M,cAEvBjI,mBAAA,CAAwC,QAAxCkI,WAAwC,EAAR,GAAC,G;;QAIrClH,YAAA,CAKkBuD,0BAAA;MALDnB,KAAK,EAAC,IAAI;MAACoB,KAAK,EAAC,KAAK;MAACC,KAAK,EAAC;;MACjCE,OAAO,EAAAC,QAAA,CACiEC,KAD1D,KACvB7D,YAAA,CAAiFC,oBAAA;QAAtEC,IAAI,EAAC,SAAS;QAACiH,IAAI,EAAC,OAAO;QAAE/G,OAAK,EAAAK,MAAA,IAAElB,MAAA,CAAA6H,OAAO,CAACvD,KAAK,CAACC,GAAG;;0BAAG,MAAEtD,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;wDACrER,YAAA,CAAkFC,oBAAA;QAAvEC,IAAI,EAAC,QAAQ;QAACiH,IAAI,EAAC,OAAO;QAAE/G,OAAK,EAAAK,MAAA,IAAElB,MAAA,CAAA8H,SAAS,CAACxD,KAAK,CAACC,GAAG;;0BAAG,MAAEtD,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;;;;;2EAlN/DjB,MAAA,CAAA+H,OAAO,E,GAuNpBvG,mBAAA,QAAW,EACX7B,mBAAA,CAUM,OAVNqI,WAUM,GATJvH,YAAA,CAQEwH,wBAAA;IAPQ,cAAY,EAAEjI,MAAA,CAAAkI,WAAW;gEAAXlI,MAAA,CAAAkI,WAAW,GAAAhH,MAAA;IACzB,WAAS,EAAElB,MAAA,CAAAmI,QAAQ;6DAARnI,MAAA,CAAAmI,QAAQ,GAAAjH,MAAA;IAC1B,YAAU,EAAE,aAAa;IACzBkH,KAAK,EAAEpI,MAAA,CAAAC,UAAU;IAClBoI,MAAM,EAAC,yCAAyC;IAC/CC,YAAW,EAAEtI,MAAA,CAAAuI,gBAAgB;IAC7BC,eAAc,EAAExI,MAAA,CAAAyI;wGAIrBjH,mBAAA,cAAiB,EACjBf,YAAA,CAyNYiI,oBAAA;gBAxND1I,MAAA,CAAAmB,aAAa;iEAAbnB,MAAA,CAAAmB,aAAa,GAAAD,MAAA;IACrB6D,KAAK,EAAE/E,MAAA,CAAA2I,UAAU;IAClB1E,KAAK,EAAC,OAAO;IACZ2E,OAAK,EAAE5I,MAAA,CAAA6I;;IA+MGC,MAAM,EAAAzE,QAAA,CACf,MAGO,CAHP1E,mBAAA,CAGO,QAHPoJ,YAGO,GAFLtI,YAAA,CAAwDC,oBAAA;MAA5CG,OAAK,EAAAI,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAElB,MAAA,CAAAmB,aAAa;;wBAAU,MAAEF,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;QAC5CR,YAAA,CAA4EC,oBAAA;MAAjEC,IAAI,EAAC,SAAS;MAAEE,OAAK,EAAEb,MAAA,CAAAgJ,QAAQ;MAAGjB,OAAO,EAAE/H,MAAA,CAAAiJ;;wBAAQ,MAAEhI,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;;sBAhNpE,MA2MU,CA3MVR,YAAA,CA2MUyI,kBAAA;MA3MAC,KAAK,EAAEnJ,MAAA,CAAAoJ,QAAQ;MAAGC,KAAK,EAAErJ,MAAA,CAAAsJ,SAAS;MAAEC,GAAG,EAAC,SAAS;MAAC,aAAW,EAAC;;wBACtE,MAEe,CAFf9I,YAAA,CAEe+I,uBAAA;QAFD3G,KAAK,EAAC,WAAW;QAACsB,IAAI,EAAC;;0BACnC,MAAgE,CAAhE1D,YAAA,CAAgEgJ,mBAAA;sBAA7CzJ,MAAA,CAAAoJ,QAAQ,CAAC3G,SAAS;qEAAlBzC,MAAA,CAAAoJ,QAAQ,CAAC3G,SAAS,GAAAvB,MAAA;UAAGN,QAAQ,EAAEZ,MAAA,CAAA2I;;;UAGpDlI,YAAA,CAee+I,uBAAA;QAfD3G,KAAK,EAAC,eAAe;QAACsB,IAAI,EAAC;;0BACvC,MAaY,CAbZ1D,YAAA,CAaYmB,oBAAA;sBAZD5B,MAAA,CAAAoJ,QAAQ,CAACM,aAAa;qEAAtB1J,MAAA,CAAAoJ,QAAQ,CAACM,aAAa,GAAAxI,MAAA;UAC/BY,WAAW,EAAC,UAAU;UACtBC,UAAU,EAAV,EAAU;UACV,cAAY,EAAZ,EAAY;UACZ,sBAAoB,EAApB,EAAoB;UACnB,iBAAe,EAAE,KAAK;UACtBG,QAAM,EAAE7B,IAAA,CAAAsJ;;4BAEP,MAAsC,E,kBADxClK,mBAAA,CAI0B6C,SAAA,QAAAC,WAAA,CAHPvC,MAAA,CAAAmD,oBAAoB,EAA9BC,MAAM;iCADfV,YAAA,CAI0BC,oBAAA;cAFvBC,GAAG,EAAEQ,MAAM,CAACN,KAAK;cACjBD,KAAK,EAAEO,MAAM,CAACP,KAAK;cACnBC,KAAK,EAAEM,MAAM,CAACN;;;;;;UAIrBrC,YAAA,CAEe+I,uBAAA;QAFD3G,KAAK,EAAC,aAAa;QAACsB,IAAI,EAAC;;0BACrC,MAAyE,CAAzE1D,YAAA,CAAyEgJ,mBAAA;sBAAtDzJ,MAAA,CAAAoJ,QAAQ,CAAC5E,OAAO;qEAAhBxE,MAAA,CAAAoJ,QAAQ,CAAC5E,OAAO,GAAAtD,MAAA;UAAEY,WAAW,EAAC;;;UAGnDrB,YAAA,CAEe+I,uBAAA;QAFD3G,KAAK,EAAC,IAAI;QAACsB,IAAI,EAAC;;0BAC5B,MAAgF,CAAhF1D,YAAA,CAAgFmJ,0BAAA;sBAAtD5J,MAAA,CAAAoJ,QAAQ,CAACS,SAAS;qEAAlB7J,MAAA,CAAAoJ,QAAQ,CAACS,SAAS,GAAA3I,MAAA;UAAG4I,SAAS,EAAE,CAAC;UAAEhI,WAAW,EAAC;;;UAG3ErB,YAAA,CAEe+I,uBAAA;QAFD3G,KAAK,EAAC,IAAI;QAACsB,IAAI,EAAC;;0BAC5B,MAA+E,CAA/E1D,YAAA,CAA+EmJ,0BAAA;sBAArD5J,MAAA,CAAAoJ,QAAQ,CAACW,QAAQ;qEAAjB/J,MAAA,CAAAoJ,QAAQ,CAACW,QAAQ,GAAA7I,MAAA;UAAG4I,SAAS,EAAE,CAAC;UAAEhI,WAAW,EAAC;;;UAG1EN,mBAAA,YAAe,EACff,YAAA,CAAqDuJ,qBAAA;QAAzC,kBAAgB,EAAC;MAAM;0BAAC,MAAI/I,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UAExCtB,mBAAA,CAqKM,OArKNsK,WAqKM,GApKJzI,mBAAA,YAAe,EACf7B,mBAAA,CAQM,OARNuK,WAQM,G,4BAPJvK,mBAAA,CAAsB,eAAf,SAAO,qBACdA,mBAAA,CAKM,OALNwK,WAKM,GAJQnK,MAAA,CAAAoJ,QAAQ,CAACgB,KAAK,CAACC,MAAM,I,cAAjC5K,mBAAA,CAA0F,QAA1F6K,WAA0F,EAAAvK,gBAAA,CAA/BC,MAAA,CAAAoJ,QAAQ,CAACgB,KAAK,CAACC,MAAM,qB,cAChF5K,mBAAA,CAAuC,QAAvC8K,WAAuC,EAAV,KAAG,IAChC9J,YAAA,CAAsEC,oBAAA;QAA3DkH,IAAI,EAAC,OAAO;QAAE/G,OAAK,EAAAI,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAElB,MAAA,CAAAwK,UAAU;;0BAAY,MAAIvJ,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UACzCjB,MAAA,CAAAoJ,QAAQ,CAACgB,KAAK,CAACC,MAAM,I,cAAtC3H,YAAA,CAA+GhC,oBAAA;;QAAvEkH,IAAI,EAAC,OAAO;QAACjH,IAAI,EAAC,QAAQ;QAAEE,OAAK,EAAAI,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAElB,MAAA,CAAAyK,UAAU;;0BAAY,MAAExJ,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;mDAIvGO,mBAAA,gBAAmB,EACnB7B,mBAAA,CAQM,OARN+K,WAQM,G,4BAPJ/K,mBAAA,CAA0B,eAAnB,aAAW,qBAClBA,mBAAA,CAKM,OALNgL,WAKM,GAJQ3K,MAAA,CAAAoJ,QAAQ,CAACgB,KAAK,CAACQ,UAAU,I,cAArCnL,mBAAA,CAAkG,QAAlGoL,WAAkG,EAAA9K,gBAAA,CAAnCC,MAAA,CAAAoJ,QAAQ,CAACgB,KAAK,CAACQ,UAAU,qB,cACxFnL,mBAAA,CAAuC,QAAvCqL,WAAuC,EAAV,KAAG,IAChCrK,YAAA,CAA0EC,oBAAA;QAA/DkH,IAAI,EAAC,OAAO;QAAE/G,OAAK,EAAAI,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAElB,MAAA,CAAAwK,UAAU;;0BAAgB,MAAIvJ,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UAC7CjB,MAAA,CAAAoJ,QAAQ,CAACgB,KAAK,CAACQ,UAAU,I,cAA1ClI,YAAA,CAAuHhC,oBAAA;;QAA3EkH,IAAI,EAAC,OAAO;QAACjH,IAAI,EAAC,QAAQ;QAAEE,OAAK,EAAAI,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAElB,MAAA,CAAAyK,UAAU;;0BAAgB,MAAExJ,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;mDAI/GO,mBAAA,uBAA0B,EAC1B7B,mBAAA,CAQM,OARNoL,WAQM,G,4BAPJpL,mBAAA,CAAiC,eAA1B,oBAAkB,qBACzBA,mBAAA,CAKM,OALNqL,WAKM,GAJQhL,MAAA,CAAAoJ,QAAQ,CAACgB,KAAK,CAACa,gBAAgB,I,cAA3CxL,mBAAA,CAA8G,QAA9GyL,WAA8G,EAAAnL,gBAAA,CAAzCC,MAAA,CAAAoJ,QAAQ,CAACgB,KAAK,CAACa,gBAAgB,qB,cACpGxL,mBAAA,CAAuC,QAAvC0L,WAAuC,EAAV,KAAG,IAChC1K,YAAA,CAAgFC,oBAAA;QAArEkH,IAAI,EAAC,OAAO;QAAE/G,OAAK,EAAAI,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAElB,MAAA,CAAAwK,UAAU;;0BAAsB,MAAIvJ,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UACnDjB,MAAA,CAAAoJ,QAAQ,CAACgB,KAAK,CAACa,gBAAgB,I,cAAhDvI,YAAA,CAAmIhC,oBAAA;;QAAjFkH,IAAI,EAAC,OAAO;QAACjH,IAAI,EAAC,QAAQ;QAAEE,OAAK,EAAAI,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAElB,MAAA,CAAAyK,UAAU;;0BAAsB,MAAExJ,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;mDAI3HO,mBAAA,wBAA2B,EAC3B7B,mBAAA,CAQM,OARNyL,WAQM,G,4BAPJzL,mBAAA,CAAkC,eAA3B,qBAAmB,qBAC1BA,mBAAA,CAKM,OALN0L,WAKM,GAJQrL,MAAA,CAAAoJ,QAAQ,CAACgB,KAAK,CAACkB,iBAAiB,I,cAA5C7L,mBAAA,CAAgH,QAAhH8L,WAAgH,EAAAxL,gBAAA,CAA1CC,MAAA,CAAAoJ,QAAQ,CAACgB,KAAK,CAACkB,iBAAiB,qB,cACtG7L,mBAAA,CAAuC,QAAvC+L,WAAuC,EAAV,KAAG,IAChC/K,YAAA,CAAiFC,oBAAA;QAAtEkH,IAAI,EAAC,OAAO;QAAE/G,OAAK,EAAAI,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAElB,MAAA,CAAAwK,UAAU;;0BAAuB,MAAIvJ,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UACpDjB,MAAA,CAAAoJ,QAAQ,CAACgB,KAAK,CAACkB,iBAAiB,I,cAAjD5I,YAAA,CAAqIhC,oBAAA;;QAAlFkH,IAAI,EAAC,OAAO;QAACjH,IAAI,EAAC,QAAQ;QAAEE,OAAK,EAAAI,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAElB,MAAA,CAAAyK,UAAU;;0BAAuB,MAAExJ,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;mDAI7HO,mBAAA,4BAA+B,EAC/B7B,mBAAA,CAQM,OARN8L,WAQM,G,4BAPJ9L,mBAAA,CAAsC,eAA/B,yBAAuB,qBAC9BA,mBAAA,CAKM,OALN+L,WAKM,GAJQ1L,MAAA,CAAAoJ,QAAQ,CAACgB,KAAK,CAACuB,qBAAqB,I,cAAhDlM,mBAAA,CAAwH,QAAxHmM,WAAwH,EAAA7L,gBAAA,CAA9CC,MAAA,CAAAoJ,QAAQ,CAACgB,KAAK,CAACuB,qBAAqB,qB,cAC9GlM,mBAAA,CAAuC,QAAvCoM,WAAuC,EAAV,KAAG,IAChCpL,YAAA,CAAqFC,oBAAA;QAA1EkH,IAAI,EAAC,OAAO;QAAE/G,OAAK,EAAAI,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAElB,MAAA,CAAAwK,UAAU;;0BAA2B,MAAIvJ,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UACxDjB,MAAA,CAAAoJ,QAAQ,CAACgB,KAAK,CAACuB,qBAAqB,I,cAArDjJ,YAAA,CAA6IhC,oBAAA;;QAAtFkH,IAAI,EAAC,OAAO;QAACjH,IAAI,EAAC,QAAQ;QAAEE,OAAK,EAAAI,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAElB,MAAA,CAAAyK,UAAU;;0BAA2B,MAAExJ,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;mDAIrIO,mBAAA,yBAA4B,EAC5B7B,mBAAA,CAQM,OARNmM,WAQM,G,4BAPJnM,mBAAA,CAAmC,eAA5B,sBAAoB,qBAC3BA,mBAAA,CAKM,OALNoM,WAKM,GAJQ/L,MAAA,CAAAoJ,QAAQ,CAACgB,KAAK,CAAC4B,kBAAkB,I,cAA7CvM,mBAAA,CAAkH,QAAlHwM,WAAkH,EAAAlM,gBAAA,CAA3CC,MAAA,CAAAoJ,QAAQ,CAACgB,KAAK,CAAC4B,kBAAkB,qB,cACxGvM,mBAAA,CAAuC,QAAvCyM,WAAuC,EAAV,KAAG,IAChCzL,YAAA,CAAkFC,oBAAA;QAAvEkH,IAAI,EAAC,OAAO;QAAE/G,OAAK,EAAAI,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAElB,MAAA,CAAAwK,UAAU;;0BAAwB,MAAIvJ,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UACrDjB,MAAA,CAAAoJ,QAAQ,CAACgB,KAAK,CAAC4B,kBAAkB,I,cAAlDtJ,YAAA,CAAuIhC,oBAAA;;QAAnFkH,IAAI,EAAC,OAAO;QAACjH,IAAI,EAAC,QAAQ;QAAEE,OAAK,EAAAI,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAElB,MAAA,CAAAyK,UAAU;;0BAAwB,MAAExJ,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;mDAI/HO,mBAAA,wBAA2B,EAC3B7B,mBAAA,CAQM,OARNwM,WAQM,G,4BAPJxM,mBAAA,CAAkC,eAA3B,qBAAmB,qBAC1BA,mBAAA,CAKM,OALNyM,WAKM,GAJQpM,MAAA,CAAAoJ,QAAQ,CAACgB,KAAK,CAACiC,iBAAiB,I,cAA5C5M,mBAAA,CAAgH,QAAhH6M,WAAgH,EAAAvM,gBAAA,CAA1CC,MAAA,CAAAoJ,QAAQ,CAACgB,KAAK,CAACiC,iBAAiB,qB,cACtG5M,mBAAA,CAAuC,QAAvC8M,WAAuC,EAAV,KAAG,IAChC9L,YAAA,CAAiFC,oBAAA;QAAtEkH,IAAI,EAAC,OAAO;QAAE/G,OAAK,EAAAI,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAElB,MAAA,CAAAwK,UAAU;;0BAAuB,MAAIvJ,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UACpDjB,MAAA,CAAAoJ,QAAQ,CAACgB,KAAK,CAACiC,iBAAiB,I,cAAjD3J,YAAA,CAAqIhC,oBAAA;;QAAlFkH,IAAI,EAAC,OAAO;QAACjH,IAAI,EAAC,QAAQ;QAAEE,OAAK,EAAAI,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAElB,MAAA,CAAAyK,UAAU;;0BAAuB,MAAExJ,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;mDAI7HO,mBAAA,wBAA2B,EAC3B7B,mBAAA,CAQM,OARN6M,WAQM,G,4BAPJ7M,mBAAA,CAAkC,eAA3B,qBAAmB,qBAC1BA,mBAAA,CAKM,OALN8M,WAKM,GAJQzM,MAAA,CAAAoJ,QAAQ,CAACgB,KAAK,CAACsC,iBAAiB,I,cAA5CjN,mBAAA,CAAgH,QAAhHkN,WAAgH,EAAA5M,gBAAA,CAA1CC,MAAA,CAAAoJ,QAAQ,CAACgB,KAAK,CAACsC,iBAAiB,qB,cACtGjN,mBAAA,CAAuC,QAAvCmN,WAAuC,EAAV,KAAG,IAChCnM,YAAA,CAAiFC,oBAAA;QAAtEkH,IAAI,EAAC,OAAO;QAAE/G,OAAK,EAAAI,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAElB,MAAA,CAAAwK,UAAU;;0BAAuB,MAAIvJ,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UACpDjB,MAAA,CAAAoJ,QAAQ,CAACgB,KAAK,CAACsC,iBAAiB,I,cAAjDhK,YAAA,CAAqIhC,oBAAA;;QAAlFkH,IAAI,EAAC,OAAO;QAACjH,IAAI,EAAC,QAAQ;QAAEE,OAAK,EAAAI,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAElB,MAAA,CAAAyK,UAAU;;0BAAuB,MAAExJ,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;mDAI7HO,mBAAA,WAAc,EACd7B,mBAAA,CAQM,OARNkN,WAQM,G,4BAPJlN,mBAAA,CAAqB,eAAd,QAAM,qBACbA,mBAAA,CAKM,OALNmN,WAKM,GAJQ9M,MAAA,CAAAoJ,QAAQ,CAACgB,KAAK,CAAC2C,KAAK,I,cAAhCtN,mBAAA,CAAwF,QAAxFuN,WAAwF,EAAAjN,gBAAA,CAA9BC,MAAA,CAAAoJ,QAAQ,CAACgB,KAAK,CAAC2C,KAAK,qB,cAC9EtN,mBAAA,CAAuC,QAAvCwN,WAAuC,EAAV,KAAG,IAChCxM,YAAA,CAAqEC,oBAAA;QAA1DkH,IAAI,EAAC,OAAO;QAAE/G,OAAK,EAAAI,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAElB,MAAA,CAAAwK,UAAU;;0BAAW,MAAIvJ,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UACxCjB,MAAA,CAAAoJ,QAAQ,CAACgB,KAAK,CAAC2C,KAAK,I,cAArCrK,YAAA,CAA6GhC,oBAAA;;QAAtEkH,IAAI,EAAC,OAAO;QAACjH,IAAI,EAAC,QAAQ;QAAEE,OAAK,EAAAI,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAElB,MAAA,CAAAyK,UAAU;;0BAAW,MAAExJ,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;mDAIrGO,mBAAA,gBAAmB,EACnB7B,mBAAA,CAQM,OARNuN,WAQM,G,4BAPJvN,mBAAA,CAA0B,eAAnB,aAAW,qBAClBA,mBAAA,CAKM,OALNwN,WAKM,GAJQnN,MAAA,CAAAoJ,QAAQ,CAACgB,KAAK,CAACgD,UAAU,I,cAArC3N,mBAAA,CAAkG,QAAlG4N,WAAkG,EAAAtN,gBAAA,CAAnCC,MAAA,CAAAoJ,QAAQ,CAACgB,KAAK,CAACgD,UAAU,qB,cACxF3N,mBAAA,CAAuC,QAAvC6N,WAAuC,EAAV,KAAG,IAChC7M,YAAA,CAA0EC,oBAAA;QAA/DkH,IAAI,EAAC,OAAO;QAAE/G,OAAK,EAAAI,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAElB,MAAA,CAAAwK,UAAU;;0BAAgB,MAAIvJ,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UAC7CjB,MAAA,CAAAoJ,QAAQ,CAACgB,KAAK,CAACgD,UAAU,I,cAA1C1K,YAAA,CAAuHhC,oBAAA;;QAA3EkH,IAAI,EAAC,OAAO;QAACjH,IAAI,EAAC,QAAQ;QAAEE,OAAK,EAAAI,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAElB,MAAA,CAAAyK,UAAU;;0BAAgB,MAAExJ,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;mDAI/GO,mBAAA,SAAY,EACZ7B,mBAAA,CAQM,OARN4N,WAQM,G,4BAPJ5N,mBAAA,CAAmB,eAAZ,MAAI,qBACXA,mBAAA,CAKM,OALN6N,WAKM,GAJQxN,MAAA,CAAAoJ,QAAQ,CAACgB,KAAK,CAACqD,GAAG,I,cAA9BhO,mBAAA,CAAoF,QAApFiO,WAAoF,EAAA3N,gBAAA,CAA5BC,MAAA,CAAAoJ,QAAQ,CAACgB,KAAK,CAACqD,GAAG,qB,cAC1EhO,mBAAA,CAAuC,QAAvCkO,WAAuC,EAAV,KAAG,IAChClN,YAAA,CAAmEC,oBAAA;QAAxDkH,IAAI,EAAC,OAAO;QAAE/G,OAAK,EAAAI,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAElB,MAAA,CAAAwK,UAAU;;0BAAS,MAAIvJ,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UACtCjB,MAAA,CAAAoJ,QAAQ,CAACgB,KAAK,CAACqD,GAAG,I,cAAnC/K,YAAA,CAAyGhC,oBAAA;;QAApEkH,IAAI,EAAC,OAAO;QAACjH,IAAI,EAAC,QAAQ;QAAEE,OAAK,EAAAI,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAElB,MAAA,CAAAyK,UAAU;;0BAAS,MAAExJ,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;mDAIjGO,mBAAA,gBAAmB,EACnB7B,mBAAA,CAQM,OARNiO,WAQM,G,4BAPJjO,mBAAA,CAA0B,eAAnB,aAAW,qBAClBA,mBAAA,CAKM,OALNkO,WAKM,GAJQ7N,MAAA,CAAAoJ,QAAQ,CAACgB,KAAK,CAAC0D,UAAU,I,cAArCrO,mBAAA,CAAkG,QAAlGsO,WAAkG,EAAAhO,gBAAA,CAAnCC,MAAA,CAAAoJ,QAAQ,CAACgB,KAAK,CAAC0D,UAAU,qB,cACxFrO,mBAAA,CAAuC,QAAvCuO,WAAuC,EAAV,KAAG,IAChCvN,YAAA,CAA0EC,oBAAA;QAA/DkH,IAAI,EAAC,OAAO;QAAE/G,OAAK,EAAAI,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAElB,MAAA,CAAAwK,UAAU;;0BAAgB,MAAIvJ,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UAC7CjB,MAAA,CAAAoJ,QAAQ,CAACgB,KAAK,CAAC0D,UAAU,I,cAA1CpL,YAAA,CAAuHhC,oBAAA;;QAA3EkH,IAAI,EAAC,OAAO;QAACjH,IAAI,EAAC,QAAQ;QAAEE,OAAK,EAAAI,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAElB,MAAA,CAAAyK,UAAU;;0BAAgB,MAAExJ,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;mDAI/GO,mBAAA,WAAc,EACd7B,mBAAA,CAQM,OARNsO,WAQM,G,4BAPJtO,mBAAA,CAAqB,eAAd,QAAM,qBACbA,mBAAA,CAKM,OALNuO,WAKM,GAJQlO,MAAA,CAAAoJ,QAAQ,CAACgB,KAAK,CAAC+D,KAAK,I,cAAhC1O,mBAAA,CAAwF,QAAxF2O,WAAwF,EAAArO,gBAAA,CAA9BC,MAAA,CAAAoJ,QAAQ,CAACgB,KAAK,CAAC+D,KAAK,qB,cAC9E1O,mBAAA,CAAuC,QAAvC4O,WAAuC,EAAV,KAAG,IAChC5N,YAAA,CAAqEC,oBAAA;QAA1DkH,IAAI,EAAC,OAAO;QAAE/G,OAAK,EAAAI,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAElB,MAAA,CAAAwK,UAAU;;0BAAW,MAAIvJ,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UACxCjB,MAAA,CAAAoJ,QAAQ,CAACgB,KAAK,CAAC+D,KAAK,I,cAArCzL,YAAA,CAA6GhC,oBAAA;;QAAtEkH,IAAI,EAAC,OAAO;QAACjH,IAAI,EAAC,QAAQ;QAAEE,OAAK,EAAAI,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAElB,MAAA,CAAAyK,UAAU;;0BAAW,MAAExJ,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;mDAIrGO,mBAAA,UAAa,EACb7B,mBAAA,CAQM,OARN2O,WAQM,G,4BAPJ3O,mBAAA,CAAoB,eAAb,OAAK,qBACZA,mBAAA,CAKM,OALN4O,YAKM,GAJQvO,MAAA,CAAAoJ,QAAQ,CAACgB,KAAK,CAACoE,IAAI,I,cAA/B/O,mBAAA,CAAsF,QAAtFgP,YAAsF,EAAA1O,gBAAA,CAA7BC,MAAA,CAAAoJ,QAAQ,CAACgB,KAAK,CAACoE,IAAI,qB,cAC5E/O,mBAAA,CAAuC,QAAvCiP,YAAuC,EAAV,KAAG,IAChCjO,YAAA,CAAoEC,oBAAA;QAAzDkH,IAAI,EAAC,OAAO;QAAE/G,OAAK,EAAAI,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAElB,MAAA,CAAAwK,UAAU;;0BAAU,MAAIvJ,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UACvCjB,MAAA,CAAAoJ,QAAQ,CAACgB,KAAK,CAACoE,IAAI,I,cAApC9L,YAAA,CAA2GhC,oBAAA;;QAArEkH,IAAI,EAAC,OAAO;QAACjH,IAAI,EAAC,QAAQ;QAAEE,OAAK,EAAAI,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAElB,MAAA,CAAAyK,UAAU;;0BAAU,MAAExJ,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;mDAInGO,mBAAA,UAAa,EACb7B,mBAAA,CAQM,OARNgP,YAQM,G,4BAPJhP,mBAAA,CAAoB,eAAb,OAAK,qBACZA,mBAAA,CAKM,OALNiP,YAKM,GAJQ5O,MAAA,CAAAoJ,QAAQ,CAACgB,KAAK,CAACyE,IAAI,I,cAA/BpP,mBAAA,CAAsF,QAAtFqP,YAAsF,EAAA/O,gBAAA,CAA7BC,MAAA,CAAAoJ,QAAQ,CAACgB,KAAK,CAACyE,IAAI,qB,cAC5EpP,mBAAA,CAAuC,QAAvCsP,YAAuC,EAAV,KAAG,IAChCtO,YAAA,CAAoEC,oBAAA;QAAzDkH,IAAI,EAAC,OAAO;QAAE/G,OAAK,EAAAI,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAElB,MAAA,CAAAwK,UAAU;;0BAAU,MAAIvJ,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UACvCjB,MAAA,CAAAoJ,QAAQ,CAACgB,KAAK,CAACyE,IAAI,I,cAApCnM,YAAA,CAA2GhC,oBAAA;;QAArEkH,IAAI,EAAC,OAAO;QAACjH,IAAI,EAAC,QAAQ;QAAEE,OAAK,EAAAI,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAElB,MAAA,CAAAyK,UAAU;;0BAAU,MAAExJ,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
# Transcriptome 列拆分更新

## 更新概述

将原来的单个 "Transcriptome" 列拆分为6个独立的列，分别对应不同的组织类型。

## 新的列结构

### 原来
- **Transcriptome** - 单个列显示任意转录组文件

### 现在
- **Transcriptome.all** - 全组织转录组文件
- **Transcriptome.leaf** - 叶片转录组文件
- **Transcriptome.panicles** - 穗部转录组文件
- **Transcriptome.shoot** - 芽部转录组文件
- **Transcriptome.stem** - 茎部转录组文件
- **Transcriptome.root** - 根部转录组文件

## 文件命名规则

每种转录组文件都有特定的命名格式：

```
transcriptome.{tissue_type}.{accession}.tar.gz
```

### 示例
- `transcriptome.all.IR64.tar.gz`
- `transcriptome.leaf.IR64.tar.gz`
- `transcriptome.panicles.IR64.tar.gz`
- `transcriptome.shoot.IR64.tar.gz`
- `transcriptome.stem.IR64.tar.gz`
- `transcriptome.root.IR64.tar.gz`

## 前端修改

### 1. 表格列定义
- 将单个 Transcriptome 列替换为6个独立列
- 每列都支持点击文件名下载功能
- 文件不存在时显示 `-` 占位符

### 2. 编辑对话框
- 在文件管理部分添加6个独立的转录组文件控制项
- 每个文件类型都有独立的选择和删除按钮
- 支持同时管理多个转录组文件

### 3. 数据结构更新
```javascript
// 原来
files: {
  transcriptome: ''
}

// 现在
files: {
  transcriptomeAll: '',
  transcriptomeLeaf: '',
  transcriptomePanicles: '',
  transcriptomeShoot: '',
  transcriptomeStem: '',
  transcriptomeRoot: ''
}
```

### 4. 文件类型映射
前端使用驼峰命名，API使用点分隔命名：

| 前端字段 | API参数 | 文件名格式 |
|----------|---------|------------|
| `transcriptomeAll` | `transcriptome.all` | `transcriptome.all.{accession}.tar.gz` |
| `transcriptomeLeaf` | `transcriptome.leaf` | `transcriptome.leaf.{accession}.tar.gz` |
| `transcriptomePanicles` | `transcriptome.panicles` | `transcriptome.panicles.{accession}.tar.gz` |
| `transcriptomeShoot` | `transcriptome.shoot` | `transcriptome.shoot.{accession}.tar.gz` |
| `transcriptomeStem` | `transcriptome.stem` | `transcriptome.stem.{accession}.tar.gz` |
| `transcriptomeRoot` | `transcriptome.root` | `transcriptome.root.{accession}.tar.gz` |

## 后端修改

### 1. 数据列表API (`admin_data_management_list`)
更新文件信息返回结构：

```python
# 原来
'transcriptomeFile': find_file([f'transcriptome.{ttype}.{accession}.tar.gz' for ttype in ['all', 'root', 'stem', 'leaf', 'panicles', 'shoot']])

# 现在
'transcriptomeAllFile': find_file([f'transcriptome.all.{accession}.tar.gz']),
'transcriptomeLeafFile': find_file([f'transcriptome.leaf.{accession}.tar.gz']),
'transcriptomePaniclesFile': find_file([f'transcriptome.panicles.{accession}.tar.gz']),
'transcriptomeShootFile': find_file([f'transcriptome.shoot.{accession}.tar.gz']),
'transcriptomeStemFile': find_file([f'transcriptome.stem.{accession}.tar.gz']),
'transcriptomeRootFile': find_file([f'transcriptome.root.{accession}.tar.gz']),
```

### 2. 文件上传API (`admin_upload_data_file`)
支持新的文件类型：

```python
file_extensions = {
    'transcriptome.all': ['.tar.gz'],
    'transcriptome.leaf': ['.tar.gz'],
    'transcriptome.panicles': ['.tar.gz'],
    'transcriptome.shoot': ['.tar.gz'],
    'transcriptome.stem': ['.tar.gz'],
    'transcriptome.root': ['.tar.gz'],
    # ... 其他文件类型
}
```

### 3. 文件下载/删除API
更新文件模式匹配：

```python
file_patterns = {
    'transcriptome.all': [f'transcriptome.all.{accession}.tar.gz'],
    'transcriptome.leaf': [f'transcriptome.leaf.{accession}.tar.gz'],
    'transcriptome.panicles': [f'transcriptome.panicles.{accession}.tar.gz'],
    'transcriptome.shoot': [f'transcriptome.shoot.{accession}.tar.gz'],
    'transcriptome.stem': [f'transcriptome.stem.{accession}.tar.gz'],
    'transcriptome.root': [f'transcriptome.root.{accession}.tar.gz'],
    # ... 其他文件类型
}
```

## 使用方法

### 1. 查看转录组文件
- 在数据表格中，每种组织类型都有独立的列
- 文件存在时显示文件名，可点击下载
- 文件不存在时显示 `-`

### 2. 上传转录组文件
1. 点击"编辑"或"新增 Accession"
2. 在文件管理部分找到对应的转录组类型
3. 点击"选择文件"按钮
4. 选择对应的 `.tar.gz` 文件
5. 点击"保存"完成上传

### 3. 删除转录组文件
1. 在编辑对话框中找到要删除的转录组类型
2. 点击红色的"删除"按钮
3. 点击"保存"确认删除

## 兼容性说明

### 向后兼容
- 保持了原有的布尔值字段（如 `hasTranscriptomeAll` 等）
- API端点和基本结构保持不变
- 现有的文件仍然可以正常访问

### 数据迁移
如果您有现有的转录组文件，需要：
1. 将文件重命名为新的格式
2. 点击"更新数据"按钮重新扫描文件系统
3. 确认所有文件都正确显示在对应的列中

## 注意事项

1. **文件格式**: 所有转录组文件必须是 `.tar.gz` 格式
2. **命名规范**: 严格按照 `transcriptome.{tissue}.{accession}.tar.gz` 格式命名
3. **组织类型**: 支持的组织类型为 `all`, `leaf`, `panicles`, `shoot`, `stem`, `root`
4. **文件大小**: 建议单个文件不超过 100MB
5. **同时上传**: 可以为同一个 Accession 上传多种组织类型的转录组文件

## 故障排除

1. **文件不显示**: 检查文件命名是否符合规范
2. **上传失败**: 确认文件格式为 `.tar.gz`
3. **下载失败**: 点击"更新数据"按钮重新扫描
4. **列显示异常**: 刷新页面或清除浏览器缓存

[metadata]
name = django-cors-headers
version = 3.13.0
description = django-cors-headers is a Django application for handling the server headers required for Cross-Origin Resource Sharing (CORS).
long_description = file: README.rst
long_description_content_type = text/x-rst
author = <PERSON>
author_email = <EMAIL>
maintainer = <PERSON>
maintainer_email = <EMAIL>
url = https://github.com/adamchainz/django-cors-headers
project_urls = 
	Changelog = https://github.com/adamchainz/django-cors-headers/blob/main/HISTORY.rst
	Twitter = https://twitter.com/adamchainz
license = MIT License
keywords = 
	django
	cors
	middleware
	rest
	api
classifiers = 
	Development Status :: 5 - Production/Stable
	Environment :: Web Environment
	Framework :: Django
	Framework :: Django :: 3.2
	Framework :: Django :: 4.0
	Framework :: Django :: 4.1
	Intended Audience :: Developers
	License :: OSI Approved :: MIT License
	Operating System :: OS Independent
	Programming Language :: Python
	Programming Language :: Python :: 3
	Programming Language :: Python :: 3.7
	Programming Language :: Python :: 3.8
	Programming Language :: Python :: 3.9
	Programming Language :: Python :: 3.10
	Programming Language :: Python :: 3.11
	Topic :: Software Development :: Libraries :: Application Frameworks
	Topic :: Software Development :: Libraries :: Python Modules

[options]
package_dir = 
	=src
packages = find:
include_package_data = True
install_requires = Django>=3.2
python_requires = >=3.7
zip_safe = False

[options.packages.find]
where = src

[coverage:run]
branch = True
parallel = True
source = 
	corsheaders
	tests

[coverage:paths]
source = 
	src
	.tox/*/site-packages

[coverage:report]
show_missing = True

[flake8]
max-line-length = 88
extend-ignore = E203

[egg_info]
tag_build = 
tag_date = 0


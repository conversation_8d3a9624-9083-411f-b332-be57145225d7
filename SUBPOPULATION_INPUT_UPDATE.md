# SubPopulation 字段输入功能更新

## 更新概述

将 SubPopulation 字段从只能下拉选择已存在的亚群，改为既可以选择已存在的亚群，也可以手动输入新的亚群。

## 功能变化

### 原来
- **限制性选择**: 只能从预定义的下拉列表中选择
- **固定选项**: 无法添加新的亚群类型
- **用户体验**: 如果需要的亚群不在列表中，无法输入

### 现在
- **灵活输入**: 既可以选择已有选项，也可以输入新值
- **自动创建**: 输入新值时会自动创建新选项
- **智能过滤**: 支持输入时的实时过滤匹配

## 技术实现

### Element Plus Select 组件属性

```vue
<el-select 
  v-model="formData.subPopulation" 
  placeholder="请选择或输入亚群"
  filterable
  allow-create
  default-first-option
  :reserve-keyword="false">
```

### 属性说明

| 属性 | 作用 | 说明 |
|------|------|------|
| `filterable` | 启用过滤功能 | 用户输入时可以过滤现有选项 |
| `allow-create` | 允许创建新选项 | 用户可以输入不在列表中的新值 |
| `default-first-option` | 默认选择第一个选项 | 过滤时自动高亮第一个匹配项 |
| `:reserve-keyword="false"` | 不保留关键字 | 选择后清空搜索关键字 |

## 预定义选项

系统仍然保留了常用的亚群选项供快速选择：

- **cA** - 亚群 A
- **cB** - 亚群 B  
- **GJ** - 粳稻
- **XI** - 籼稻
- **WILD** - 野生稻
- **O.glaberrima** - 光稃稻
- **未知** - 未知亚群（值为 "-"）

## 使用方法

### 1. 选择已有亚群
1. 点击 SubPopulation 下拉框
2. 从列表中选择需要的亚群
3. 点击选项完成选择

### 2. 输入新亚群
1. 点击 SubPopulation 下拉框
2. 直接输入新的亚群名称
3. 按回车键或点击"创建 xxx"选项
4. 新亚群会被自动添加并选中

### 3. 过滤搜索
1. 点击 SubPopulation 下拉框
2. 输入部分亚群名称
3. 系统会自动过滤显示匹配的选项
4. 选择匹配的选项或创建新选项

## 使用示例

### 场景1：选择常用亚群
```
用户操作：点击下拉框 → 选择 "cA"
结果：formData.subPopulation = "cA"
```

### 场景2：输入新亚群
```
用户操作：点击下拉框 → 输入 "NewGroup" → 按回车
结果：formData.subPopulation = "NewGroup"
```

### 场景3：过滤搜索
```
用户操作：点击下拉框 → 输入 "c" → 显示 "cA", "cB" → 选择 "cA"
结果：formData.subPopulation = "cA"
```

## 数据处理

### 前端处理
- 新输入的亚群值会直接保存到 `formData.subPopulation`
- 空值会被转换为 "-" 发送到后端
- 支持任意字符串作为亚群名称

### 后端处理
- 后端接收任意字符串作为 subPopulation 值
- 不需要预先定义或验证亚群名称
- 新的亚群值会被正常保存到数据库

## 验证规则

目前 SubPopulation 字段没有特殊的验证规则：
- **允许空值**: 空值会被转换为 "-"
- **允许任意字符**: 支持字母、数字、特殊字符
- **长度限制**: 建议不超过 50 个字符（数据库字段限制）

## 注意事项

### 1. 命名建议
- 使用有意义的亚群名称
- 避免使用特殊字符和空格
- 建议使用英文或标准缩写

### 2. 数据一致性
- 相同的亚群应使用相同的名称
- 注意大小写敏感性
- 建议建立亚群命名规范

### 3. 历史数据
- 现有的亚群数据不受影响
- 新输入的亚群会与现有数据并存
- 可以通过"更新数据"功能查看所有亚群

## 扩展功能建议

### 未来可能的改进
1. **动态选项加载**: 从数据库加载已存在的所有亚群
2. **亚群统计**: 显示每个亚群的使用次数
3. **亚群验证**: 添加亚群名称格式验证
4. **亚群管理**: 提供亚群的增删改查功能

### 实现动态选项（可选）
如果需要从数据库动态加载已存在的亚群，可以：

```javascript
// 加载已存在的亚群列表
const loadSubPopulations = async () => {
  try {
    const response = await axios.get('/admin/subpopulations/')
    subPopulationOptions.value = response.data.subpopulations
  } catch (error) {
    console.error('加载亚群列表失败:', error)
  }
}
```

## 总结

这次更新使 SubPopulation 字段更加灵活和用户友好：
- ✅ 保留了原有的快速选择功能
- ✅ 新增了自定义输入功能
- ✅ 支持智能过滤和搜索
- ✅ 提升了用户体验和数据录入效率

用户现在可以根据实际需要，既可以快速选择常用亚群，也可以灵活输入新的亚群类型，大大提高了系统的适用性和扩展性。

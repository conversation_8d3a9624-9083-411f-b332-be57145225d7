# 数据表格管理 - 更新数据功能

## 功能说明

在数据表格管理页面中新增了"更新数据"按钮，该功能直接复制自文件管理页面的"重新扫描"功能。

## 按钮位置

位于数据表格管理页面的顶部工具栏，在"刷新数据"按钮旁边：

```
[新增 Accession] [刷新数据] [更新数据]
```

## 功能对比

### 刷新数据 vs 更新数据

| 功能 | 刷新数据 | 更新数据 |
|------|----------|----------|
| **作用** | 重新加载当前数据 | 重新扫描文件系统并更新数据库 |
| **API调用** | `GET /admin/data-management/list/` | `POST /admin/rescan/` |
| **处理范围** | 仅刷新前端显示 | 扫描文件系统，同步数据库 |
| **适用场景** | 普通数据刷新 | 文件发生变化后的数据同步 |

## 技术实现

### 前端实现
```javascript
// 更新数据（重新扫描文件）
const handleUpdateData = async () => {
  try {
    loading.value = true
    const response = await axios.post('/admin/rescan/')

    if (response.data.success) {
      ElMessage.success(response.data.message)
      loadData()
    } else {
      ElMessage.error(response.data.message)
    }
  } catch (error) {
    console.error('更新数据失败:', error)
    ElMessage.error('更新数据失败')
  } finally {
    loading.value = false
  }
}
```

### 后端API
使用与文件管理页面相同的API端点：
- **URL**: `POST /admin/rescan/`
- **功能**: 扫描 `manual_files/` 目录，更新数据库记录
- **返回**: 扫描结果统计信息

## 使用方法

1. **何时使用**：
   - 在数据表格管理页面进行文件操作后
   - 发现文件状态显示不正确时
   - 手动在服务器上添加/删除文件后

2. **操作步骤**：
   - 点击页面顶部的"更新数据"按钮
   - 等待扫描完成（按钮会显示加载状态）
   - 查看成功消息，确认扫描结果
   - 数据表格会自动刷新显示最新状态

3. **预期结果**：
   - 系统会扫描 `manual_files/` 目录
   - 添加新发现的文件记录
   - 清理不存在文件的记录
   - 更新文件状态显示

## 扫描范围

更新数据功能会扫描以下内容：

### 文件类型
- Genome 文件 (`.fasta`, `.fa`, `.fas`)
- Annotation 文件 (`.gff`, `.gff3`)
- Transcriptome 文件 (`.tar.gz`)
- Codon 文件 (`.tar.gz`)
- Centromere 文件 (`.bed`)
- TEs 文件 (`.tar.gz`)
- CoreBlocks 文件 (`.bed`)
- miRNA 文件 (`.bed`)
- tRNA 文件 (`.bed`)
- rRNA 文件 (`.bed`)

### 处理逻辑
1. **扫描文件系统**: 遍历 `manual_files/` 目录
2. **添加新文件**: 为新发现的文件创建数据库记录
3. **清理无效记录**: 删除文件已不存在的数据库记录
4. **更新统计**: 返回添加和删除的文件数量

## 注意事项

1. **执行时间**: 扫描可能需要几秒钟，取决于文件数量
2. **数据同步**: 扫描完成后会自动刷新数据表格
3. **错误处理**: 如果扫描失败，会显示错误消息
4. **权限要求**: 需要管理员权限才能执行扫描操作

## 与文件管理页面的关系

该功能与文件管理页面的"重新扫描"按钮完全相同：
- 使用相同的后端API (`/admin/rescan/`)
- 执行相同的扫描逻辑
- 返回相同格式的结果

这确保了两个页面的数据同步机制保持一致。

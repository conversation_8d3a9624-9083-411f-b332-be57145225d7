# Generated by Django 3.2.25 on 2025-06-30 11:26

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='FileType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='类型名称')),
                ('extension', models.CharField(max_length=20, verbose_name='文件扩展名')),
                ('description', models.TextField(blank=True, null=True, verbose_name='描述')),
            ],
            options={
                'verbose_name': '文件类型',
                'verbose_name_plural': '文件类型',
            },
        ),
        migrations.CreateModel(
            name='GenomeFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, verbose_name='文件名')),
                ('organism', models.CharField(choices=[('MH63', 'MH63'), ('ZS97', 'ZS97'), ('N22', 'N22'), ('other', '其他')], max_length=50, verbose_name='生物体')),
                ('category', models.CharField(choices=[('genome', '基因组序列'), ('transcriptome', '转录组'), ('miRNA', '微RNA'), ('tRNA', '转运RNA'), ('rRNA', '核糖体RNA'), ('telomere', '端粒'), ('centromere', '着丝粒'), ('TEs', '转座子'), ('other', '其他')], max_length=50, verbose_name='文件类别')),
                ('file_path', models.CharField(max_length=500, verbose_name='文件路径')),
                ('description', models.TextField(blank=True, null=True, verbose_name='描述')),
                ('size', models.BigIntegerField(default=0, verbose_name='文件大小(字节)')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('file_type', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='files.filetype', verbose_name='文件类型')),
            ],
            options={
                'verbose_name': '基因组文件',
                'verbose_name_plural': '基因组文件',
            },
        ),
    ]

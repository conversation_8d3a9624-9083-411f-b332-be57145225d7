# 管理后台数据表格管理功能

## 功能概述

在管理后台 `http://localhost:8080/#/admin/dashboard` 中新增了"数据表格管理"功能，可以直接管理 `http://localhost:8080/#/data` 页面显示的所有数据。

## 主要功能

### 1. 数据展示
- 显示所有 Accession 数据
- 展示 SubPopulation、SeqData、经纬度信息
- 显示各种文件的存在状态（Genome、Annotation、Codon等）
- 支持分页浏览

### 2. 新增 Accession
- 点击"新增 Accession"按钮
- 填写必要信息：
  - Accession（必填，唯一标识）
  - SubPopulation（可选：cA、cB、GJ、XI、WILD、O.glaberrima）
  - SeqData URL（可选）
  - 经度、纬度（可选）
- 数据会直接写入 `manual_files/supplymentary_data.txt` 文件

### 3. 编辑 Accession
- 点击表格中的"编辑"按钮
- 修改 SubPopulation、SeqData、经纬度信息
- Accession 名称不可修改
- 修改会直接更新 `manual_files/supplymentary_data.txt` 文件

### 4. 删除 Accession
- 点击表格中的"删除"按钮
- 确认删除后会：
  - 从 `manual_files/supplymentary_data.txt` 中删除对应行
  - 删除 `manual_files/` 目录下所有相关文件：
    - `genome.{accession}.*`
    - `annotation.{accession}.*`
    - `codon.{accession}.*`
    - `centromere.{accession}.*`
    - `TEs.{accession}.*`
    - `coreBlocks.{accession}.*`
    - `miRNA.{accession}.*`
    - `tRNA.{accession}.*`
    - `rRNA.{accession}.*`
    - `transcriptome.*.{accession}.*`

## 数据文件结构

### supplymentary_data.txt 格式
```
Accession	SubPopulation	SeqData	longitude	latitude
TG27	cA	https://download.cncb.ac.cn/gsa/CRA004623/CRR306397/	80.77	7.87
TG49	cA	https://download.cncb.ac.cn/gsa/CRA004623/CRR306409/	90.36	23.69
...
```

### 文件命名规则
- 基因组文件：`genome.{accession}.{fasta|fa|fas}`
- 注释文件：`annotation.{accession}.gff`
- 密码子文件：`codon.{accession}.tar.gz`
- 着丝粒文件：`centromere.{accession}.bed`
- 转座子文件：`TEs.{accession}.tar.gz`
- 核心区块文件：`coreBlocks.{accession}.bed`
- RNA文件：`{miRNA|tRNA|rRNA}.{accession}.bed`
- 转录组文件：`transcriptome.{type}.{accession}.tar.gz`

## API 接口

### 后端新增的API接口：
1. `GET /admin/data-management/list/` - 获取数据列表
2. `POST /admin/data-management/accession/` - 新增 Accession
3. `PUT /admin/data-management/accession/{accession}/update/` - 更新 Accession
4. `DELETE /admin/data-management/accession/{accession}/delete/` - 删除 Accession

## 前端组件

### 新增组件：
- `vue_project/src/views/AdminDataManager.vue` - 数据表格管理组件

### 修改组件：
- `vue_project/src/views/AdminDashboard.vue` - 添加数据管理菜单和组件

## 使用方法

1. 访问 `http://localhost:8080/#/admin/login`
2. 使用管理员账号登录（用户名：root，密码：root123）
3. 在左侧菜单中选择"数据表格管理"
4. 进行新增、编辑、删除操作

## 注意事项

1. **数据同步**：所有操作都直接修改文件系统中的数据，会立即反映到前端数据表格中
2. **文件管理**：删除 Accession 时会同时删除所有相关的数据文件
3. **备份建议**：在进行批量操作前建议备份 `manual_files/` 目录
4. **权限控制**：目前使用简单的硬编码认证，生产环境建议使用更安全的认证方式

## 技术实现

- **前端**：Vue 3 + Element Plus
- **后端**：Django REST Framework
- **数据存储**：文本文件 + 文件系统
- **文件操作**：直接读写 `supplymentary_data.txt` 和相关数据文件

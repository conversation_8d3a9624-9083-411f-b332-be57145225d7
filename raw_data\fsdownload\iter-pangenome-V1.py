# -*- coding: utf-8 -*-
# @Author: wjq
# @Date:   2024-11-18 10:02:25
# @Last Modified by:   wjq
# @Last Modified time: 2024-12-02 21:38:27


import matplotlib.pyplot as plt
import numpy as np
import matplotlib.patches as mpatches
import matplotlib.pyplot as plt


"""
先将参考序列按照每行固定的碱基打印在每行上，记录每个碱基位置。
依次根据每次迭代，取交集的位置，显示碱基，其它位置上变成横线。
每次迭代显示出标记是哪个范围。每次过程都出一张图。

动画实现：考虑出多张图合并成动画；或者是用一些库直接化成动画的形式


前几次迭代只是 用色块表示,等最后一次结束再用碱基表示
"""


import re
from Bio import SeqIO



def Rectangle(ax, loc, heigt, width, color, alpha):
    p = mpatches.Rectangle(loc, width, heigt, edgecolor="none", facecolor=color, alpha=alpha)
    ax.add_patch(p)

def plot_sequence(sequence):

    text_width = len(text) * font_size / 72  # 72是点到英寸的转换因子
    text_height = font_size

    # 计算文本应该开始的位置，使其居中
    start_x = (width - text_width) / 2
    start_y = (height - text_height) / 2

    # 将文本写入PDF
    page.setFont(font, font_size)
    page.drawString(start_x, start_y, text)

    
    # 设置A4纸的尺寸（单位：英寸）
    plt.figure(figsize=(11, 8.27))

    # 将序列转换为列表，以便绘图
    sequence_list = list(sequence)

    # 绘制序列
    for i, base in enumerate(sequence_list):
        plt.text(i, 0, base, ha='center', va='center')

    # 设置图形的边界
    plt.xlim(0, len(sequence))
    plt.ylim(-1, 1)

    # 隐藏坐标轴
    plt.axis('off')

    # 保存图形到文件
    plt.savefig('sequence_plot.pdf', bbox_inches='tight', pad_inches=0)

    # 显示图形
    plt.show()


def get_seq(seq, pos):
    seq_len = len(seq)
    Recordseq = ''
    target_seq = {}
    for row in pos:
        s, e = int(row[1]), int(row[2])
        k = '_'.join(row)
        target_seq[k] = seq[s:e+1]

    return target_seq

def get_pos(coords):
    pos = []
    for row in open(coords):
        rows = row.strip().split()
        pos.append(rows[:3])
    return pos


seq = {}
for seq_record in SeqIO.parse("MH63RS3.fasta", "fasta"):
    seq[seq_record.id] = seq_record.seq

sequence = ''
for k in seq:
    # print(len(seq[k]))
    sequence = seq[k]



coordspos = get_pos('Chr01.50.R1_RP_qryRecord.coords')
target_seq = get_seq(sequence, coordspos)

len_num = 12
total_len = len(sequence)
one_length = 0
last_length = 0
if total_len % len_num == 0:
    one_length = int(total_len/len_num)
else:
    one_length = int(total_len/len_num)
    last_length = total_len - (len_num-1)*one_length
    # len_num += 1

# print(total_len, len_num, one_length, last_length, one_length*(len_num-1)+last_length)

gl1, gl2 = 0.92, 0.92
start = 0.04
end = 0.96

hight = gl1/len_num
fig=plt.figure(figsize=(10, 10))
root = plt.axes([0, 0, 1, 1])


hight_pos = []
s, e = 0, 0
for k in range(1, len_num+1):
    plt.plot([start, end], [start+float(hight/2)+hight*(k-1), start+float(hight/2)+hight*(k-1)], color='grey', lw=2, alpha=.5)

    if k == len_num:
        s = one_length*(k-1)
        e = s+last_length
        pixhight = start+float(hight/2)+hight*(k-1)
        hight_pos.append([s, e, pixhight, gl2/last_length])
    else:
        s = one_length*(k-1)
        e = s+one_length
        pixhight = start+float(hight/2)+hight*(k-1)
        hight_pos.append([s, e, pixhight, gl2/one_length])


color = '#55a868'
bar_height = 0.01
for row in coordspos:
    s, e = int(row[1]), int(row[2])
    for k in hight_pos:
        sp, ep, pixh, pixstep = k

        if sp < s and e < ep:
            Rectangle(root, [(s-sp+1)*pixstep+start, pixh-bar_height/2], bar_height, abs(e-s+1)*pixstep, color, 1)
        elif sp < s < ep and e > ep:
            Rectangle(root, [(s-sp+1)*pixstep+start, pixh-bar_height/2], bar_height, abs(ep-s+1)*pixstep, color, 1)
        elif sp > s and sp < e < ep:
            Rectangle(root, [(sp-sp)*pixstep+start, pixh-bar_height/2], bar_height, abs(e-sp+1)*pixstep, color, 1)
        elif sp > s and e > ep:
            Rectangle(root, [(sp-sp)*pixstep+start, pixh-bar_height/2], bar_height, abs(ep-sp+1)*pixstep, color, 1)

plt.savefig("pangenome.pdf", dpi=500)
plt.savefig("pangenome.png", dpi=600)




# fig=plt.figure(figsize=(10, 10))
# plt.axis('off') # Get rid of the axes
# 
# plt.subplots_adjust(left=0.02, right=1, top=0.98, bottom=0)



#  判断好没一行的长度，如果色块没在一行，要进行分割好
color = ['#66c2a5', '#fc8d62', '#66c2a5', '#fc8d62']
# Ptr_Sbr.local1.quartet
# loc = [0.06, 0.15, 0.06, 0.15]
loc = [0.1, 0.06, 0.1, 0.06]
# hight = [0.9, 0.7, 0.35, 0.15]
hight = [0.95, 0.7, 0.35, 0.1]

bar_height = 0.025


# Generated by Django 3.2.25 on 2025-07-01 14:39

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('files', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='FileCategory',
            fields=[
                ('code', models.CharField(max_length=50, primary_key=True, serialize=False, verbose_name='类别代码')),
                ('name', models.CharField(max_length=100, verbose_name='类别名称')),
                ('description', models.TextField(blank=True, null=True, verbose_name='描述')),
            ],
            options={
                'verbose_name': '文件类别',
                'verbose_name_plural': '文件类别',
            },
        ),
        migrations.CreateModel(
            name='Organism',
            fields=[
                ('code', models.CharField(max_length=20, primary_key=True, serialize=False, verbose_name='生物体代码')),
                ('name', models.Char<PERSON>ield(max_length=100, verbose_name='生物体名称')),
                ('description', models.TextField(blank=True, null=True, verbose_name='描述')),
            ],
            options={
                'verbose_name': '生物体',
                'verbose_name_plural': '生物体',
            },
        ),
        migrations.AlterField(
            model_name='genomefile',
            name='category',
            field=models.CharField(choices=[('genome', '基因组序列'), ('transcriptome.all', '转录组-All'), ('transcriptome.root', '转录组-Root'), ('transcriptome.stem', '转录组-Stem'), ('transcriptome.leaf', '转录组-Leaf'), ('transcriptome.panicles', '转录组-Panicles'), ('transcriptome.shoot', '转录组-Shoot'), ('miRNA', '微RNA'), ('tRNA', '转运RNA'), ('rRNA', '核糖体RNA'), ('telomere', '端粒'), ('centromere', '着丝粒'), ('TEs', '转座子'), ('other', '其他')], max_length=50, verbose_name='文件类别'),
        ),
        migrations.AlterField(
            model_name='genomefile',
            name='organism',
            field=models.CharField(max_length=50, verbose_name='生物体'),
        ),
    ]

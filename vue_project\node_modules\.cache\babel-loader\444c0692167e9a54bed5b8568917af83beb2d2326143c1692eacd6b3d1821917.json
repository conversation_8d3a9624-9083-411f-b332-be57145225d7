{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\nimport { ref, reactive, onMounted } from 'vue';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nimport { Plus, Refresh, Download, Upload, Delete, Document } from '@element-plus/icons-vue';\nimport axios from 'axios';\nexport default {\n  name: 'AdminDataManager',\n  components: {\n    Plus,\n    Refresh,\n    Download,\n    Upload,\n    Delete,\n    Document\n  },\n  setup() {\n    const loading = ref(false);\n    const saving = ref(false);\n    const tableData = ref([]);\n    const currentPage = ref(1);\n    const pageSize = ref(20);\n    const totalCount = ref(0);\n    const showAddDialog = ref(false);\n    const editingRow = ref(null);\n    const formRef = ref(null);\n\n    // 搜索和筛选相关\n    const searchAccession = ref('');\n    const selectedSubPopulations = ref([]);\n    const allAccessionOptions = ref([]);\n    const loadingAccessions = ref(false);\n    const subPopulationOptions = ref([{\n      label: 'cA',\n      value: 'cA'\n    }, {\n      label: 'cB',\n      value: 'cB'\n    }, {\n      label: 'GJ',\n      value: 'GJ'\n    }, {\n      label: 'XI',\n      value: 'XI'\n    }, {\n      label: 'WILD',\n      value: 'WILD'\n    }, {\n      label: 'O.glaberrima',\n      value: 'O.glaberrima'\n    }, {\n      label: '未知',\n      value: '-'\n    }]);\n    const formData = reactive({\n      accession: '',\n      subPopulation: '',\n      seqData: '',\n      longitude: null,\n      latitude: null,\n      files: {\n        genome: '',\n        annotation: '',\n        transcriptomeAll: '',\n        transcriptomeLeaf: '',\n        transcriptomePanicles: '',\n        transcriptomeShoot: '',\n        transcriptomeStem: '',\n        transcriptomeRoot: '',\n        codon: '',\n        centromere: '',\n        TEs: '',\n        coreBlocks: '',\n        miRNA: '',\n        tRNA: '',\n        rRNA: ''\n      },\n      filesToUpload: {} // 存储待上传的文件\n    });\n    const formRules = {\n      accession: [{\n        required: true,\n        message: '请输入Accession',\n        trigger: 'blur'\n      }]\n    };\n\n    // 加载亚群选项\n    const loadSubPopulationOptions = async () => {\n      try {\n        const response = await axios.get('/admin/data-management/list/', {\n          params: {\n            page: 1,\n            page_size: 1000\n          } // 获取所有数据来提取亚群\n        });\n        if (response.data.success && response.data.data) {\n          // 提取所有唯一的亚群\n          const existingSubPopulations = new Set();\n          response.data.data.forEach(item => {\n            if (item.subPopulation && item.subPopulation !== '-') {\n              existingSubPopulations.add(item.subPopulation);\n            }\n          });\n\n          // 合并默认选项和已存在的亚群\n          const defaultOptions = [{\n            label: 'cA',\n            value: 'cA'\n          }, {\n            label: 'cB',\n            value: 'cB'\n          }, {\n            label: 'GJ',\n            value: 'GJ'\n          }, {\n            label: 'XI',\n            value: 'XI'\n          }, {\n            label: 'WILD',\n            value: 'WILD'\n          }, {\n            label: 'O.glaberrima',\n            value: 'O.glaberrima'\n          }, {\n            label: '未知',\n            value: '-'\n          }];\n          const defaultValues = new Set(defaultOptions.map(opt => opt.value));\n          const additionalOptions = Array.from(existingSubPopulations).filter(value => !defaultValues.has(value)).map(value => ({\n            label: value,\n            value: value\n          }));\n          subPopulationOptions.value = [...defaultOptions, ...additionalOptions];\n        }\n      } catch (error) {\n        console.error('加载亚群选项失败:', error);\n      }\n    };\n\n    // 加载数据\n    const loadData = async () => {\n      try {\n        loading.value = true;\n        const params = {\n          page: currentPage.value,\n          page_size: pageSize.value\n        };\n\n        // 添加搜索参数\n        if (searchAccession.value) {\n          params.search = searchAccession.value;\n        }\n\n        // 添加亚群筛选参数\n        if (selectedSubPopulations.value.length > 0) {\n          params.sub_populations = selectedSubPopulations.value.join(',');\n        }\n        const response = await axios.get('/admin/data-management/list/', {\n          params\n        });\n        const data = response.data;\n        if (data.success) {\n          tableData.value = data.data || [];\n          totalCount.value = data.total || 0;\n\n          // 调试信息：检查第一条记录的文件状态\n          if (data.data && data.data.length > 0) {\n            console.log('第一条记录的文件状态:', data.data[0]);\n          }\n        } else {\n          throw new Error(data.message || '获取数据失败');\n        }\n      } catch (error) {\n        console.error('加载数据失败:', error);\n        ElMessage.error('加载数据失败');\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 刷新数据\n    const refreshData = () => {\n      loadData();\n    };\n\n    // 处理亚群变化（表单中的）\n    const handleSubPopulationChange = value => {\n      // 如果是新输入的亚群，添加到选项列表中\n      if (value && !subPopulationOptions.value.find(opt => opt.value === value)) {\n        subPopulationOptions.value.push({\n          label: value,\n          value: value\n        });\n      }\n    };\n\n    // 加载所有Accession选项\n    const loadAllAccessions = async () => {\n      try {\n        loadingAccessions.value = true;\n        const response = await axios.get('/admin/data-management/list/', {\n          params: {\n            page: 1,\n            page_size: 1000 // 获取所有数据\n          }\n        });\n        if (response.data.success) {\n          allAccessionOptions.value = response.data.data.map(item => item.accession).sort();\n        }\n      } catch (error) {\n        console.error('加载Accession选项失败:', error);\n      } finally {\n        loadingAccessions.value = false;\n      }\n    };\n\n    // 处理Accession选择变化\n    const handleAccessionChange = value => {\n      searchAccession.value = value;\n      currentPage.value = 1;\n      loadData();\n    };\n\n    // 清除Accession搜索\n    const handleAccessionClear = () => {\n      searchAccession.value = '';\n      currentPage.value = 1;\n      loadData();\n    };\n\n    // 处理SubPopulation筛选变化\n    const handleSubPopulationFilterChange = () => {\n      currentPage.value = 1;\n      loadData();\n    };\n\n    // 重置筛选\n    const resetFilters = () => {\n      searchAccession.value = '';\n      selectedSubPopulations.value = [];\n      currentPage.value = 1;\n      loadData();\n    };\n\n    // 更新数据（重新扫描文件）\n    const handleUpdateData = async () => {\n      try {\n        loading.value = true;\n        const response = await axios.post('/admin/rescan/');\n        if (response.data.success) {\n          ElMessage.success(response.data.message);\n          loadData();\n        } else {\n          ElMessage.error(response.data.message);\n        }\n      } catch (error) {\n        console.error('更新数据失败:', error);\n        ElMessage.error('更新数据失败');\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 分页处理\n    const handleSizeChange = size => {\n      pageSize.value = size;\n      currentPage.value = 1;\n      loadData();\n    };\n    const handleCurrentChange = page => {\n      currentPage.value = page;\n      loadData();\n    };\n\n    // 编辑行\n    const editRow = row => {\n      editingRow.value = row;\n      formData.accession = row.accession;\n      formData.subPopulation = row.subPopulation || '';\n      formData.seqData = row.seqData === '-' ? '' : row.seqData || '';\n      formData.longitude = row.longitude;\n      formData.latitude = row.latitude;\n\n      // 加载文件信息\n      formData.files.genome = row.genomeFile || '';\n      formData.files.annotation = row.annotationFile || '';\n      formData.files.transcriptomeAll = row.transcriptomeAllFile || '';\n      formData.files.transcriptomeLeaf = row.transcriptomeLeafFile || '';\n      formData.files.transcriptomePanicles = row.transcriptomePaniclesFile || '';\n      formData.files.transcriptomeShoot = row.transcriptomeShootFile || '';\n      formData.files.transcriptomeStem = row.transcriptomeStemFile || '';\n      formData.files.transcriptomeRoot = row.transcriptomeRootFile || '';\n      formData.files.codon = row.codonFile || '';\n      formData.files.centromere = row.centromereFile || '';\n      formData.files.TEs = row.tesFile || '';\n      formData.files.coreBlocks = row.coreBlocksFile || '';\n      formData.files.miRNA = row.miRNAFile || '';\n      formData.files.tRNA = row.tRNAFile || '';\n      formData.files.rRNA = row.rRNAFile || '';\n      formData.filesToUpload = {}; // 清空待上传文件\n      showAddDialog.value = true;\n    };\n\n    // 删除行\n    const deleteRow = async row => {\n      try {\n        await ElMessageBox.confirm(`确定要删除 Accession \"${row.accession}\" 吗？这将删除该条目的所有相关数据。`, '确认删除', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        });\n\n        // 调用删除API\n        await axios.delete(`/admin/data-management/accession/${row.accession}/delete/`);\n        ElMessage.success('删除成功');\n        loadData();\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('删除失败:', error);\n          ElMessage.error('删除失败');\n        }\n      }\n    };\n\n    // 保存数据\n    const saveData = async () => {\n      try {\n        await formRef.value.validate();\n        saving.value = true;\n        const data = {\n          accession: formData.accession,\n          subPopulation: formData.subPopulation || '-',\n          seqData: formData.seqData || '-',\n          longitude: formData.longitude,\n          latitude: formData.latitude\n        };\n\n        // 先保存基本信息\n        if (editingRow.value) {\n          // 编辑\n          await axios.put(`/admin/data-management/accession/${editingRow.value.accession}/update/`, data);\n        } else {\n          // 新增\n          await axios.post('/admin/data-management/accession/', data);\n        }\n\n        // 处理文件上传\n        const accession = formData.accession;\n\n        // 文件类型映射：前端字段名 -> API参数\n        const fileTypeMapping = {\n          'genome': 'genome',\n          'annotation': 'annotation',\n          'transcriptomeAll': 'transcriptome.all',\n          'transcriptomeLeaf': 'transcriptome.leaf',\n          'transcriptomePanicles': 'transcriptome.panicles',\n          'transcriptomeShoot': 'transcriptome.shoot',\n          'transcriptomeStem': 'transcriptome.stem',\n          'transcriptomeRoot': 'transcriptome.root',\n          'codon': 'codon',\n          'centromere': 'centromere',\n          'TEs': 'TEs',\n          'coreBlocks': 'coreBlocks',\n          'miRNA': 'miRNA',\n          'tRNA': 'tRNA',\n          'rRNA': 'rRNA'\n        };\n        for (const [frontendFileType, file] of Object.entries(formData.filesToUpload)) {\n          if (file) {\n            try {\n              const apiFileType = fileTypeMapping[frontendFileType] || frontendFileType;\n              const fileFormData = new FormData();\n              fileFormData.append('file', file);\n              fileFormData.append('accession', accession);\n              fileFormData.append('fileType', apiFileType);\n              await axios.post('/admin/data-management/upload-file/', fileFormData, {\n                headers: {\n                  'Content-Type': 'multipart/form-data'\n                }\n              });\n            } catch (fileError) {\n              console.error(`文件 ${frontendFileType} 上传失败:`, fileError);\n              ElMessage.warning(`文件 ${frontendFileType} 上传失败`);\n            }\n          }\n        }\n\n        // 处理文件删除（如果文件名被清空但原来有文件）\n        if (editingRow.value) {\n          const fileTypeMappings = [{\n            formKey: 'genome',\n            apiKey: 'genome',\n            originalKey: 'genomeFile'\n          }, {\n            formKey: 'annotation',\n            apiKey: 'annotation',\n            originalKey: 'annotationFile'\n          }, {\n            formKey: 'transcriptomeAll',\n            apiKey: 'transcriptome.all',\n            originalKey: 'transcriptomeAllFile'\n          }, {\n            formKey: 'transcriptomeLeaf',\n            apiKey: 'transcriptome.leaf',\n            originalKey: 'transcriptomeLeafFile'\n          }, {\n            formKey: 'transcriptomePanicles',\n            apiKey: 'transcriptome.panicles',\n            originalKey: 'transcriptomePaniclesFile'\n          }, {\n            formKey: 'transcriptomeShoot',\n            apiKey: 'transcriptome.shoot',\n            originalKey: 'transcriptomeShootFile'\n          }, {\n            formKey: 'transcriptomeStem',\n            apiKey: 'transcriptome.stem',\n            originalKey: 'transcriptomeStemFile'\n          }, {\n            formKey: 'transcriptomeRoot',\n            apiKey: 'transcriptome.root',\n            originalKey: 'transcriptomeRootFile'\n          }, {\n            formKey: 'codon',\n            apiKey: 'codon',\n            originalKey: 'codonFile'\n          }, {\n            formKey: 'centromere',\n            apiKey: 'centromere',\n            originalKey: 'centromereFile'\n          }, {\n            formKey: 'TEs',\n            apiKey: 'TEs',\n            originalKey: 'tesFile'\n          }, {\n            formKey: 'coreBlocks',\n            apiKey: 'coreBlocks',\n            originalKey: 'coreBlocksFile'\n          }, {\n            formKey: 'miRNA',\n            apiKey: 'miRNA',\n            originalKey: 'miRNAFile'\n          }, {\n            formKey: 'tRNA',\n            apiKey: 'tRNA',\n            originalKey: 'tRNAFile'\n          }, {\n            formKey: 'rRNA',\n            apiKey: 'rRNA',\n            originalKey: 'rRNAFile'\n          }];\n          for (const mapping of fileTypeMappings) {\n            const originalFile = editingRow.value[mapping.originalKey];\n            const currentFile = formData.files[mapping.formKey];\n\n            // 如果原来有文件，现在没有，且没有新上传的文件，则删除\n            if (originalFile && !currentFile && !formData.filesToUpload[mapping.formKey]) {\n              try {\n                await axios.delete(`/admin/data-management/delete-file/${accession}/${mapping.apiKey}/`);\n              } catch (deleteError) {\n                console.error(`文件 ${mapping.apiKey} 删除失败:`, deleteError);\n              }\n            }\n          }\n        }\n        ElMessage.success(editingRow.value ? '更新成功' : '新增成功');\n        showAddDialog.value = false;\n        loadData();\n        loadSubPopulationOptions(); // 重新加载亚群选项\n      } catch (error) {\n        console.error('保存失败:', error);\n        ElMessage.error('保存失败');\n      } finally {\n        saving.value = false;\n      }\n    };\n\n    // 重置表单\n    const resetForm = () => {\n      editingRow.value = null;\n      formData.accession = '';\n      formData.subPopulation = '';\n      formData.seqData = '';\n      formData.longitude = null;\n      formData.latitude = null;\n\n      // 重置文件信息\n      formData.files = {\n        genome: '',\n        annotation: '',\n        transcriptomeAll: '',\n        transcriptomeLeaf: '',\n        transcriptomePanicles: '',\n        transcriptomeShoot: '',\n        transcriptomeStem: '',\n        transcriptomeRoot: '',\n        codon: '',\n        centromere: '',\n        TEs: '',\n        coreBlocks: '',\n        miRNA: '',\n        tRNA: '',\n        rRNA: ''\n      };\n      formData.filesToUpload = {};\n      if (formRef.value) {\n        formRef.value.clearValidate();\n      }\n    };\n\n    // 文件选择方法（用于编辑对话框）\n    const selectFile = fileType => {\n      const input = document.createElement('input');\n      input.type = 'file';\n\n      // 根据文件类型设置接受的文件格式\n      const fileExtensions = {\n        'genome': '.fasta,.fa,.fas',\n        'annotation': '.gff,.gff3',\n        'transcriptomeAll': '.tar.gz',\n        'transcriptomeLeaf': '.tar.gz',\n        'transcriptomePanicles': '.tar.gz',\n        'transcriptomeShoot': '.tar.gz',\n        'transcriptomeStem': '.tar.gz',\n        'transcriptomeRoot': '.tar.gz',\n        'codon': '.tar.gz',\n        'centromere': '.bed',\n        'TEs': '.tar.gz',\n        'coreBlocks': '.bed',\n        'miRNA': '.bed',\n        'tRNA': '.bed',\n        'rRNA': '.bed'\n      };\n      input.accept = fileExtensions[fileType] || '*';\n      input.onchange = event => {\n        const file = event.target.files[0];\n        if (file) {\n          formData.files[fileType] = file.name;\n          formData.filesToUpload[fileType] = file;\n        }\n      };\n      input.click();\n    };\n\n    // 移除文件方法（用于编辑对话框）\n    const removeFile = fileType => {\n      formData.files[fileType] = '';\n      delete formData.filesToUpload[fileType];\n    };\n\n    // 文件操作方法（用于表格中的下载）\n    const uploadFile = (accession, fileType) => {\n      // 创建文件输入元素\n      const input = document.createElement('input');\n      input.type = 'file';\n\n      // 根据文件类型设置接受的文件格式\n      const fileExtensions = {\n        'genome': '.fasta,.fa,.fas',\n        'annotation': '.gff,.gff3',\n        'transcriptome.all': '.tar.gz',\n        'transcriptome.leaf': '.tar.gz',\n        'transcriptome.panicles': '.tar.gz',\n        'transcriptome.shoot': '.tar.gz',\n        'transcriptome.stem': '.tar.gz',\n        'transcriptome.root': '.tar.gz',\n        'codon': '.tar.gz',\n        'centromere': '.bed',\n        'TEs': '.tar.gz',\n        'coreBlocks': '.bed',\n        'miRNA': '.bed',\n        'tRNA': '.bed',\n        'rRNA': '.bed'\n      };\n      input.accept = fileExtensions[fileType] || '*';\n      input.onchange = async event => {\n        const file = event.target.files[0];\n        if (!file) return;\n        try {\n          const formData = new FormData();\n          formData.append('file', file);\n          formData.append('accession', accession);\n          formData.append('fileType', fileType);\n          const response = await axios.post('/admin/data-management/upload-file/', formData, {\n            headers: {\n              'Content-Type': 'multipart/form-data'\n            }\n          });\n          if (response.data.success) {\n            ElMessage.success(`${fileType} 文件上传成功`);\n            loadData(); // 刷新数据\n          } else {\n            ElMessage.error(response.data.message || '上传失败');\n          }\n        } catch (error) {\n          console.error('文件上传失败:', error);\n          ElMessage.error('文件上传失败');\n        }\n      };\n      input.click();\n    };\n    const downloadFile = async (accession, fileType) => {\n      try {\n        const response = await axios.get(`/admin/data-management/download-file/${accession}/${fileType}/`, {\n          responseType: 'blob'\n        });\n\n        // 创建下载链接\n        const url = window.URL.createObjectURL(new Blob([response.data]));\n        const link = document.createElement('a');\n        link.href = url;\n\n        // 根据文件类型设置文件名\n        const extensions = {\n          'genome': 'fasta',\n          'annotation': 'gff',\n          'transcriptome': 'tar.gz',\n          'codon': 'tar.gz',\n          'centromere': 'bed',\n          'TEs': 'tar.gz',\n          'coreBlocks': 'bed',\n          'miRNA': 'bed',\n          'tRNA': 'bed',\n          'rRNA': 'bed'\n        };\n        link.download = `${fileType}.${accession}.${extensions[fileType]}`;\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        window.URL.revokeObjectURL(url);\n      } catch (error) {\n        console.error('文件下载失败:', error);\n        ElMessage.error('文件下载失败');\n      }\n    };\n    const deleteFile = async (accession, fileType) => {\n      try {\n        await ElMessageBox.confirm(`确定要删除 ${accession} 的 ${fileType} 文件吗？`, '确认删除', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        });\n        const response = await axios.delete(`/admin/data-management/delete-file/${accession}/${fileType}/`);\n        if (response.data.success) {\n          ElMessage.success(`${fileType} 文件删除成功`);\n          loadData(); // 刷新数据\n        } else {\n          ElMessage.error(response.data.message || '删除失败');\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('文件删除失败:', error);\n          ElMessage.error('文件删除失败');\n        }\n      }\n    };\n    onMounted(() => {\n      loadData();\n      loadSubPopulationOptions();\n      loadAllAccessions();\n    });\n    return {\n      loading,\n      saving,\n      tableData,\n      currentPage,\n      pageSize,\n      totalCount,\n      showAddDialog,\n      editingRow,\n      formRef,\n      formData,\n      formRules,\n      subPopulationOptions,\n      // 搜索和筛选相关\n      searchAccession,\n      selectedSubPopulations,\n      accessionOptions,\n      loadingAccessions,\n      searchAccessions,\n      handleAccessionChange,\n      handleAccessionClear,\n      handleSubPopulationFilterChange,\n      resetFilters,\n      // 原有方法\n      loadData,\n      refreshData,\n      handleUpdateData,\n      handleSizeChange,\n      handleCurrentChange,\n      editRow,\n      deleteRow,\n      saveData,\n      resetForm,\n      selectFile,\n      removeFile,\n      uploadFile,\n      downloadFile,\n      deleteFile\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "onMounted", "ElMessage", "ElMessageBox", "Plus", "Refresh", "Download", "Upload", "Delete", "Document", "axios", "name", "components", "setup", "loading", "saving", "tableData", "currentPage", "pageSize", "totalCount", "showAddDialog", "editingRow", "formRef", "searchAccession", "selectedSubPopulations", "allAccessionOptions", "loadingAccessions", "subPopulationOptions", "label", "value", "formData", "accession", "subPopulation", "seqData", "longitude", "latitude", "files", "genome", "annotation", "transcriptomeAll", "transcriptomeLeaf", "transcriptomePanicles", "transcriptomeShoot", "transcriptomeStem", "transcriptomeRoot", "codon", "centromere", "TEs", "coreBlocks", "miRNA", "tRNA", "rRNA", "filesToUpload", "formRules", "required", "message", "trigger", "loadSubPopulationOptions", "response", "get", "params", "page", "page_size", "data", "success", "existingSubPopulations", "Set", "for<PERSON>ach", "item", "add", "defaultOptions", "defaultValues", "map", "opt", "additionalOptions", "Array", "from", "filter", "has", "error", "console", "loadData", "search", "length", "sub_populations", "join", "total", "log", "Error", "refreshData", "handleSubPopulationChange", "find", "push", "loadAllAccessions", "sort", "handleAccessionChange", "handleAccessionClear", "handleSubPopulationFilterChange", "resetFilters", "handleUpdateData", "post", "handleSizeChange", "size", "handleCurrentChange", "editRow", "row", "genomeFile", "annotationFile", "transcriptomeAllFile", "transcriptomeLeafFile", "transcriptomePaniclesFile", "transcriptomeShootFile", "transcriptomeStemFile", "transcriptomeRootFile", "codonFile", "centromereFile", "tesFile", "coreBlocksFile", "miRNAFile", "tRNAFile", "rRNAFile", "deleteRow", "confirm", "confirmButtonText", "cancelButtonText", "type", "delete", "saveData", "validate", "put", "fileTypeMapping", "frontendFileType", "file", "Object", "entries", "apiFileType", "fileFormData", "FormData", "append", "headers", "fileError", "warning", "fileTypeMappings", "formKey", "<PERSON><PERSON><PERSON><PERSON>", "original<PERSON>ey", "mapping", "originalFile", "currentFile", "deleteError", "resetForm", "clearValidate", "selectFile", "fileType", "input", "document", "createElement", "fileExtensions", "accept", "onchange", "event", "target", "click", "removeFile", "uploadFile", "downloadFile", "responseType", "url", "window", "URL", "createObjectURL", "Blob", "link", "href", "extensions", "download", "body", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "deleteFile", "accessionOptions", "searchAccessions"], "sources": ["D:\\gene_manage_system\\vue_project\\src\\views\\AdminDataManager.vue"], "sourcesContent": ["<template>\n  <div class=\"admin-data-manager\">\n    <div class=\"page-header\">\n      <h2>数据表格管理</h2>\n      <div class=\"header-actions\">\n        <div class=\"stats-info\">\n          <span>总计: {{ totalCount }} 条记录</span>\n          <span v-if=\"tableData.length > 0\">\n            (当前页: {{ tableData.length }} 条)\n          </span>\n        </div>\n        <el-button type=\"primary\" @click=\"showAddDialog = true\">\n          <el-icon><Plus /></el-icon>\n          新增 Accession\n        </el-button>\n        <el-button @click=\"refreshData\">\n          <el-icon><Refresh /></el-icon>\n          刷新数据\n        </el-button>\n        <el-button type=\"success\" @click=\"handleUpdateData\">\n          <el-icon><Refresh /></el-icon>\n          更新数据\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 搜索和筛选区域 -->\n    <div class=\"filter-section\">\n      <div class=\"filter-row\">\n        <!-- Accession搜索框 -->\n        <div class=\"filter-item\">\n          <label>Accession搜索:</label>\n          <el-select\n            v-model=\"searchAccession\"\n            placeholder=\"请选择Accession\"\n            filterable\n            clearable\n            style=\"width: 250px;\"\n            @change=\"handleAccessionChange\"\n            @clear=\"handleAccessionClear\">\n            <el-option\n              v-for=\"accession in allAccessionOptions\"\n              :key=\"accession\"\n              :label=\"accession\"\n              :value=\"accession\" />\n          </el-select>\n        </div>\n\n        <!-- SubPopulation筛选 -->\n        <div class=\"filter-item\">\n          <label>SubPopulation筛选:</label>\n          <el-select\n            v-model=\"selectedSubPopulations\"\n            placeholder=\"请选择亚群\"\n            multiple\n            collapse-tags\n            collapse-tags-tooltip\n            clearable\n            style=\"width: 300px;\"\n            @change=\"handleSubPopulationFilterChange\">\n            <el-option\n              v-for=\"option in subPopulationOptions\"\n              :key=\"option.value\"\n              :label=\"option.label\"\n              :value=\"option.value\" />\n          </el-select>\n        </div>\n\n        <!-- 重置按钮 -->\n        <div class=\"filter-item\">\n          <el-button @click=\"resetFilters\">\n            <el-icon><Refresh /></el-icon>\n            重置筛选\n          </el-button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 数据表格 -->\n    <el-table\n      :data=\"tableData\"\n      v-loading=\"loading\"\n      border\n      stripe\n      style=\"width: 100%\"\n      :header-cell-style=\"{ background: '#f0f5ff', color: '#1a56db', fontWeight: 'bold' }\">\n      \n      <el-table-column prop=\"accession\" label=\"Accession\" width=\"150\" fixed=\"left\" />\n      <el-table-column prop=\"subPopulation\" label=\"SubPopulation\" width=\"120\" />\n      <el-table-column prop=\"seqData\" label=\"SeqData\" width=\"200\">\n        <template #default=\"scope\">\n          <a v-if=\"scope.row.seqData && scope.row.seqData !== '-'\" \n             :href=\"scope.row.seqData\" \n             target=\"_blank\" \n             class=\"data-link\">\n            {{ scope.row.seqData }}\n          </a>\n          <span v-else class=\"data-empty\">-</span>\n        </template>\n      </el-table-column>\n      <el-table-column prop=\"longitude\" label=\"Longitude\" width=\"100\" />\n      <el-table-column prop=\"latitude\" label=\"Latitude\" width=\"100\" />\n\n      <!-- 文件显示列 -->\n      <el-table-column label=\"Genome\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.genomeFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'genome')\"\n                :title=\"scope.row.genomeFile\">\n            {{ scope.row.genomeFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"Annotation\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.annotationFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'annotation')\"\n                :title=\"scope.row.annotationFile\">\n            {{ scope.row.annotationFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"Transcriptome.all\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.transcriptomeAllFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'transcriptome.all')\"\n                :title=\"scope.row.transcriptomeAllFile\">\n            {{ scope.row.transcriptomeAllFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"Transcriptome.leaf\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.transcriptomeLeafFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'transcriptome.leaf')\"\n                :title=\"scope.row.transcriptomeLeafFile\">\n            {{ scope.row.transcriptomeLeafFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"Transcriptome.panicles\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.transcriptomePaniclesFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'transcriptome.panicles')\"\n                :title=\"scope.row.transcriptomePaniclesFile\">\n            {{ scope.row.transcriptomePaniclesFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"Transcriptome.shoot\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.transcriptomeShootFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'transcriptome.shoot')\"\n                :title=\"scope.row.transcriptomeShootFile\">\n            {{ scope.row.transcriptomeShootFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"Transcriptome.stem\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.transcriptomeStemFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'transcriptome.stem')\"\n                :title=\"scope.row.transcriptomeStemFile\">\n            {{ scope.row.transcriptomeStemFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"Transcriptome.root\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.transcriptomeRootFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'transcriptome.root')\"\n                :title=\"scope.row.transcriptomeRootFile\">\n            {{ scope.row.transcriptomeRootFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"Codon\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.codonFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'codon')\"\n                :title=\"scope.row.codonFile\">\n            {{ scope.row.codonFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"Centromere\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.centromereFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'centromere')\"\n                :title=\"scope.row.centromereFile\">\n            {{ scope.row.centromereFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"TEs\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.tesFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'TEs')\"\n                :title=\"scope.row.tesFile\">\n            {{ scope.row.tesFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"CoreBlocks\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.coreBlocksFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'coreBlocks')\"\n                :title=\"scope.row.coreBlocksFile\">\n            {{ scope.row.coreBlocksFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"miRNA\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.miRNAFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'miRNA')\"\n                :title=\"scope.row.miRNAFile\">\n            {{ scope.row.miRNAFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"tRNA\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.tRNAFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'tRNA')\"\n                :title=\"scope.row.tRNAFile\">\n            {{ scope.row.tRNAFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"rRNA\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.rRNAFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'rRNA')\"\n                :title=\"scope.row.rRNAFile\">\n            {{ scope.row.rRNAFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"操作\" width=\"150\" fixed=\"right\">\n        <template #default=\"scope\">\n          <el-button type=\"primary\" size=\"small\" @click=\"editRow(scope.row)\">编辑</el-button>\n          <el-button type=\"danger\" size=\"small\" @click=\"deleteRow(scope.row)\">删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- 分页 -->\n    <div class=\"pagination-container\">\n      <el-pagination\n        v-model:current-page=\"currentPage\"\n        v-model:page-size=\"pageSize\"\n        :page-sizes=\"[20, 50, 100]\"\n        :total=\"totalCount\"\n        layout=\"total, sizes, prev, pager, next, jumper\"\n        @size-change=\"handleSizeChange\"\n        @current-change=\"handleCurrentChange\"\n      />\n    </div>\n\n    <!-- 新增/编辑对话框 -->\n    <el-dialog\n      v-model=\"showAddDialog\"\n      :title=\"editingRow ? '编辑 Accession' : '新增 Accession'\"\n      width=\"600px\"\n      @close=\"resetForm\">\n      \n      <el-form :model=\"formData\" :rules=\"formRules\" ref=\"formRef\" label-width=\"120px\">\n        <el-form-item label=\"Accession\" prop=\"accession\">\n          <el-input v-model=\"formData.accession\" :disabled=\"editingRow\" />\n        </el-form-item>\n        \n        <el-form-item label=\"SubPopulation\" prop=\"subPopulation\">\n          <el-select\n            v-model=\"formData.subPopulation\"\n            placeholder=\"请选择或输入亚群\"\n            filterable\n            allow-create\n            default-first-option\n            :reserve-keyword=\"false\"\n            @change=\"handleSubPopulationChange\">\n            <el-option\n              v-for=\"option in subPopulationOptions\"\n              :key=\"option.value\"\n              :label=\"option.label\"\n              :value=\"option.value\" />\n          </el-select>\n        </el-form-item>\n        \n        <el-form-item label=\"SeqData URL\" prop=\"seqData\">\n          <el-input v-model=\"formData.seqData\" placeholder=\"请输入SeqData链接，留空则为 -\" />\n        </el-form-item>\n        \n        <el-form-item label=\"经度\" prop=\"longitude\">\n          <el-input-number v-model=\"formData.longitude\" :precision=\"2\" placeholder=\"经度\" />\n        </el-form-item>\n        \n        <el-form-item label=\"纬度\" prop=\"latitude\">\n          <el-input-number v-model=\"formData.latitude\" :precision=\"2\" placeholder=\"纬度\" />\n        </el-form-item>\n\n        <!-- 文件管理部分 -->\n        <el-divider content-position=\"left\">文件管理</el-divider>\n\n        <div class=\"file-management-grid\">\n          <!-- Genome -->\n          <div class=\"file-item\">\n            <label>Genome:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.genome\" class=\"current-file\">{{ formData.files.genome }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('genome')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.genome\" size=\"small\" type=\"danger\" @click=\"removeFile('genome')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- Annotation -->\n          <div class=\"file-item\">\n            <label>Annotation:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.annotation\" class=\"current-file\">{{ formData.files.annotation }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('annotation')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.annotation\" size=\"small\" type=\"danger\" @click=\"removeFile('annotation')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- Transcriptome.all -->\n          <div class=\"file-item\">\n            <label>Transcriptome.all:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.transcriptomeAll\" class=\"current-file\">{{ formData.files.transcriptomeAll }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('transcriptomeAll')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.transcriptomeAll\" size=\"small\" type=\"danger\" @click=\"removeFile('transcriptomeAll')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- Transcriptome.leaf -->\n          <div class=\"file-item\">\n            <label>Transcriptome.leaf:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.transcriptomeLeaf\" class=\"current-file\">{{ formData.files.transcriptomeLeaf }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('transcriptomeLeaf')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.transcriptomeLeaf\" size=\"small\" type=\"danger\" @click=\"removeFile('transcriptomeLeaf')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- Transcriptome.panicles -->\n          <div class=\"file-item\">\n            <label>Transcriptome.panicles:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.transcriptomePanicles\" class=\"current-file\">{{ formData.files.transcriptomePanicles }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('transcriptomePanicles')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.transcriptomePanicles\" size=\"small\" type=\"danger\" @click=\"removeFile('transcriptomePanicles')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- Transcriptome.shoot -->\n          <div class=\"file-item\">\n            <label>Transcriptome.shoot:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.transcriptomeShoot\" class=\"current-file\">{{ formData.files.transcriptomeShoot }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('transcriptomeShoot')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.transcriptomeShoot\" size=\"small\" type=\"danger\" @click=\"removeFile('transcriptomeShoot')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- Transcriptome.stem -->\n          <div class=\"file-item\">\n            <label>Transcriptome.stem:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.transcriptomeStem\" class=\"current-file\">{{ formData.files.transcriptomeStem }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('transcriptomeStem')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.transcriptomeStem\" size=\"small\" type=\"danger\" @click=\"removeFile('transcriptomeStem')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- Transcriptome.root -->\n          <div class=\"file-item\">\n            <label>Transcriptome.root:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.transcriptomeRoot\" class=\"current-file\">{{ formData.files.transcriptomeRoot }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('transcriptomeRoot')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.transcriptomeRoot\" size=\"small\" type=\"danger\" @click=\"removeFile('transcriptomeRoot')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- Codon -->\n          <div class=\"file-item\">\n            <label>Codon:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.codon\" class=\"current-file\">{{ formData.files.codon }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('codon')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.codon\" size=\"small\" type=\"danger\" @click=\"removeFile('codon')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- Centromere -->\n          <div class=\"file-item\">\n            <label>Centromere:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.centromere\" class=\"current-file\">{{ formData.files.centromere }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('centromere')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.centromere\" size=\"small\" type=\"danger\" @click=\"removeFile('centromere')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- TEs -->\n          <div class=\"file-item\">\n            <label>TEs:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.TEs\" class=\"current-file\">{{ formData.files.TEs }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('TEs')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.TEs\" size=\"small\" type=\"danger\" @click=\"removeFile('TEs')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- CoreBlocks -->\n          <div class=\"file-item\">\n            <label>CoreBlocks:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.coreBlocks\" class=\"current-file\">{{ formData.files.coreBlocks }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('coreBlocks')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.coreBlocks\" size=\"small\" type=\"danger\" @click=\"removeFile('coreBlocks')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- miRNA -->\n          <div class=\"file-item\">\n            <label>miRNA:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.miRNA\" class=\"current-file\">{{ formData.files.miRNA }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('miRNA')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.miRNA\" size=\"small\" type=\"danger\" @click=\"removeFile('miRNA')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- tRNA -->\n          <div class=\"file-item\">\n            <label>tRNA:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.tRNA\" class=\"current-file\">{{ formData.files.tRNA }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('tRNA')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.tRNA\" size=\"small\" type=\"danger\" @click=\"removeFile('tRNA')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- rRNA -->\n          <div class=\"file-item\">\n            <label>rRNA:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.rRNA\" class=\"current-file\">{{ formData.files.rRNA }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('rRNA')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.rRNA\" size=\"small\" type=\"danger\" @click=\"removeFile('rRNA')\">删除</el-button>\n            </div>\n          </div>\n        </div>\n      </el-form>\n      \n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"showAddDialog = false\">取消</el-button>\n          <el-button type=\"primary\" @click=\"saveData\" :loading=\"saving\">保存</el-button>\n        </span>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, onMounted } from 'vue'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport { Plus, Refresh, Download, Upload, Delete, Document } from '@element-plus/icons-vue'\nimport axios from 'axios'\n\nexport default {\n  name: 'AdminDataManager',\n  components: {\n    Plus,\n    Refresh,\n    Download,\n    Upload,\n    Delete,\n    Document\n  },\n  setup() {\n    const loading = ref(false)\n    const saving = ref(false)\n    const tableData = ref([])\n    const currentPage = ref(1)\n    const pageSize = ref(20)\n    const totalCount = ref(0)\n\n    const showAddDialog = ref(false)\n    const editingRow = ref(null)\n    const formRef = ref(null)\n\n    // 搜索和筛选相关\n    const searchAccession = ref('')\n    const selectedSubPopulations = ref([])\n    const allAccessionOptions = ref([])\n    const loadingAccessions = ref(false)\n\n    const subPopulationOptions = ref([\n      { label: 'cA', value: 'cA' },\n      { label: 'cB', value: 'cB' },\n      { label: 'GJ', value: 'GJ' },\n      { label: 'XI', value: 'XI' },\n      { label: 'WILD', value: 'WILD' },\n      { label: 'O.glaberrima', value: 'O.glaberrima' },\n      { label: '未知', value: '-' }\n    ])\n    \n    const formData = reactive({\n      accession: '',\n      subPopulation: '',\n      seqData: '',\n      longitude: null,\n      latitude: null,\n      files: {\n        genome: '',\n        annotation: '',\n        transcriptomeAll: '',\n        transcriptomeLeaf: '',\n        transcriptomePanicles: '',\n        transcriptomeShoot: '',\n        transcriptomeStem: '',\n        transcriptomeRoot: '',\n        codon: '',\n        centromere: '',\n        TEs: '',\n        coreBlocks: '',\n        miRNA: '',\n        tRNA: '',\n        rRNA: ''\n      },\n      filesToUpload: {} // 存储待上传的文件\n    })\n    \n    const formRules = {\n      accession: [\n        { required: true, message: '请输入Accession', trigger: 'blur' }\n      ]\n    }\n\n    // 加载亚群选项\n    const loadSubPopulationOptions = async () => {\n      try {\n        const response = await axios.get('/admin/data-management/list/', {\n          params: { page: 1, page_size: 1000 } // 获取所有数据来提取亚群\n        })\n\n        if (response.data.success && response.data.data) {\n          // 提取所有唯一的亚群\n          const existingSubPopulations = new Set()\n          response.data.data.forEach(item => {\n            if (item.subPopulation && item.subPopulation !== '-') {\n              existingSubPopulations.add(item.subPopulation)\n            }\n          })\n\n          // 合并默认选项和已存在的亚群\n          const defaultOptions = [\n            { label: 'cA', value: 'cA' },\n            { label: 'cB', value: 'cB' },\n            { label: 'GJ', value: 'GJ' },\n            { label: 'XI', value: 'XI' },\n            { label: 'WILD', value: 'WILD' },\n            { label: 'O.glaberrima', value: 'O.glaberrima' },\n            { label: '未知', value: '-' }\n          ]\n\n          const defaultValues = new Set(defaultOptions.map(opt => opt.value))\n          const additionalOptions = Array.from(existingSubPopulations)\n            .filter(value => !defaultValues.has(value))\n            .map(value => ({ label: value, value: value }))\n\n          subPopulationOptions.value = [...defaultOptions, ...additionalOptions]\n        }\n      } catch (error) {\n        console.error('加载亚群选项失败:', error)\n      }\n    }\n\n    // 加载数据\n    const loadData = async () => {\n      try {\n        loading.value = true\n        const params = {\n          page: currentPage.value,\n          page_size: pageSize.value\n        }\n\n        // 添加搜索参数\n        if (searchAccession.value) {\n          params.search = searchAccession.value\n        }\n\n        // 添加亚群筛选参数\n        if (selectedSubPopulations.value.length > 0) {\n          params.sub_populations = selectedSubPopulations.value.join(',')\n        }\n\n        const response = await axios.get('/admin/data-management/list/', { params })\n        const data = response.data\n\n        if (data.success) {\n          tableData.value = data.data || []\n          totalCount.value = data.total || 0\n\n          // 调试信息：检查第一条记录的文件状态\n          if (data.data && data.data.length > 0) {\n            console.log('第一条记录的文件状态:', data.data[0])\n          }\n        } else {\n          throw new Error(data.message || '获取数据失败')\n        }\n\n      } catch (error) {\n        console.error('加载数据失败:', error)\n        ElMessage.error('加载数据失败')\n      } finally {\n        loading.value = false\n      }\n    }\n\n    // 刷新数据\n    const refreshData = () => {\n      loadData()\n    }\n\n    // 处理亚群变化（表单中的）\n    const handleSubPopulationChange = (value) => {\n      // 如果是新输入的亚群，添加到选项列表中\n      if (value && !subPopulationOptions.value.find(opt => opt.value === value)) {\n        subPopulationOptions.value.push({\n          label: value,\n          value: value\n        })\n      }\n    }\n\n    // 加载所有Accession选项\n    const loadAllAccessions = async () => {\n      try {\n        loadingAccessions.value = true\n        const response = await axios.get('/admin/data-management/list/', {\n          params: {\n            page: 1,\n            page_size: 1000  // 获取所有数据\n          }\n        })\n\n        if (response.data.success) {\n          allAccessionOptions.value = response.data.data.map(item => item.accession).sort()\n        }\n      } catch (error) {\n        console.error('加载Accession选项失败:', error)\n      } finally {\n        loadingAccessions.value = false\n      }\n    }\n\n    // 处理Accession选择变化\n    const handleAccessionChange = (value) => {\n      searchAccession.value = value\n      currentPage.value = 1\n      loadData()\n    }\n\n    // 清除Accession搜索\n    const handleAccessionClear = () => {\n      searchAccession.value = ''\n      currentPage.value = 1\n      loadData()\n    }\n\n    // 处理SubPopulation筛选变化\n    const handleSubPopulationFilterChange = () => {\n      currentPage.value = 1\n      loadData()\n    }\n\n    // 重置筛选\n    const resetFilters = () => {\n      searchAccession.value = ''\n      selectedSubPopulations.value = []\n      currentPage.value = 1\n      loadData()\n    }\n\n    // 更新数据（重新扫描文件）\n    const handleUpdateData = async () => {\n      try {\n        loading.value = true\n        const response = await axios.post('/admin/rescan/')\n\n        if (response.data.success) {\n          ElMessage.success(response.data.message)\n          loadData()\n        } else {\n          ElMessage.error(response.data.message)\n        }\n      } catch (error) {\n        console.error('更新数据失败:', error)\n        ElMessage.error('更新数据失败')\n      } finally {\n        loading.value = false\n      }\n    }\n\n    // 分页处理\n    const handleSizeChange = (size) => {\n      pageSize.value = size\n      currentPage.value = 1\n      loadData()\n    }\n\n    const handleCurrentChange = (page) => {\n      currentPage.value = page\n      loadData()\n    }\n\n    // 编辑行\n    const editRow = (row) => {\n      editingRow.value = row\n      formData.accession = row.accession\n      formData.subPopulation = row.subPopulation || ''\n      formData.seqData = row.seqData === '-' ? '' : (row.seqData || '')\n      formData.longitude = row.longitude\n      formData.latitude = row.latitude\n\n      // 加载文件信息\n      formData.files.genome = row.genomeFile || ''\n      formData.files.annotation = row.annotationFile || ''\n      formData.files.transcriptomeAll = row.transcriptomeAllFile || ''\n      formData.files.transcriptomeLeaf = row.transcriptomeLeafFile || ''\n      formData.files.transcriptomePanicles = row.transcriptomePaniclesFile || ''\n      formData.files.transcriptomeShoot = row.transcriptomeShootFile || ''\n      formData.files.transcriptomeStem = row.transcriptomeStemFile || ''\n      formData.files.transcriptomeRoot = row.transcriptomeRootFile || ''\n      formData.files.codon = row.codonFile || ''\n      formData.files.centromere = row.centromereFile || ''\n      formData.files.TEs = row.tesFile || ''\n      formData.files.coreBlocks = row.coreBlocksFile || ''\n      formData.files.miRNA = row.miRNAFile || ''\n      formData.files.tRNA = row.tRNAFile || ''\n      formData.files.rRNA = row.rRNAFile || ''\n\n      formData.filesToUpload = {} // 清空待上传文件\n      showAddDialog.value = true\n    }\n\n    // 删除行\n    const deleteRow = async (row) => {\n      try {\n        await ElMessageBox.confirm(\n          `确定要删除 Accession \"${row.accession}\" 吗？这将删除该条目的所有相关数据。`,\n          '确认删除',\n          {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }\n        )\n        \n        // 调用删除API\n        await axios.delete(`/admin/data-management/accession/${row.accession}/delete/`)\n        \n        ElMessage.success('删除成功')\n        loadData()\n        \n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('删除失败:', error)\n          ElMessage.error('删除失败')\n        }\n      }\n    }\n\n    // 保存数据\n    const saveData = async () => {\n      try {\n        await formRef.value.validate()\n\n        saving.value = true\n\n        const data = {\n          accession: formData.accession,\n          subPopulation: formData.subPopulation || '-',\n          seqData: formData.seqData || '-',\n          longitude: formData.longitude,\n          latitude: formData.latitude\n        }\n\n        // 先保存基本信息\n        if (editingRow.value) {\n          // 编辑\n          await axios.put(`/admin/data-management/accession/${editingRow.value.accession}/update/`, data)\n        } else {\n          // 新增\n          await axios.post('/admin/data-management/accession/', data)\n        }\n\n        // 处理文件上传\n        const accession = formData.accession\n\n        // 文件类型映射：前端字段名 -> API参数\n        const fileTypeMapping = {\n          'genome': 'genome',\n          'annotation': 'annotation',\n          'transcriptomeAll': 'transcriptome.all',\n          'transcriptomeLeaf': 'transcriptome.leaf',\n          'transcriptomePanicles': 'transcriptome.panicles',\n          'transcriptomeShoot': 'transcriptome.shoot',\n          'transcriptomeStem': 'transcriptome.stem',\n          'transcriptomeRoot': 'transcriptome.root',\n          'codon': 'codon',\n          'centromere': 'centromere',\n          'TEs': 'TEs',\n          'coreBlocks': 'coreBlocks',\n          'miRNA': 'miRNA',\n          'tRNA': 'tRNA',\n          'rRNA': 'rRNA'\n        }\n\n        for (const [frontendFileType, file] of Object.entries(formData.filesToUpload)) {\n          if (file) {\n            try {\n              const apiFileType = fileTypeMapping[frontendFileType] || frontendFileType\n              const fileFormData = new FormData()\n              fileFormData.append('file', file)\n              fileFormData.append('accession', accession)\n              fileFormData.append('fileType', apiFileType)\n\n              await axios.post('/admin/data-management/upload-file/', fileFormData, {\n                headers: {\n                  'Content-Type': 'multipart/form-data'\n                }\n              })\n            } catch (fileError) {\n              console.error(`文件 ${frontendFileType} 上传失败:`, fileError)\n              ElMessage.warning(`文件 ${frontendFileType} 上传失败`)\n            }\n          }\n        }\n\n        // 处理文件删除（如果文件名被清空但原来有文件）\n        if (editingRow.value) {\n          const fileTypeMappings = [\n            { formKey: 'genome', apiKey: 'genome', originalKey: 'genomeFile' },\n            { formKey: 'annotation', apiKey: 'annotation', originalKey: 'annotationFile' },\n            { formKey: 'transcriptomeAll', apiKey: 'transcriptome.all', originalKey: 'transcriptomeAllFile' },\n            { formKey: 'transcriptomeLeaf', apiKey: 'transcriptome.leaf', originalKey: 'transcriptomeLeafFile' },\n            { formKey: 'transcriptomePanicles', apiKey: 'transcriptome.panicles', originalKey: 'transcriptomePaniclesFile' },\n            { formKey: 'transcriptomeShoot', apiKey: 'transcriptome.shoot', originalKey: 'transcriptomeShootFile' },\n            { formKey: 'transcriptomeStem', apiKey: 'transcriptome.stem', originalKey: 'transcriptomeStemFile' },\n            { formKey: 'transcriptomeRoot', apiKey: 'transcriptome.root', originalKey: 'transcriptomeRootFile' },\n            { formKey: 'codon', apiKey: 'codon', originalKey: 'codonFile' },\n            { formKey: 'centromere', apiKey: 'centromere', originalKey: 'centromereFile' },\n            { formKey: 'TEs', apiKey: 'TEs', originalKey: 'tesFile' },\n            { formKey: 'coreBlocks', apiKey: 'coreBlocks', originalKey: 'coreBlocksFile' },\n            { formKey: 'miRNA', apiKey: 'miRNA', originalKey: 'miRNAFile' },\n            { formKey: 'tRNA', apiKey: 'tRNA', originalKey: 'tRNAFile' },\n            { formKey: 'rRNA', apiKey: 'rRNA', originalKey: 'rRNAFile' }\n          ]\n\n          for (const mapping of fileTypeMappings) {\n            const originalFile = editingRow.value[mapping.originalKey]\n            const currentFile = formData.files[mapping.formKey]\n\n            // 如果原来有文件，现在没有，且没有新上传的文件，则删除\n            if (originalFile && !currentFile && !formData.filesToUpload[mapping.formKey]) {\n              try {\n                await axios.delete(`/admin/data-management/delete-file/${accession}/${mapping.apiKey}/`)\n              } catch (deleteError) {\n                console.error(`文件 ${mapping.apiKey} 删除失败:`, deleteError)\n              }\n            }\n          }\n        }\n\n        ElMessage.success(editingRow.value ? '更新成功' : '新增成功')\n        showAddDialog.value = false\n        loadData()\n        loadSubPopulationOptions() // 重新加载亚群选项\n\n      } catch (error) {\n        console.error('保存失败:', error)\n        ElMessage.error('保存失败')\n      } finally {\n        saving.value = false\n      }\n    }\n\n    // 重置表单\n    const resetForm = () => {\n      editingRow.value = null\n      formData.accession = ''\n      formData.subPopulation = ''\n      formData.seqData = ''\n      formData.longitude = null\n      formData.latitude = null\n\n      // 重置文件信息\n      formData.files = {\n        genome: '',\n        annotation: '',\n        transcriptomeAll: '',\n        transcriptomeLeaf: '',\n        transcriptomePanicles: '',\n        transcriptomeShoot: '',\n        transcriptomeStem: '',\n        transcriptomeRoot: '',\n        codon: '',\n        centromere: '',\n        TEs: '',\n        coreBlocks: '',\n        miRNA: '',\n        tRNA: '',\n        rRNA: ''\n      }\n      formData.filesToUpload = {}\n\n      if (formRef.value) {\n        formRef.value.clearValidate()\n      }\n    }\n\n    // 文件选择方法（用于编辑对话框）\n    const selectFile = (fileType) => {\n      const input = document.createElement('input')\n      input.type = 'file'\n\n      // 根据文件类型设置接受的文件格式\n      const fileExtensions = {\n        'genome': '.fasta,.fa,.fas',\n        'annotation': '.gff,.gff3',\n        'transcriptomeAll': '.tar.gz',\n        'transcriptomeLeaf': '.tar.gz',\n        'transcriptomePanicles': '.tar.gz',\n        'transcriptomeShoot': '.tar.gz',\n        'transcriptomeStem': '.tar.gz',\n        'transcriptomeRoot': '.tar.gz',\n        'codon': '.tar.gz',\n        'centromere': '.bed',\n        'TEs': '.tar.gz',\n        'coreBlocks': '.bed',\n        'miRNA': '.bed',\n        'tRNA': '.bed',\n        'rRNA': '.bed'\n      }\n\n      input.accept = fileExtensions[fileType] || '*'\n\n      input.onchange = (event) => {\n        const file = event.target.files[0]\n        if (file) {\n          formData.files[fileType] = file.name\n          formData.filesToUpload[fileType] = file\n        }\n      }\n\n      input.click()\n    }\n\n    // 移除文件方法（用于编辑对话框）\n    const removeFile = (fileType) => {\n      formData.files[fileType] = ''\n      delete formData.filesToUpload[fileType]\n    }\n\n    // 文件操作方法（用于表格中的下载）\n    const uploadFile = (accession, fileType) => {\n      // 创建文件输入元素\n      const input = document.createElement('input')\n      input.type = 'file'\n\n      // 根据文件类型设置接受的文件格式\n      const fileExtensions = {\n        'genome': '.fasta,.fa,.fas',\n        'annotation': '.gff,.gff3',\n        'transcriptome.all': '.tar.gz',\n        'transcriptome.leaf': '.tar.gz',\n        'transcriptome.panicles': '.tar.gz',\n        'transcriptome.shoot': '.tar.gz',\n        'transcriptome.stem': '.tar.gz',\n        'transcriptome.root': '.tar.gz',\n        'codon': '.tar.gz',\n        'centromere': '.bed',\n        'TEs': '.tar.gz',\n        'coreBlocks': '.bed',\n        'miRNA': '.bed',\n        'tRNA': '.bed',\n        'rRNA': '.bed'\n      }\n\n      input.accept = fileExtensions[fileType] || '*'\n\n      input.onchange = async (event) => {\n        const file = event.target.files[0]\n        if (!file) return\n\n        try {\n          const formData = new FormData()\n          formData.append('file', file)\n          formData.append('accession', accession)\n          formData.append('fileType', fileType)\n\n          const response = await axios.post('/admin/data-management/upload-file/', formData, {\n            headers: {\n              'Content-Type': 'multipart/form-data'\n            }\n          })\n\n          if (response.data.success) {\n            ElMessage.success(`${fileType} 文件上传成功`)\n            loadData() // 刷新数据\n          } else {\n            ElMessage.error(response.data.message || '上传失败')\n          }\n        } catch (error) {\n          console.error('文件上传失败:', error)\n          ElMessage.error('文件上传失败')\n        }\n      }\n\n      input.click()\n    }\n\n    const downloadFile = async (accession, fileType) => {\n      try {\n        const response = await axios.get(`/admin/data-management/download-file/${accession}/${fileType}/`, {\n          responseType: 'blob'\n        })\n\n        // 创建下载链接\n        const url = window.URL.createObjectURL(new Blob([response.data]))\n        const link = document.createElement('a')\n        link.href = url\n\n        // 根据文件类型设置文件名\n        const extensions = {\n          'genome': 'fasta',\n          'annotation': 'gff',\n          'transcriptome': 'tar.gz',\n          'codon': 'tar.gz',\n          'centromere': 'bed',\n          'TEs': 'tar.gz',\n          'coreBlocks': 'bed',\n          'miRNA': 'bed',\n          'tRNA': 'bed',\n          'rRNA': 'bed'\n        }\n\n        link.download = `${fileType}.${accession}.${extensions[fileType]}`\n        document.body.appendChild(link)\n        link.click()\n        document.body.removeChild(link)\n        window.URL.revokeObjectURL(url)\n\n      } catch (error) {\n        console.error('文件下载失败:', error)\n        ElMessage.error('文件下载失败')\n      }\n    }\n\n    const deleteFile = async (accession, fileType) => {\n      try {\n        await ElMessageBox.confirm(\n          `确定要删除 ${accession} 的 ${fileType} 文件吗？`,\n          '确认删除',\n          {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }\n        )\n\n        const response = await axios.delete(`/admin/data-management/delete-file/${accession}/${fileType}/`)\n\n        if (response.data.success) {\n          ElMessage.success(`${fileType} 文件删除成功`)\n          loadData() // 刷新数据\n        } else {\n          ElMessage.error(response.data.message || '删除失败')\n        }\n\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('文件删除失败:', error)\n          ElMessage.error('文件删除失败')\n        }\n      }\n    }\n\n    onMounted(() => {\n      loadData()\n      loadSubPopulationOptions()\n      loadAllAccessions()\n    })\n\n    return {\n      loading,\n      saving,\n      tableData,\n      currentPage,\n      pageSize,\n      totalCount,\n      showAddDialog,\n      editingRow,\n      formRef,\n      formData,\n      formRules,\n      subPopulationOptions,\n      // 搜索和筛选相关\n      searchAccession,\n      selectedSubPopulations,\n      accessionOptions,\n      loadingAccessions,\n      searchAccessions,\n      handleAccessionChange,\n      handleAccessionClear,\n      handleSubPopulationFilterChange,\n      resetFilters,\n      // 原有方法\n      loadData,\n      refreshData,\n      handleUpdateData,\n      handleSizeChange,\n      handleCurrentChange,\n      editRow,\n      deleteRow,\n      saveData,\n      resetForm,\n      selectFile,\n      removeFile,\n      uploadFile,\n      downloadFile,\n      deleteFile\n    }\n  }\n}\n</script>\n\n<style scoped>\n.admin-data-manager {\n  padding: 20px;\n}\n\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.page-header h2 {\n  margin: 0;\n  color: #1a56db;\n}\n\n.header-actions {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.stats-info {\n  color: #666;\n  font-size: 14px;\n}\n\n.stats-info span {\n  margin-right: 10px;\n}\n\n/* 筛选区域样式 */\n.filter-section {\n  background: #f8f9fa;\n  padding: 20px;\n  border-radius: 8px;\n  margin-bottom: 20px;\n  border: 1px solid #e9ecef;\n}\n\n.filter-row {\n  display: flex;\n  align-items: center;\n  gap: 20px;\n  flex-wrap: wrap;\n}\n\n.filter-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.filter-item label {\n  font-weight: 500;\n  color: #495057;\n  white-space: nowrap;\n  min-width: fit-content;\n}\n\n.file-link {\n  color: #1a56db;\n  cursor: pointer;\n  text-decoration: none;\n  word-break: break-all;\n  font-size: 12px;\n}\n\n.file-link:hover {\n  text-decoration: underline;\n}\n\n.file-management-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 15px;\n  margin-top: 10px;\n}\n\n.file-item {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.file-item label {\n  font-weight: bold;\n  color: #333;\n  font-size: 14px;\n}\n\n.file-controls {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  flex-wrap: wrap;\n}\n\n.current-file {\n  color: #67c23a;\n  font-size: 12px;\n  word-break: break-all;\n  flex: 1;\n  min-width: 0;\n}\n\n.no-file {\n  color: #999;\n  font-size: 12px;\n}\n\n.data-link {\n  color: #1a56db;\n  text-decoration: none;\n  word-break: break-all;\n}\n\n.data-link:hover {\n  text-decoration: underline;\n}\n\n.data-empty {\n  color: #999;\n}\n\n.pagination-container {\n  margin-top: 20px;\n  display: flex;\n  justify-content: center;\n}\n\n.dialog-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 10px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;AAghBA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAQ,QAAS,KAAI;AAC7C,SAASC,SAAS,EAAEC,YAAW,QAAS,cAAa;AACrD,SAASC,IAAI,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,MAAM,EAAEC,QAAO,QAAS,yBAAwB;AAC1F,OAAOC,KAAI,MAAO,OAAM;AAExB,eAAe;EACbC,IAAI,EAAE,kBAAkB;EACxBC,UAAU,EAAE;IACVR,IAAI;IACJC,OAAO;IACPC,QAAQ;IACRC,MAAM;IACNC,MAAM;IACNC;EACF,CAAC;EACDI,KAAKA,CAAA,EAAG;IACN,MAAMC,OAAM,GAAIf,GAAG,CAAC,KAAK;IACzB,MAAMgB,MAAK,GAAIhB,GAAG,CAAC,KAAK;IACxB,MAAMiB,SAAQ,GAAIjB,GAAG,CAAC,EAAE;IACxB,MAAMkB,WAAU,GAAIlB,GAAG,CAAC,CAAC;IACzB,MAAMmB,QAAO,GAAInB,GAAG,CAAC,EAAE;IACvB,MAAMoB,UAAS,GAAIpB,GAAG,CAAC,CAAC;IAExB,MAAMqB,aAAY,GAAIrB,GAAG,CAAC,KAAK;IAC/B,MAAMsB,UAAS,GAAItB,GAAG,CAAC,IAAI;IAC3B,MAAMuB,OAAM,GAAIvB,GAAG,CAAC,IAAI;;IAExB;IACA,MAAMwB,eAAc,GAAIxB,GAAG,CAAC,EAAE;IAC9B,MAAMyB,sBAAqB,GAAIzB,GAAG,CAAC,EAAE;IACrC,MAAM0B,mBAAkB,GAAI1B,GAAG,CAAC,EAAE;IAClC,MAAM2B,iBAAgB,GAAI3B,GAAG,CAAC,KAAK;IAEnC,MAAM4B,oBAAmB,GAAI5B,GAAG,CAAC,CAC/B;MAAE6B,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAK,CAAC,EAC5B;MAAED,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAK,CAAC,EAC5B;MAAED,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAK,CAAC,EAC5B;MAAED,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAK,CAAC,EAC5B;MAAED,KAAK,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAO,CAAC,EAChC;MAAED,KAAK,EAAE,cAAc;MAAEC,KAAK,EAAE;IAAe,CAAC,EAChD;MAAED,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAI,EAC3B;IAED,MAAMC,QAAO,GAAI9B,QAAQ,CAAC;MACxB+B,SAAS,EAAE,EAAE;MACbC,aAAa,EAAE,EAAE;MACjBC,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE,IAAI;MACfC,QAAQ,EAAE,IAAI;MACdC,KAAK,EAAE;QACLC,MAAM,EAAE,EAAE;QACVC,UAAU,EAAE,EAAE;QACdC,gBAAgB,EAAE,EAAE;QACpBC,iBAAiB,EAAE,EAAE;QACrBC,qBAAqB,EAAE,EAAE;QACzBC,kBAAkB,EAAE,EAAE;QACtBC,iBAAiB,EAAE,EAAE;QACrBC,iBAAiB,EAAE,EAAE;QACrBC,KAAK,EAAE,EAAE;QACTC,UAAU,EAAE,EAAE;QACdC,GAAG,EAAE,EAAE;QACPC,UAAU,EAAE,EAAE;QACdC,KAAK,EAAE,EAAE;QACTC,IAAI,EAAE,EAAE;QACRC,IAAI,EAAE;MACR,CAAC;MACDC,aAAa,EAAE,CAAC,EAAE;IACpB,CAAC;IAED,MAAMC,SAAQ,GAAI;MAChBtB,SAAS,EAAE,CACT;QAAEuB,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,cAAc;QAAEC,OAAO,EAAE;MAAO;IAE/D;;IAEA;IACA,MAAMC,wBAAuB,GAAI,MAAAA,CAAA,KAAY;MAC3C,IAAI;QACF,MAAMC,QAAO,GAAI,MAAMhD,KAAK,CAACiD,GAAG,CAAC,8BAA8B,EAAE;UAC/DC,MAAM,EAAE;YAAEC,IAAI,EAAE,CAAC;YAAEC,SAAS,EAAE;UAAK,EAAE;QACvC,CAAC;QAED,IAAIJ,QAAQ,CAACK,IAAI,CAACC,OAAM,IAAKN,QAAQ,CAACK,IAAI,CAACA,IAAI,EAAE;UAC/C;UACA,MAAME,sBAAqB,GAAI,IAAIC,GAAG,CAAC;UACvCR,QAAQ,CAACK,IAAI,CAACA,IAAI,CAACI,OAAO,CAACC,IAAG,IAAK;YACjC,IAAIA,IAAI,CAACpC,aAAY,IAAKoC,IAAI,CAACpC,aAAY,KAAM,GAAG,EAAE;cACpDiC,sBAAsB,CAACI,GAAG,CAACD,IAAI,CAACpC,aAAa;YAC/C;UACF,CAAC;;UAED;UACA,MAAMsC,cAAa,GAAI,CACrB;YAAE1C,KAAK,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAK,CAAC,EAC5B;YAAED,KAAK,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAK,CAAC,EAC5B;YAAED,KAAK,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAK,CAAC,EAC5B;YAAED,KAAK,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAK,CAAC,EAC5B;YAAED,KAAK,EAAE,MAAM;YAAEC,KAAK,EAAE;UAAO,CAAC,EAChC;YAAED,KAAK,EAAE,cAAc;YAAEC,KAAK,EAAE;UAAe,CAAC,EAChD;YAAED,KAAK,EAAE,IAAI;YAAEC,KAAK,EAAE;UAAI,EAC5B;UAEA,MAAM0C,aAAY,GAAI,IAAIL,GAAG,CAACI,cAAc,CAACE,GAAG,CAACC,GAAE,IAAKA,GAAG,CAAC5C,KAAK,CAAC;UAClE,MAAM6C,iBAAgB,GAAIC,KAAK,CAACC,IAAI,CAACX,sBAAsB,EACxDY,MAAM,CAAChD,KAAI,IAAK,CAAC0C,aAAa,CAACO,GAAG,CAACjD,KAAK,CAAC,EACzC2C,GAAG,CAAC3C,KAAI,KAAM;YAAED,KAAK,EAAEC,KAAK;YAAEA,KAAK,EAAEA;UAAM,CAAC,CAAC;UAEhDF,oBAAoB,CAACE,KAAI,GAAI,CAAC,GAAGyC,cAAc,EAAE,GAAGI,iBAAiB;QACvE;MACF,EAAE,OAAOK,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;MAClC;IACF;;IAEA;IACA,MAAME,QAAO,GAAI,MAAAA,CAAA,KAAY;MAC3B,IAAI;QACFnE,OAAO,CAACe,KAAI,GAAI,IAAG;QACnB,MAAM+B,MAAK,GAAI;UACbC,IAAI,EAAE5C,WAAW,CAACY,KAAK;UACvBiC,SAAS,EAAE5C,QAAQ,CAACW;QACtB;;QAEA;QACA,IAAIN,eAAe,CAACM,KAAK,EAAE;UACzB+B,MAAM,CAACsB,MAAK,GAAI3D,eAAe,CAACM,KAAI;QACtC;;QAEA;QACA,IAAIL,sBAAsB,CAACK,KAAK,CAACsD,MAAK,GAAI,CAAC,EAAE;UAC3CvB,MAAM,CAACwB,eAAc,GAAI5D,sBAAsB,CAACK,KAAK,CAACwD,IAAI,CAAC,GAAG;QAChE;QAEA,MAAM3B,QAAO,GAAI,MAAMhD,KAAK,CAACiD,GAAG,CAAC,8BAA8B,EAAE;UAAEC;QAAO,CAAC;QAC3E,MAAMG,IAAG,GAAIL,QAAQ,CAACK,IAAG;QAEzB,IAAIA,IAAI,CAACC,OAAO,EAAE;UAChBhD,SAAS,CAACa,KAAI,GAAIkC,IAAI,CAACA,IAAG,IAAK,EAAC;UAChC5C,UAAU,CAACU,KAAI,GAAIkC,IAAI,CAACuB,KAAI,IAAK;;UAEjC;UACA,IAAIvB,IAAI,CAACA,IAAG,IAAKA,IAAI,CAACA,IAAI,CAACoB,MAAK,GAAI,CAAC,EAAE;YACrCH,OAAO,CAACO,GAAG,CAAC,aAAa,EAAExB,IAAI,CAACA,IAAI,CAAC,CAAC,CAAC;UACzC;QACF,OAAO;UACL,MAAM,IAAIyB,KAAK,CAACzB,IAAI,CAACR,OAAM,IAAK,QAAQ;QAC1C;MAEF,EAAE,OAAOwB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK;QAC9B7E,SAAS,CAAC6E,KAAK,CAAC,QAAQ;MAC1B,UAAU;QACRjE,OAAO,CAACe,KAAI,GAAI,KAAI;MACtB;IACF;;IAEA;IACA,MAAM4D,WAAU,GAAIA,CAAA,KAAM;MACxBR,QAAQ,CAAC;IACX;;IAEA;IACA,MAAMS,yBAAwB,GAAK7D,KAAK,IAAK;MAC3C;MACA,IAAIA,KAAI,IAAK,CAACF,oBAAoB,CAACE,KAAK,CAAC8D,IAAI,CAAClB,GAAE,IAAKA,GAAG,CAAC5C,KAAI,KAAMA,KAAK,CAAC,EAAE;QACzEF,oBAAoB,CAACE,KAAK,CAAC+D,IAAI,CAAC;UAC9BhE,KAAK,EAAEC,KAAK;UACZA,KAAK,EAAEA;QACT,CAAC;MACH;IACF;;IAEA;IACA,MAAMgE,iBAAgB,GAAI,MAAAA,CAAA,KAAY;MACpC,IAAI;QACFnE,iBAAiB,CAACG,KAAI,GAAI,IAAG;QAC7B,MAAM6B,QAAO,GAAI,MAAMhD,KAAK,CAACiD,GAAG,CAAC,8BAA8B,EAAE;UAC/DC,MAAM,EAAE;YACNC,IAAI,EAAE,CAAC;YACPC,SAAS,EAAE,IAAG,CAAG;UACnB;QACF,CAAC;QAED,IAAIJ,QAAQ,CAACK,IAAI,CAACC,OAAO,EAAE;UACzBvC,mBAAmB,CAACI,KAAI,GAAI6B,QAAQ,CAACK,IAAI,CAACA,IAAI,CAACS,GAAG,CAACJ,IAAG,IAAKA,IAAI,CAACrC,SAAS,CAAC,CAAC+D,IAAI,CAAC;QAClF;MACF,EAAE,OAAOf,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,kBAAkB,EAAEA,KAAK;MACzC,UAAU;QACRrD,iBAAiB,CAACG,KAAI,GAAI,KAAI;MAChC;IACF;;IAEA;IACA,MAAMkE,qBAAoB,GAAKlE,KAAK,IAAK;MACvCN,eAAe,CAACM,KAAI,GAAIA,KAAI;MAC5BZ,WAAW,CAACY,KAAI,GAAI;MACpBoD,QAAQ,CAAC;IACX;;IAEA;IACA,MAAMe,oBAAmB,GAAIA,CAAA,KAAM;MACjCzE,eAAe,CAACM,KAAI,GAAI,EAAC;MACzBZ,WAAW,CAACY,KAAI,GAAI;MACpBoD,QAAQ,CAAC;IACX;;IAEA;IACA,MAAMgB,+BAA8B,GAAIA,CAAA,KAAM;MAC5ChF,WAAW,CAACY,KAAI,GAAI;MACpBoD,QAAQ,CAAC;IACX;;IAEA;IACA,MAAMiB,YAAW,GAAIA,CAAA,KAAM;MACzB3E,eAAe,CAACM,KAAI,GAAI,EAAC;MACzBL,sBAAsB,CAACK,KAAI,GAAI,EAAC;MAChCZ,WAAW,CAACY,KAAI,GAAI;MACpBoD,QAAQ,CAAC;IACX;;IAEA;IACA,MAAMkB,gBAAe,GAAI,MAAAA,CAAA,KAAY;MACnC,IAAI;QACFrF,OAAO,CAACe,KAAI,GAAI,IAAG;QACnB,MAAM6B,QAAO,GAAI,MAAMhD,KAAK,CAAC0F,IAAI,CAAC,gBAAgB;QAElD,IAAI1C,QAAQ,CAACK,IAAI,CAACC,OAAO,EAAE;UACzB9D,SAAS,CAAC8D,OAAO,CAACN,QAAQ,CAACK,IAAI,CAACR,OAAO;UACvC0B,QAAQ,CAAC;QACX,OAAO;UACL/E,SAAS,CAAC6E,KAAK,CAACrB,QAAQ,CAACK,IAAI,CAACR,OAAO;QACvC;MACF,EAAE,OAAOwB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK;QAC9B7E,SAAS,CAAC6E,KAAK,CAAC,QAAQ;MAC1B,UAAU;QACRjE,OAAO,CAACe,KAAI,GAAI,KAAI;MACtB;IACF;;IAEA;IACA,MAAMwE,gBAAe,GAAKC,IAAI,IAAK;MACjCpF,QAAQ,CAACW,KAAI,GAAIyE,IAAG;MACpBrF,WAAW,CAACY,KAAI,GAAI;MACpBoD,QAAQ,CAAC;IACX;IAEA,MAAMsB,mBAAkB,GAAK1C,IAAI,IAAK;MACpC5C,WAAW,CAACY,KAAI,GAAIgC,IAAG;MACvBoB,QAAQ,CAAC;IACX;;IAEA;IACA,MAAMuB,OAAM,GAAKC,GAAG,IAAK;MACvBpF,UAAU,CAACQ,KAAI,GAAI4E,GAAE;MACrB3E,QAAQ,CAACC,SAAQ,GAAI0E,GAAG,CAAC1E,SAAQ;MACjCD,QAAQ,CAACE,aAAY,GAAIyE,GAAG,CAACzE,aAAY,IAAK,EAAC;MAC/CF,QAAQ,CAACG,OAAM,GAAIwE,GAAG,CAACxE,OAAM,KAAM,GAAE,GAAI,EAAC,GAAKwE,GAAG,CAACxE,OAAM,IAAK,EAAE;MAChEH,QAAQ,CAACI,SAAQ,GAAIuE,GAAG,CAACvE,SAAQ;MACjCJ,QAAQ,CAACK,QAAO,GAAIsE,GAAG,CAACtE,QAAO;;MAE/B;MACAL,QAAQ,CAACM,KAAK,CAACC,MAAK,GAAIoE,GAAG,CAACC,UAAS,IAAK,EAAC;MAC3C5E,QAAQ,CAACM,KAAK,CAACE,UAAS,GAAImE,GAAG,CAACE,cAAa,IAAK,EAAC;MACnD7E,QAAQ,CAACM,KAAK,CAACG,gBAAe,GAAIkE,GAAG,CAACG,oBAAmB,IAAK,EAAC;MAC/D9E,QAAQ,CAACM,KAAK,CAACI,iBAAgB,GAAIiE,GAAG,CAACI,qBAAoB,IAAK,EAAC;MACjE/E,QAAQ,CAACM,KAAK,CAACK,qBAAoB,GAAIgE,GAAG,CAACK,yBAAwB,IAAK,EAAC;MACzEhF,QAAQ,CAACM,KAAK,CAACM,kBAAiB,GAAI+D,GAAG,CAACM,sBAAqB,IAAK,EAAC;MACnEjF,QAAQ,CAACM,KAAK,CAACO,iBAAgB,GAAI8D,GAAG,CAACO,qBAAoB,IAAK,EAAC;MACjElF,QAAQ,CAACM,KAAK,CAACQ,iBAAgB,GAAI6D,GAAG,CAACQ,qBAAoB,IAAK,EAAC;MACjEnF,QAAQ,CAACM,KAAK,CAACS,KAAI,GAAI4D,GAAG,CAACS,SAAQ,IAAK,EAAC;MACzCpF,QAAQ,CAACM,KAAK,CAACU,UAAS,GAAI2D,GAAG,CAACU,cAAa,IAAK,EAAC;MACnDrF,QAAQ,CAACM,KAAK,CAACW,GAAE,GAAI0D,GAAG,CAACW,OAAM,IAAK,EAAC;MACrCtF,QAAQ,CAACM,KAAK,CAACY,UAAS,GAAIyD,GAAG,CAACY,cAAa,IAAK,EAAC;MACnDvF,QAAQ,CAACM,KAAK,CAACa,KAAI,GAAIwD,GAAG,CAACa,SAAQ,IAAK,EAAC;MACzCxF,QAAQ,CAACM,KAAK,CAACc,IAAG,GAAIuD,GAAG,CAACc,QAAO,IAAK,EAAC;MACvCzF,QAAQ,CAACM,KAAK,CAACe,IAAG,GAAIsD,GAAG,CAACe,QAAO,IAAK,EAAC;MAEvC1F,QAAQ,CAACsB,aAAY,GAAI,CAAC,GAAE;MAC5BhC,aAAa,CAACS,KAAI,GAAI,IAAG;IAC3B;;IAEA;IACA,MAAM4F,SAAQ,GAAI,MAAOhB,GAAG,IAAK;MAC/B,IAAI;QACF,MAAMtG,YAAY,CAACuH,OAAO,CACxB,oBAAoBjB,GAAG,CAAC1E,SAAS,qBAAqB,EACtD,MAAM,EACN;UACE4F,iBAAiB,EAAE,IAAI;UACvBC,gBAAgB,EAAE,IAAI;UACtBC,IAAI,EAAE;QACR,CACF;;QAEA;QACA,MAAMnH,KAAK,CAACoH,MAAM,CAAC,oCAAoCrB,GAAG,CAAC1E,SAAS,UAAU;QAE9E7B,SAAS,CAAC8D,OAAO,CAAC,MAAM;QACxBiB,QAAQ,CAAC;MAEX,EAAE,OAAOF,KAAK,EAAE;QACd,IAAIA,KAAI,KAAM,QAAQ,EAAE;UACtBC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK;UAC5B7E,SAAS,CAAC6E,KAAK,CAAC,MAAM;QACxB;MACF;IACF;;IAEA;IACA,MAAMgD,QAAO,GAAI,MAAAA,CAAA,KAAY;MAC3B,IAAI;QACF,MAAMzG,OAAO,CAACO,KAAK,CAACmG,QAAQ,CAAC;QAE7BjH,MAAM,CAACc,KAAI,GAAI,IAAG;QAElB,MAAMkC,IAAG,GAAI;UACXhC,SAAS,EAAED,QAAQ,CAACC,SAAS;UAC7BC,aAAa,EAAEF,QAAQ,CAACE,aAAY,IAAK,GAAG;UAC5CC,OAAO,EAAEH,QAAQ,CAACG,OAAM,IAAK,GAAG;UAChCC,SAAS,EAAEJ,QAAQ,CAACI,SAAS;UAC7BC,QAAQ,EAAEL,QAAQ,CAACK;QACrB;;QAEA;QACA,IAAId,UAAU,CAACQ,KAAK,EAAE;UACpB;UACA,MAAMnB,KAAK,CAACuH,GAAG,CAAC,oCAAoC5G,UAAU,CAACQ,KAAK,CAACE,SAAS,UAAU,EAAEgC,IAAI;QAChG,OAAO;UACL;UACA,MAAMrD,KAAK,CAAC0F,IAAI,CAAC,mCAAmC,EAAErC,IAAI;QAC5D;;QAEA;QACA,MAAMhC,SAAQ,GAAID,QAAQ,CAACC,SAAQ;;QAEnC;QACA,MAAMmG,eAAc,GAAI;UACtB,QAAQ,EAAE,QAAQ;UAClB,YAAY,EAAE,YAAY;UAC1B,kBAAkB,EAAE,mBAAmB;UACvC,mBAAmB,EAAE,oBAAoB;UACzC,uBAAuB,EAAE,wBAAwB;UACjD,oBAAoB,EAAE,qBAAqB;UAC3C,mBAAmB,EAAE,oBAAoB;UACzC,mBAAmB,EAAE,oBAAoB;UACzC,OAAO,EAAE,OAAO;UAChB,YAAY,EAAE,YAAY;UAC1B,KAAK,EAAE,KAAK;UACZ,YAAY,EAAE,YAAY;UAC1B,OAAO,EAAE,OAAO;UAChB,MAAM,EAAE,MAAM;UACd,MAAM,EAAE;QACV;QAEA,KAAK,MAAM,CAACC,gBAAgB,EAAEC,IAAI,KAAKC,MAAM,CAACC,OAAO,CAACxG,QAAQ,CAACsB,aAAa,CAAC,EAAE;UAC7E,IAAIgF,IAAI,EAAE;YACR,IAAI;cACF,MAAMG,WAAU,GAAIL,eAAe,CAACC,gBAAgB,KAAKA,gBAAe;cACxE,MAAMK,YAAW,GAAI,IAAIC,QAAQ,CAAC;cAClCD,YAAY,CAACE,MAAM,CAAC,MAAM,EAAEN,IAAI;cAChCI,YAAY,CAACE,MAAM,CAAC,WAAW,EAAE3G,SAAS;cAC1CyG,YAAY,CAACE,MAAM,CAAC,UAAU,EAAEH,WAAW;cAE3C,MAAM7H,KAAK,CAAC0F,IAAI,CAAC,qCAAqC,EAAEoC,YAAY,EAAE;gBACpEG,OAAO,EAAE;kBACP,cAAc,EAAE;gBAClB;cACF,CAAC;YACH,EAAE,OAAOC,SAAS,EAAE;cAClB5D,OAAO,CAACD,KAAK,CAAC,MAAMoD,gBAAgB,QAAQ,EAAES,SAAS;cACvD1I,SAAS,CAAC2I,OAAO,CAAC,MAAMV,gBAAgB,OAAO;YACjD;UACF;QACF;;QAEA;QACA,IAAI9G,UAAU,CAACQ,KAAK,EAAE;UACpB,MAAMiH,gBAAe,GAAI,CACvB;YAAEC,OAAO,EAAE,QAAQ;YAAEC,MAAM,EAAE,QAAQ;YAAEC,WAAW,EAAE;UAAa,CAAC,EAClE;YAAEF,OAAO,EAAE,YAAY;YAAEC,MAAM,EAAE,YAAY;YAAEC,WAAW,EAAE;UAAiB,CAAC,EAC9E;YAAEF,OAAO,EAAE,kBAAkB;YAAEC,MAAM,EAAE,mBAAmB;YAAEC,WAAW,EAAE;UAAuB,CAAC,EACjG;YAAEF,OAAO,EAAE,mBAAmB;YAAEC,MAAM,EAAE,oBAAoB;YAAEC,WAAW,EAAE;UAAwB,CAAC,EACpG;YAAEF,OAAO,EAAE,uBAAuB;YAAEC,MAAM,EAAE,wBAAwB;YAAEC,WAAW,EAAE;UAA4B,CAAC,EAChH;YAAEF,OAAO,EAAE,oBAAoB;YAAEC,MAAM,EAAE,qBAAqB;YAAEC,WAAW,EAAE;UAAyB,CAAC,EACvG;YAAEF,OAAO,EAAE,mBAAmB;YAAEC,MAAM,EAAE,oBAAoB;YAAEC,WAAW,EAAE;UAAwB,CAAC,EACpG;YAAEF,OAAO,EAAE,mBAAmB;YAAEC,MAAM,EAAE,oBAAoB;YAAEC,WAAW,EAAE;UAAwB,CAAC,EACpG;YAAEF,OAAO,EAAE,OAAO;YAAEC,MAAM,EAAE,OAAO;YAAEC,WAAW,EAAE;UAAY,CAAC,EAC/D;YAAEF,OAAO,EAAE,YAAY;YAAEC,MAAM,EAAE,YAAY;YAAEC,WAAW,EAAE;UAAiB,CAAC,EAC9E;YAAEF,OAAO,EAAE,KAAK;YAAEC,MAAM,EAAE,KAAK;YAAEC,WAAW,EAAE;UAAU,CAAC,EACzD;YAAEF,OAAO,EAAE,YAAY;YAAEC,MAAM,EAAE,YAAY;YAAEC,WAAW,EAAE;UAAiB,CAAC,EAC9E;YAAEF,OAAO,EAAE,OAAO;YAAEC,MAAM,EAAE,OAAO;YAAEC,WAAW,EAAE;UAAY,CAAC,EAC/D;YAAEF,OAAO,EAAE,MAAM;YAAEC,MAAM,EAAE,MAAM;YAAEC,WAAW,EAAE;UAAW,CAAC,EAC5D;YAAEF,OAAO,EAAE,MAAM;YAAEC,MAAM,EAAE,MAAM;YAAEC,WAAW,EAAE;UAAW,EAC7D;UAEA,KAAK,MAAMC,OAAM,IAAKJ,gBAAgB,EAAE;YACtC,MAAMK,YAAW,GAAI9H,UAAU,CAACQ,KAAK,CAACqH,OAAO,CAACD,WAAW;YACzD,MAAMG,WAAU,GAAItH,QAAQ,CAACM,KAAK,CAAC8G,OAAO,CAACH,OAAO;;YAElD;YACA,IAAII,YAAW,IAAK,CAACC,WAAU,IAAK,CAACtH,QAAQ,CAACsB,aAAa,CAAC8F,OAAO,CAACH,OAAO,CAAC,EAAE;cAC5E,IAAI;gBACF,MAAMrI,KAAK,CAACoH,MAAM,CAAC,sCAAsC/F,SAAS,IAAImH,OAAO,CAACF,MAAM,GAAG;cACzF,EAAE,OAAOK,WAAW,EAAE;gBACpBrE,OAAO,CAACD,KAAK,CAAC,MAAMmE,OAAO,CAACF,MAAM,QAAQ,EAAEK,WAAW;cACzD;YACF;UACF;QACF;QAEAnJ,SAAS,CAAC8D,OAAO,CAAC3C,UAAU,CAACQ,KAAI,GAAI,MAAK,GAAI,MAAM;QACpDT,aAAa,CAACS,KAAI,GAAI,KAAI;QAC1BoD,QAAQ,CAAC;QACTxB,wBAAwB,CAAC,GAAE;MAE7B,EAAE,OAAOsB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK;QAC5B7E,SAAS,CAAC6E,KAAK,CAAC,MAAM;MACxB,UAAU;QACRhE,MAAM,CAACc,KAAI,GAAI,KAAI;MACrB;IACF;;IAEA;IACA,MAAMyH,SAAQ,GAAIA,CAAA,KAAM;MACtBjI,UAAU,CAACQ,KAAI,GAAI,IAAG;MACtBC,QAAQ,CAACC,SAAQ,GAAI,EAAC;MACtBD,QAAQ,CAACE,aAAY,GAAI,EAAC;MAC1BF,QAAQ,CAACG,OAAM,GAAI,EAAC;MACpBH,QAAQ,CAACI,SAAQ,GAAI,IAAG;MACxBJ,QAAQ,CAACK,QAAO,GAAI,IAAG;;MAEvB;MACAL,QAAQ,CAACM,KAAI,GAAI;QACfC,MAAM,EAAE,EAAE;QACVC,UAAU,EAAE,EAAE;QACdC,gBAAgB,EAAE,EAAE;QACpBC,iBAAiB,EAAE,EAAE;QACrBC,qBAAqB,EAAE,EAAE;QACzBC,kBAAkB,EAAE,EAAE;QACtBC,iBAAiB,EAAE,EAAE;QACrBC,iBAAiB,EAAE,EAAE;QACrBC,KAAK,EAAE,EAAE;QACTC,UAAU,EAAE,EAAE;QACdC,GAAG,EAAE,EAAE;QACPC,UAAU,EAAE,EAAE;QACdC,KAAK,EAAE,EAAE;QACTC,IAAI,EAAE,EAAE;QACRC,IAAI,EAAE;MACR;MACArB,QAAQ,CAACsB,aAAY,GAAI,CAAC;MAE1B,IAAI9B,OAAO,CAACO,KAAK,EAAE;QACjBP,OAAO,CAACO,KAAK,CAAC0H,aAAa,CAAC;MAC9B;IACF;;IAEA;IACA,MAAMC,UAAS,GAAKC,QAAQ,IAAK;MAC/B,MAAMC,KAAI,GAAIC,QAAQ,CAACC,aAAa,CAAC,OAAO;MAC5CF,KAAK,CAAC7B,IAAG,GAAI,MAAK;;MAElB;MACA,MAAMgC,cAAa,GAAI;QACrB,QAAQ,EAAE,iBAAiB;QAC3B,YAAY,EAAE,YAAY;QAC1B,kBAAkB,EAAE,SAAS;QAC7B,mBAAmB,EAAE,SAAS;QAC9B,uBAAuB,EAAE,SAAS;QAClC,oBAAoB,EAAE,SAAS;QAC/B,mBAAmB,EAAE,SAAS;QAC9B,mBAAmB,EAAE,SAAS;QAC9B,OAAO,EAAE,SAAS;QAClB,YAAY,EAAE,MAAM;QACpB,KAAK,EAAE,SAAS;QAChB,YAAY,EAAE,MAAM;QACpB,OAAO,EAAE,MAAM;QACf,MAAM,EAAE,MAAM;QACd,MAAM,EAAE;MACV;MAEAH,KAAK,CAACI,MAAK,GAAID,cAAc,CAACJ,QAAQ,KAAK,GAAE;MAE7CC,KAAK,CAACK,QAAO,GAAKC,KAAK,IAAK;QAC1B,MAAM5B,IAAG,GAAI4B,KAAK,CAACC,MAAM,CAAC7H,KAAK,CAAC,CAAC;QACjC,IAAIgG,IAAI,EAAE;UACRtG,QAAQ,CAACM,KAAK,CAACqH,QAAQ,IAAIrB,IAAI,CAACzH,IAAG;UACnCmB,QAAQ,CAACsB,aAAa,CAACqG,QAAQ,IAAIrB,IAAG;QACxC;MACF;MAEAsB,KAAK,CAACQ,KAAK,CAAC;IACd;;IAEA;IACA,MAAMC,UAAS,GAAKV,QAAQ,IAAK;MAC/B3H,QAAQ,CAACM,KAAK,CAACqH,QAAQ,IAAI,EAAC;MAC5B,OAAO3H,QAAQ,CAACsB,aAAa,CAACqG,QAAQ;IACxC;;IAEA;IACA,MAAMW,UAAS,GAAIA,CAACrI,SAAS,EAAE0H,QAAQ,KAAK;MAC1C;MACA,MAAMC,KAAI,GAAIC,QAAQ,CAACC,aAAa,CAAC,OAAO;MAC5CF,KAAK,CAAC7B,IAAG,GAAI,MAAK;;MAElB;MACA,MAAMgC,cAAa,GAAI;QACrB,QAAQ,EAAE,iBAAiB;QAC3B,YAAY,EAAE,YAAY;QAC1B,mBAAmB,EAAE,SAAS;QAC9B,oBAAoB,EAAE,SAAS;QAC/B,wBAAwB,EAAE,SAAS;QACnC,qBAAqB,EAAE,SAAS;QAChC,oBAAoB,EAAE,SAAS;QAC/B,oBAAoB,EAAE,SAAS;QAC/B,OAAO,EAAE,SAAS;QAClB,YAAY,EAAE,MAAM;QACpB,KAAK,EAAE,SAAS;QAChB,YAAY,EAAE,MAAM;QACpB,OAAO,EAAE,MAAM;QACf,MAAM,EAAE,MAAM;QACd,MAAM,EAAE;MACV;MAEAH,KAAK,CAACI,MAAK,GAAID,cAAc,CAACJ,QAAQ,KAAK,GAAE;MAE7CC,KAAK,CAACK,QAAO,GAAI,MAAOC,KAAK,IAAK;QAChC,MAAM5B,IAAG,GAAI4B,KAAK,CAACC,MAAM,CAAC7H,KAAK,CAAC,CAAC;QACjC,IAAI,CAACgG,IAAI,EAAE;QAEX,IAAI;UACF,MAAMtG,QAAO,GAAI,IAAI2G,QAAQ,CAAC;UAC9B3G,QAAQ,CAAC4G,MAAM,CAAC,MAAM,EAAEN,IAAI;UAC5BtG,QAAQ,CAAC4G,MAAM,CAAC,WAAW,EAAE3G,SAAS;UACtCD,QAAQ,CAAC4G,MAAM,CAAC,UAAU,EAAEe,QAAQ;UAEpC,MAAM/F,QAAO,GAAI,MAAMhD,KAAK,CAAC0F,IAAI,CAAC,qCAAqC,EAAEtE,QAAQ,EAAE;YACjF6G,OAAO,EAAE;cACP,cAAc,EAAE;YAClB;UACF,CAAC;UAED,IAAIjF,QAAQ,CAACK,IAAI,CAACC,OAAO,EAAE;YACzB9D,SAAS,CAAC8D,OAAO,CAAC,GAAGyF,QAAQ,SAAS;YACtCxE,QAAQ,CAAC,GAAE;UACb,OAAO;YACL/E,SAAS,CAAC6E,KAAK,CAACrB,QAAQ,CAACK,IAAI,CAACR,OAAM,IAAK,MAAM;UACjD;QACF,EAAE,OAAOwB,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK;UAC9B7E,SAAS,CAAC6E,KAAK,CAAC,QAAQ;QAC1B;MACF;MAEA2E,KAAK,CAACQ,KAAK,CAAC;IACd;IAEA,MAAMG,YAAW,GAAI,MAAAA,CAAOtI,SAAS,EAAE0H,QAAQ,KAAK;MAClD,IAAI;QACF,MAAM/F,QAAO,GAAI,MAAMhD,KAAK,CAACiD,GAAG,CAAC,wCAAwC5B,SAAS,IAAI0H,QAAQ,GAAG,EAAE;UACjGa,YAAY,EAAE;QAChB,CAAC;;QAED;QACA,MAAMC,GAAE,GAAIC,MAAM,CAACC,GAAG,CAACC,eAAe,CAAC,IAAIC,IAAI,CAAC,CAACjH,QAAQ,CAACK,IAAI,CAAC,CAAC;QAChE,MAAM6G,IAAG,GAAIjB,QAAQ,CAACC,aAAa,CAAC,GAAG;QACvCgB,IAAI,CAACC,IAAG,GAAIN,GAAE;;QAEd;QACA,MAAMO,UAAS,GAAI;UACjB,QAAQ,EAAE,OAAO;UACjB,YAAY,EAAE,KAAK;UACnB,eAAe,EAAE,QAAQ;UACzB,OAAO,EAAE,QAAQ;UACjB,YAAY,EAAE,KAAK;UACnB,KAAK,EAAE,QAAQ;UACf,YAAY,EAAE,KAAK;UACnB,OAAO,EAAE,KAAK;UACd,MAAM,EAAE,KAAK;UACb,MAAM,EAAE;QACV;QAEAF,IAAI,CAACG,QAAO,GAAI,GAAGtB,QAAQ,IAAI1H,SAAS,IAAI+I,UAAU,CAACrB,QAAQ,CAAC,EAAC;QACjEE,QAAQ,CAACqB,IAAI,CAACC,WAAW,CAACL,IAAI;QAC9BA,IAAI,CAACV,KAAK,CAAC;QACXP,QAAQ,CAACqB,IAAI,CAACE,WAAW,CAACN,IAAI;QAC9BJ,MAAM,CAACC,GAAG,CAACU,eAAe,CAACZ,GAAG;MAEhC,EAAE,OAAOxF,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK;QAC9B7E,SAAS,CAAC6E,KAAK,CAAC,QAAQ;MAC1B;IACF;IAEA,MAAMqG,UAAS,GAAI,MAAAA,CAAOrJ,SAAS,EAAE0H,QAAQ,KAAK;MAChD,IAAI;QACF,MAAMtJ,YAAY,CAACuH,OAAO,CACxB,SAAS3F,SAAS,MAAM0H,QAAQ,OAAO,EACvC,MAAM,EACN;UACE9B,iBAAiB,EAAE,IAAI;UACvBC,gBAAgB,EAAE,IAAI;UACtBC,IAAI,EAAE;QACR,CACF;QAEA,MAAMnE,QAAO,GAAI,MAAMhD,KAAK,CAACoH,MAAM,CAAC,sCAAsC/F,SAAS,IAAI0H,QAAQ,GAAG;QAElG,IAAI/F,QAAQ,CAACK,IAAI,CAACC,OAAO,EAAE;UACzB9D,SAAS,CAAC8D,OAAO,CAAC,GAAGyF,QAAQ,SAAS;UACtCxE,QAAQ,CAAC,GAAE;QACb,OAAO;UACL/E,SAAS,CAAC6E,KAAK,CAACrB,QAAQ,CAACK,IAAI,CAACR,OAAM,IAAK,MAAM;QACjD;MAEF,EAAE,OAAOwB,KAAK,EAAE;QACd,IAAIA,KAAI,KAAM,QAAQ,EAAE;UACtBC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK;UAC9B7E,SAAS,CAAC6E,KAAK,CAAC,QAAQ;QAC1B;MACF;IACF;IAEA9E,SAAS,CAAC,MAAM;MACdgF,QAAQ,CAAC;MACTxB,wBAAwB,CAAC;MACzBoC,iBAAiB,CAAC;IACpB,CAAC;IAED,OAAO;MACL/E,OAAO;MACPC,MAAM;MACNC,SAAS;MACTC,WAAW;MACXC,QAAQ;MACRC,UAAU;MACVC,aAAa;MACbC,UAAU;MACVC,OAAO;MACPQ,QAAQ;MACRuB,SAAS;MACT1B,oBAAoB;MACpB;MACAJ,eAAe;MACfC,sBAAsB;MACtB6J,gBAAgB;MAChB3J,iBAAiB;MACjB4J,gBAAgB;MAChBvF,qBAAqB;MACrBC,oBAAoB;MACpBC,+BAA+B;MAC/BC,YAAY;MACZ;MACAjB,QAAQ;MACRQ,WAAW;MACXU,gBAAgB;MAChBE,gBAAgB;MAChBE,mBAAmB;MACnBC,OAAO;MACPiB,SAAS;MACTM,QAAQ;MACRuB,SAAS;MACTE,UAAU;MACVW,UAAU;MACVC,UAAU;MACVC,YAAY;MACZe;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}